/*
 * @Description: API for BiShareSDK
 * @Autor: wangr
 * @Date: 2023-04-19 09:17:13
 * @LastEditors: wangr
 * @LastEditTime: 2023-05-26 16:44:46
 */
#pragma once
#ifndef _BISHARESDK_BISHARE_SERVICE_H
#define _BISHARESDK_BISHARE_SERVICE_H

#include "bishare-define.h"

#ifdef __cplusplus
extern "C" {
#endif

#if !defined(PTW32_STATIC_LIB)
#  if defined(PTW32_BUILD)
#    define PTW32_DLLPORT __declspec (dllexport)
#  else
#    define PTW32_DLLPORT
#  endif
#else
#  define PTW32_DLLPORT
#endif
/**
 * @brief: 初始化
 * @param {bool_type_t} is_console 是否输出打印
 * @param {bool_type_t} is_file 是否保存日志文件
 * @param {char*} file_path 日志文件路径
 * @param {log_priority_t} priority 日志优先级
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_init(bool_type_t is_console, bool_type_t is_file, const char* file_path, log_priority_t priority);
/**
 * @brief: 释放
 * @return {bstatus_t} result

 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_release();
/**
 * @brief: 主动发送设备发现请求
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_discovery_device();
/**
 * @brief: 清除已发现的设备列表
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_clear_discovery_device();
/**
 * @brief: 获取设备列表，列表内容由回调函数进行返回
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_get_discovery_device();
/**
 * @brief: 连接远程控制通道
 * @param {char*} remote_host 远端IP地址
 * @param {unsigned int} remote_port 远端端口号
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_connect_remote_control(const char* remote_host, unsigned int remote_port);
/**
 * @brief: 
 * @param {int} session
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_disconnect_remote_control(int session);
/**
 * @brief: 
 * @param {char*} remote_host
 * @param {unsigned int} remote_port
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_connect_remote_stream(const char* remote_host, unsigned int remote_port);
/**
 * @brief: 
 * @param {long} tms
 * @param {int} type
 * @param {void*} data
 * @param {int} size
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_send_remote_stream(long tms, int type, const void* data, int size);
/**
 * @brief: 
 * @param {int} session
 * @param {int} display_id
 * @param {int} direction
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_start_screen_record(int session, int display_id, int direction);
/**
 * @brief: 
 * @param {int} session
 * @param {int} display_id
 * @param {int} direction
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_stop_screen_record(int session, int display_id, int direction);
/**
 * @brief: 
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_start_capture();
/**
 * @brief: 注册回调函数
 * @param {OnEventCallback} on_event_callback 回调函数
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_register_callback(OnEventCallback on_event_callback);
/**
 * @brief: 注册事件回调函数
 * @param {OnEventCallback} on_event_callback 回调函数
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_register_event_callback(OnEventCallback on_event_callback);
/**
 * @brief: 注册缓冲区回调函数
 * @param {OnPacketCallback} on_packet_callback 回调函数
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_register_packet_callback(OnPacketCallback on_packet_callback);
/**
 * @brief: 获取已打开且未最小化的窗口信息，列表内容由回调函数进行返回
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_get_windows_info();
/**
 * @brief: 获取显示器信息，列表内容由回调函数进行返回
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_get_monitors_info();
/**
 * @brief: 设置本设备名称
 * @param {const char*} name 设备名称
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_set_device_model(const char* name);
/**
 * @brief: 获取本设备名称
 * @return {const char*} name 设备名称
 * @author: wangr
 */
PTW32_DLLPORT const char* bishare_service_get_device_model();
/**
 * @brief: 获取当前路径
 * @return {const char*} dir 路径
 * @author: wangr
 */
PTW32_DLLPORT const char* bishare_service_get_current_director();
/**
 * @brief: 复位本设备名称
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_reset_device_model();
/**
 * @brief: 设置鼠标穿梭使能
 * @param {bool_type_t} enable
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_set_remote_mouse_control(bool_type_t enable);
/**
 * @brief: 获取鼠标穿梭使能
 * @return {bool_type_t} enable
 * @author: wangr
 */
PTW32_DLLPORT bool_type_t bishare_service_get_remote_mouse_control();
/**
 * @brief: 获取本设备存储路径根目录
 * @return {const char*} name 根目录地址
 * @author: wangr
 */
PTW32_DLLPORT const char* bishare_service_get_root_path();
/**
 * @brief: 设置本机网络参数
 * @param {network_type_t} type 网络类型 
 * @param {const char*} addr 本机IP
 * @param {const char*} mac 本机MAC
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_set_network_info(network_type_t type, const char* addr, const char* mac);
/**
 * @brief: 设置本机设备信息
 * @param {const char*} name 设备名称
 * @param {const char*} pwd 设备密码
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_set_device_info(const char* name, const char* pwd);
/**
 * @brief: 发送KEY-VALUE命令
 * @param {int} session 
 * @param {const char*} key 
 * @param {int} kLen key长度
 * @param {const char*} value
 * @param {int} vLen value长度
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_send_key_value(int session, const char* key, int kLen, const char* value, int vLen);
/**
 * @brief: 发送触摸事件
 * @param {int} session 
 * @param {int} displayId 
 * @param {int} pointerCount
 * @param {int} actionIndex
 * @param {int} action
 * @param {float*} x
 * @param {float*} y
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_send_touch_event(int session, int displayId, int pointerCount, int actionIndex, int action, const float* x, const float* y);
/**
 * @brief: 发送按键事件
 * @param {int} session 
 * @param {int} displayId 
 * @param {int} keyCode
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_send_key_event(int session, int displayId, int keyCode);
/**
 * @brief: 设置屏幕尺寸
 * @param {int} screenWidth 
 * @param {int} screenHeight 
 * @param {int} videoWidth
 * @param {int} videoHeight
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_set_size(int screenWidth, int screenHeight, int videoWidth, int videoHeight);
/**
 * @brief: 控制DMR播放视频
 * @param {int} session 对端session
 * @param {char*} filePath 
 * @param {char*} fileName
 * @param {char*} enhance
 * @return {bstatus_t} result
 * @author: wangr
 */ 
PTW32_DLLPORT bstatus_t bishare_service_transport_play_video_to_dmr(int session, const char* filePath, const char* fileName, const char* enhance);
/**
 * @brief: 控制DMR播放音频
 * @param {int} session 对端session 
 * @param {char*} filePath 
 * @param {char*} fileName
 * @param {char*} enhance
 * @return {bstatus_t} result
 * @author: wangr
 */ 
PTW32_DLLPORT bstatus_t bishare_service_transport_play_audio_to_dmr(int session, const char* filePath, const char* fileName, const char* enhance);
/**
 * @brief: 控制DMR播放图片
 * @param {int} session 对端session 
 * @param {char*} filePath 
 * @param {char*} fileName
 * @param {char*} enhance
 * @return {bstatus_t} result
 * @author: wangr
 */ 
PTW32_DLLPORT bstatus_t bishare_service_transport_play_image_to_dmr(int session, const char* filePath, const char* fileName, const char* enhance);
/**
 * @brief: 控制DMR停止播放
 * @param {int} session 对端session 
 * @param {bool_type_t} isRePlay 是否重播 (未实现)
 * @return {bstatus_t} result
 * @author: wangr
 */ 
PTW32_DLLPORT bstatus_t bishare_service_transport_stop_to_dmr(int session, bool_type_t isRePlay);
/**
 * @brief: 控制DMR播放位置
 * @param {int} session 对端session 
 * @param {char*} position
 * @return {bstatus_t} result
 * @author: wangr
 */ 
PTW32_DLLPORT bstatus_t bishare_service_transport_seek_bar_position_to_dmr(int session, const char* position);
/**
 * @brief: 控制DMR播放静音
 * @param {int} session 对端session 
 * @return {bstatus_t} result
 * @author: wangr
 */ 
PTW32_DLLPORT bstatus_t bishare_service_transport_mute_to_dmr(int session);
/**
 * @brief: 控制DMR增加1格音量
 * @param {int} session 对端session 
 * @return {bstatus_t} result
 * @author: wangr
 */ 
PTW32_DLLPORT bstatus_t bishare_service_transport_add_volume_by_one_to_dmr(int session);
/**
 * @brief: 控制DMR减少1格音量
 * @param {int} session 对端session 
 * @return {bstatus_t} result
 * @author: wangr
 */ 
PTW32_DLLPORT bstatus_t bishare_service_transport_sub_volume_by_one_to_dmr(int session);
/**
 * @brief: 控制DMR播放暂停
 * @param {int} session 对端session 
 * @return {bstatus_t} result
 * @author: wangr
 */ 
PTW32_DLLPORT bstatus_t bishare_service_transport_pause_to_dmr(int session);
/**
 * @brief: 控制DMR播放开始
 * @param {int} session 对端session 
 * @return {bstatus_t} result
 * @author: wangr
 */ 
PTW32_DLLPORT bstatus_t bishare_service_transport_play_to_dmr(int session);
/**
 * @brief: 设置服务器系统根目录
 * @param {char*} dir 系统根目录 
 * @return {bstatus_t} result
 * @author: wangr
 */ 
PTW32_DLLPORT bstatus_t bishare_service_set_mount_root_directory(const char* dir);
/**
 * @brief: 使能远程控制
 * @param {bool_type_t} enable 是否是能 
 * @return {bstatus_t} result
 * @author: wangr
 */ 
PTW32_DLLPORT bstatus_t bishare_service_enable_remote_control(bool_type_t enable);
/**
 * @brief: 开启Sw设备
 * @return {bstatus_t} result
 * @author: wangr
 */ 
PTW32_DLLPORT bstatus_t bishare_service_open_sw_device();
/**
 * @brief: 关闭Sw设备
 * @return {bstatus_t} result
 * @author: wangr
 */ 
PTW32_DLLPORT bstatus_t bishare_service_release_sw_device();
/**
 * @brief: 设置屏幕开关状态
 * @param {screen_state_t} state 开关状态 0-off, 1-on
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_set_screen_state(screen_state_t state);
/**
 * @brief: 设置默认音频输出设备
 * @param {bool} enable enable 是否是能 
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_set_default_audio_output_device(bool_type_t enable);
/**
 * @brief: 屏幕截图
 * @param {char*} filePath 是否是能 
 * @param {long} top 左顶点y坐标
 * @param {long} bottom 右底点y坐标
 * @param {long} left 左顶点x坐标
 * @param {long} right 右底点x坐标
 * @return {bstatus_t} result
 * @author: wangr
*/
PTW32_DLLPORT bstatus_t bishare_service_screenshot(const char* filePath, long top, long bottom, long left, long right);
/**
 * @brief: 启动WIFI-P2P广播
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_start_wifi_direct();
/**
 * @brief: 关闭WIFI-P2P广播
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_stop_wifi_direct();
/**
 * @brief: 开启WIFI-P2P探寻
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_discover_wifi_direct_device();
/**
 * @brief: 连接WIFI-P2P设备
 * @param {char*} name 设备名称 
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_connect_wifi_direct_device(const char* name);
/**
 * @brief: 是否支持WIFI-P2P
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_is_support_wifi_direct();
/**
 * @brief: 断开WIFI-P2P设备
 * @param {char*} name 设备名称 
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_disconnect_wifi_direct_device(const char* name);
/**
 * @brief: 连接WIFI
 * @param {char*} ssid SSID 
 * @param {char*} pwd 密码
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_connect_wifi_with_pwd(const char* ssid, const char* pwd);

/**
 * @brief: 发现远程设备
 * @param {char*} pincode 
 * @return {bstatus_t} result
 * @author: wangr
 */
PTW32_DLLPORT bstatus_t bishare_service_find_remote_device(const char* pincode);
#ifdef __cplusplus
}
#endif

#endif //_BISHARESDK_BISHARE_SERVICE_H
