#ifndef BISHARE_MANAGER_NAPI_H
#define BISHARE_MANAGER_NAPI_H

#include <napi/native_api.h>
#include <memory>
#include "bishare_napi.h"

namespace OHOS {
    namespace BiShare {
        namespace Interfaces {

            /**
             * BiShareManager NAPI包装类
             * 
             * 职责：
             * 1. 提供JavaScript可调用的BiShareManager类
             * 2. 实现单例模式
             * 3. 封装BiShare服务管理相关的NAPI方法
             * 4. 支持异步执行，避免主线程阻塞
             */
            class BiShareManagerNapi {
            public:
                /**
                 * 初始化BiShareManager类到NAPI环境
                 */
                static napi_value Init(napi_env env, napi_value exports);

                /**
                 * 创建BiShareManager实例
                 */
                static napi_value New(napi_env env, napi_callback_info info);

                /**
                 * 获取BiShareManager单例实例
                 */
                static napi_value GetInstance(napi_env env, napi_callback_info info);

                /**
                 * 初始化BiShare服务
                 */
                static napi_value Initialize(napi_env env, napi_callback_info info);

                /**
                 * 释放BiShare服务
                 */
                static napi_value Release(napi_env env, napi_callback_info info);

                /**
                 * 事件监听 - on
                 */
                static napi_value On(napi_env env, napi_callback_info info);

                /**
                 * 事件监听 - off
                 */
                static napi_value Off(napi_env env, napi_callback_info info);

                /**
                 * 事件监听 - once
                 */
                static napi_value Once(napi_env env, napi_callback_info info);

            private:
                /**
                 * 获取原生BiShare实例
                 */
                static BiShareNapi* GetNativeManager();

                /**
                 * 从NAPI值中提取BiShareManagerNapi实例
                 */
                static BiShareManagerNapi* GetInstanceFromThis(napi_env env, napi_value thisArg);

                /**
                 * 单例实例
                 */
                static napi_ref constructor_;
                static BiShareNapi* nativeManager_;

                /**
                 * 私有构造函数
                 */
                BiShareManagerNapi();
                ~BiShareManagerNapi();

                // 实例数据
                BiShareNapi* biShareManager_;
            };

        } // namespace Interfaces
    } // namespace BiShare
} // namespace OHOS

#endif // BISHARE_MANAGER_NAPI_H
