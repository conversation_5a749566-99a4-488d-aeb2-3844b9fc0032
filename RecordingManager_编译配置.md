# RecordingManager C++编译配置说明

## 📋 需要添加到编译系统的文件

### 1. 新增的C++源文件
确保以下文件被添加到CMakeLists.txt或BUILD.gn中：

```
bishare/src/main/cpp/interfaces/napi/recording_manager_napi.h
bishare/src/main/cpp/interfaces/napi/recording_manager_napi.cpp
```

### 2. CMakeLists.txt配置示例

如果项目使用CMake，需要在相应的CMakeLists.txt中添加：

```cmake
# 在bishare模块的CMakeLists.txt中添加
set(NAPI_SOURCES
    # 现有的源文件...
    src/main/cpp/interfaces/napi/bishare_napi_interface.cpp
    src/main/cpp/interfaces/napi/recording_manager_napi.cpp  # 新增
    # 其他源文件...
)

# 添加头文件路径
include_directories(
    # 现有的包含路径...
    src/main/cpp/interfaces/napi
    # 其他包含路径...
)
```

### 3. BUILD.gn配置示例

如果项目使用GN构建系统，需要在BUILD.gn中添加：

```gn
# 在bishare模块的BUILD.gn中添加
sources = [
  # 现有的源文件...
  "src/main/cpp/interfaces/napi/bishare_napi_interface.cpp",
  "src/main/cpp/interfaces/napi/recording_manager_napi.cpp",  # 新增
  # 其他源文件...
]

include_dirs = [
  # 现有的包含路径...
  "src/main/cpp/interfaces/napi",
  # 其他包含路径...
]
```

## 🔗 依赖关系

### 1. 头文件依赖
`recording_manager_napi.cpp` 依赖以下头文件：
- `recording_manager_napi.h` (新增)
- `bishare_napi.h` (现有)
- `bishare_logger.h` (现有)
- `bishare_recording.h` (现有)
- `<napi/native_api.h>` (系统)

### 2. 链接依赖
确保链接以下库：
- OpenHarmony NAPI库
- BiShare核心库
- 日志库

## 🚀 编译验证

### 1. 编译检查
```bash
# 清理并重新编译bishare模块
./build.sh clean
./build.sh bishare

# 检查编译输出中是否包含recording_manager_napi.cpp
```

### 2. 符号检查
编译完成后，可以检查生成的.so文件是否包含RecordingManager类：
```bash
# 检查导出的符号
nm -D libbishare_napi.so | grep RecordingManager
objdump -T libbishare_napi.so | grep RecordingManager
```

### 3. 运行时验证
```typescript
// 在JavaScript/TypeScript中测试
import { RecordingManager } from '@ohos/libbishare_napi';

try {
  const manager = RecordingManager.getInstance();
  console.log('✅ RecordingManager类导出成功');
} catch (error) {
  console.error('❌ RecordingManager类导出失败:', error);
}
```

## 🐛 常见编译问题

### 1. 头文件找不到
**错误**: `fatal error: 'recording_manager_napi.h' file not found`
**解决**: 检查include_directories配置，确保包含了正确的路径

### 2. 链接错误
**错误**: `undefined reference to BiShareRecordManager`
**解决**: 确保链接了BiShare核心库，检查链接顺序

### 3. NAPI符号未定义
**错误**: `undefined reference to napi_define_class`
**解决**: 确保链接了OpenHarmony NAPI库

### 4. 重复定义错误
**错误**: `multiple definition of RecordingManagerNapi::Init`
**解决**: 检查是否重复包含了源文件，确保每个.cpp文件只被编译一次

## 📝 编译后验证清单

- [ ] 编译无错误无警告
- [ ] 生成的.so文件包含RecordingManager符号
- [ ] JavaScript可以成功导入RecordingManager类
- [ ] getInstance()方法可以正常调用
- [ ] 基本方法调用不报错

## 🔧 调试建议

如果遇到问题，可以：

1. **启用详细编译日志**: 查看具体的编译和链接过程
2. **检查依赖关系**: 确保所有依赖的库都正确链接
3. **使用调试器**: 在C++代码中添加断点，验证方法是否被调用
4. **添加日志**: 在关键位置添加BiShareLogger输出，跟踪执行流程

---

**注意**: 具体的编译配置可能因项目结构而异，请根据实际的构建系统调整配置。
