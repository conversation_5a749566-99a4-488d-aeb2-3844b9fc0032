import createLogger from '../utils/Logger';
import { RecordingOptions, ScreenshotOptions, SizeOptions, Callback } from '../interfaces/BiShareTypes';
import { screenshot, setDefaultAudioOutputDevice, setSize, startCapture,
  startScreenRecord,
  stopScreenRecord } from 'libbishare_napi.so';

const logger = createLogger('BiShare.RecordingManager');

/**
 * Recording Manager class - handles screen recording operations
 */
export default class RecordingManager {
  private static instance: RecordingManager;
  private isRecording: boolean = false;

  /**
   * Private constructor to implement Singleton pattern
   */
  private constructor() {
    logger.info('RecordingManager created');
  }

  /**
   * Get RecordingManager instance (Singleton)
   * @returns RecordingManager instance
   */
  static getInstance(): RecordingManager {
    if (!RecordingManager.instance) {
      RecordingManager.instance = new RecordingManager();
    }
    return RecordingManager.instance;
  }

  /**
   * Start screen recording
   * @param options Recording options
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  startScreenRecord(options: RecordingOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      // 回调模式：传递回调函数给底层NAPI
      startScreenRecord(
        options.session,
        options.displayId,
        options.direction,
        (error: Error | null, result: boolean) => {
          if (error) {
            logger.error(`Failed to start screen recording with callback: ${error.message}`);
          } else {
            this.isRecording = true;
            logger.info(`Started screen recording with callback: session=${options.session}, displayId=${options.displayId}, direction=${options.direction}`);
          }
          callback(error, result);
        }
      );
    } else {
      // Promise模式：不传递回调函数，让底层NAPI返回Promise
      return startScreenRecord(
        options.session,
        options.displayId,
        options.direction
      ).then((result: boolean) => {
        this.isRecording = true;
        logger.info(`Started screen recording with promise: session=${options.session}, displayId=${options.displayId}, direction=${options.direction}`);
        return result;
      }).catch((error: Error) => {
        logger.error(`Failed to start screen recording with promise: ${error.message}`);
        throw error;
      });
    }
  }

  /**
   * Stop screen recording
   * @param options Recording options
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  stopScreenRecord(options: RecordingOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      // 回调模式：传递回调函数给底层NAPI
      stopScreenRecord(
        options.session,
        options.displayId,
        options.direction,
        (error: Error | null, result: boolean) => {
          if (error) {
            logger.error(`Failed to stop screen recording with callback: ${error.message}`);
          } else {
            this.isRecording = false;
            logger.info(`Stopped screen recording with callback: session=${options.session}, displayId=${options.displayId}, direction=${options.direction}`);
          }
          callback(error, result);
        }
      );
    } else {
      // Promise模式：不传递回调函数，让底层NAPI返回Promise
      return stopScreenRecord(
        options.session,
        options.displayId,
        options.direction
      ).then((result: boolean) => {
        this.isRecording = false;
        logger.info(`Stopped screen recording with promise: session=${options.session}, displayId=${options.displayId}, direction=${options.direction}`);
        return result;
      }).catch((error: Error) => {
        logger.error(`Failed to stop screen recording with promise: ${error.message}`);
        throw error;
      });
    }
  }

  /**
   * Start capture
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  startCapture(callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      // 回调模式：传递回调函数给底层NAPI
      startCapture((error: Error | null, result: boolean) => {
        if (error) {
          logger.error(`Failed to start capture with callback: ${error.message}`);
        } else {
          logger.info('Started capture with callback');
        }
        callback(error, result);
      });
    } else {
      // Promise模式：不传递回调函数，让底层NAPI返回Promise
      return startCapture()
        .then((result: boolean) => {
          logger.info('Started capture with promise');
          return result;
        }).catch((error: Error) => {
          logger.error(`Failed to start capture with promise: ${error.message}`);
          throw error;
        });
    }
  }

  /**
   * Set screen and video size
   * @param options Size options
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  setSize(options: SizeOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      // 回调模式：传递回调函数给底层NAPI
      setSize(
        options.screenWidth,
        options.screenHeight,
        options.videoWidth,
        options.videoHeight,
        (error: Error | null, result: boolean) => {
          if (error) {
            logger.error(`Failed to set size with callback: ${error.message}`);
          } else {
            logger.info(`Set size with callback: screenWidth=${options.screenWidth}, screenHeight=${options.screenHeight}, videoWidth=${options.videoWidth}, videoHeight=${options.videoHeight}`);
          }
          callback(error, result);
        }
      );
    } else {
      // Promise模式：不传递回调函数，让底层NAPI返回Promise
      return setSize(
        options.screenWidth,
        options.screenHeight,
        options.videoWidth,
        options.videoHeight
      ).then((result: boolean) => {
        logger.info(`Set size with promise: screenWidth=${options.screenWidth}, screenHeight=${options.screenHeight}, videoWidth=${options.videoWidth}, videoHeight=${options.videoHeight}`);
        return result;
      }).catch((error: Error) => {
        logger.error(`Failed to set size with promise: ${error.message}`);
        throw error;
      });
    }
  }

  /**
   * Set default audio output device
   * @param enable Whether to enable the default audio output device
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  setDefaultAudioOutputDevice(enable: boolean, callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      // 回调模式：传递回调函数给底层NAPI
      setDefaultAudioOutputDevice(enable, (error: Error | null, result: boolean) => {
        if (error) {
          logger.error(`Failed to set default audio output device with callback: ${error.message}`);
        } else {
          logger.info(`Set default audio output device with callback: enable=${enable}`);
        }
        callback(error, result);
      });
    } else {
      // Promise模式：不传递回调函数，让底层NAPI返回Promise
      return setDefaultAudioOutputDevice(enable)
        .then((result: boolean) => {
          logger.info(`Set default audio output device with promise: enable=${enable}`);
          return result;
        }).catch((error: Error) => {
          logger.error(`Failed to set default audio output device with promise: ${error.message}`);
          throw error;
        });
    }
  }

  /**
   * Take a screenshot
   * @param options Screenshot options
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  screenshot(options: ScreenshotOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      // 回调模式：传递回调函数给底层NAPI
      screenshot(
        options.filePath,
        options.top,
        options.bottom,
        options.left,
        options.right,
        (error: Error | null, result: boolean) => {
          if (error) {
            logger.error(`Failed to take screenshot with callback: ${error.message}`);
          } else {
            logger.info(`Took screenshot with callback: filePath=${options.filePath}`);
          }
          callback(error, result);
        }
      );
    } else {
      // Promise模式：不传递回调函数，让底层NAPI返回Promise
      return screenshot(
        options.filePath,
        options.top,
        options.bottom,
        options.left,
        options.right
      ).then((result: boolean) => {
        logger.info(`Took screenshot with promise: filePath=${options.filePath}`);
        return result;
      }).catch((error: Error) => {
        logger.error(`Failed to take screenshot with promise: ${error.message}`);
        throw error;
      });
    }
  }

  /**
   * Check if recording is in progress
   * @returns True if recording is in progress, false otherwise
   */
  isRecordingInProgress(): boolean {
    return this.isRecording;
  }
}