# Entry模块通信层 (comm)

## 🎯 概述

Entry模块的comm目录是应用层与BiShare模块之间的通信桥梁，提供简化的API接口、统一的状态管理、响应式数据绑定和完善的错误处理机制。

## 📁 目录结构

```
entry/src/main/ets/comm/
├── adapters/                    # 适配器层
│   └── UIAdapter.ets           # UI适配器，为UI层提供简化接口
├── managers/                    # 管理器层
│   ├── DeviceManager.ets       # 设备管理器
│   ├── NetworkManager.ets      # 网络管理器
│   ├── RecordingManager.ets    # 录制管理器
│   └── EventManager.ets        # 事件管理器
├── service/                     # 服务层
│   └── BiShareService.ets      # BiShare统一服务
├── constants/                   # 常量定义
│   └── CommConstants.ets       # 通信层常量
├── DESIGN_PRINCIPLES.md        # 设计原理文档
├── INTEGRATION_GUIDE.md        # 接入指南文档
└── README.md                   # 本文档
```

## 🏗️ 架构设计

### 分层架构

```
┌─────────────────────────────────────────────────────────────┐
│                    UI层 (Pages/Components)                  │
├─────────────────────────────────────────────────────────────┤
│                    适配器层 (Adapters)                      │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   UIAdapter                             │ │
│  │  - 简化API接口                                           │ │
│  │  - 响应式状态管理                                        │ │
│  │  - 统一错误处理                                          │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    服务层 (Service)                         │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                 BiShareService                          │ │
│  │  - 统一服务管理                                          │ │
│  │  - 模块协调                                             │ │
│  │  - 生命周期管理                                          │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                   管理器层 (Managers)                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │DeviceManager│ │NetworkMgr   │ │RecordingMgr │           │
│  │- 设备管理    │ │- 网络管理    │ │- 录制管理    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                  BiShare模块层                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              @ohos/libbishare_napi                      │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

1. **UIAdapter**: UI适配器，为UI层提供简化的接口
2. **BiShareService**: 统一的服务管理入口
3. **DeviceManager**: 设备发现、连接、管理
4. **NetworkManager**: 网络连接、状态监控
5. **RecordingManager**: 录制功能管理
6. **EventManager**: 事件监听和分发

## 🚀 快速开始

### 1. 基本使用

```typescript
import { UIAdapter } from '../comm/adapters/UIAdapter';

// 获取适配器实例
const adapter = UIAdapter.getInstance();

// 初始化服务
const success = await adapter.initialize(context, options);

// 开始设备发现
await adapter.startDeviceDiscovery();

// 获取发现的设备
const devices = adapter.getDiscoveredDevices();
```

### 2. 网络管理

```typescript
// 网络信息直接使用bishare模块提供的类型
import { NetworkInfoOptions, NetworkType } from '@ohos/libbishare_napi';

const networkInfo: NetworkInfoOptions = {
  networkType: NetworkType.Wlan,
  addr: '*************',
  mac: '702ad701bd2a'
};

// 设置网络信息
const result = await networkManager.setNetworkInfo(networkInfo);
```

### 3. 事件监听

```typescript
import { UIAdapter } from '../comm/adapters/UIAdapter';

const adapter = UIAdapter.getInstance();

// 监听状态变化
adapter.addStateChangeListener('deviceDiscovered', (devices) => {
  console.log('发现新设备:', devices);
});

// 监听连接状态
adapter.addStateChangeListener('deviceConnected', (device) => {
  console.log('设备已连接:', device.id);
});
```

## ⚙️ 配置选项

### 默认配置

- 默认超时时间: 30秒
- 默认重试次数: 3次
- 默认刷新间隔: 5秒
- 设备发现超时: 60秒
- 设备连接超时: 15秒

### 错误代码

- `SERVICE_NOT_INITIALIZED`: 服务未初始化
- `DEVICE_DISCOVERY_FAILED`: 设备发现失败
- `NETWORK_SETUP_FAILED`: 网络设置失败
- `OPERATION_TIMEOUT`: 操作超时
- `PERMISSION_DENIED`: 权限不足

## 🔧 扩展指南

### 添加新的管理器

1. 在 `managers/` 目录下创建新的管理器类
2. 在 `BiShareService` 中注册新管理器
3. 在 `UIAdapter` 中添加相应的适配方法
4. 更新相关文档

### 添加新的事件类型

1. 直接使用bishare模块提供的事件类型
2. 在 `EventManager` 中添加相应的监听器支持
3. 在相关管理器中触发新事件

### 添加新的工具类

1. 在 `constants/` 目录下添加相关常量
2. 或直接在相应的管理器中实现工具方法
3. 优先使用bishare模块提供的功能

## 🛡️ 错误处理

所有异步操作都返回统一的结果格式：

```typescript
interface BiShareResult<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}
```

## 📊 性能优化

1. **状态缓存**: 缓存频繁访问的状态
2. **事件节流**: 限制高频事件的处理频率
3. **懒加载**: 按需初始化管理器
4. **异步操作**: 避免阻塞主线程

## 📚 详细文档

- **[设计原理文档](./DESIGN_PRINCIPLES.md)** - 详细的架构设计原理、设计模式应用和核心组件设计
- **[接入指南文档](./INTEGRATION_GUIDE.md)** - 新功能和管理器的详细接入方式、步骤和最佳实践

## 🔍 注意事项

1. **线程安全**: 所有管理器都是单例模式，注意线程安全
2. **内存管理**: 及时释放资源，避免内存泄漏
3. **错误处理**: 所有异步操作都要进行错误处理
4. **日志记录**: 使用统一的日志标签，便于调试
5. **权限检查**: 网络相关操作需要检查相应权限
6. **类型统一**: 统一使用bishare模块提供的类型定义

## 🎯 设计原则

1. **适配器模式**: UIAdapter适配复杂的底层API
2. **外观模式**: BiShareService提供统一的服务接口
3. **观察者模式**: 事件监听和状态通知
4. **单例模式**: 全局唯一的服务实例
5. **单一职责**: 每个组件只负责特定功能
6. **依赖倒置**: 高层模块不依赖低层模块

## 📈 TODO

- [ ] 集成OpenHarmony真实的网络API
- [ ] 实现真实的设备发现机制
- [ ] 添加更多的网络状态监控
- [ ] 优化错误处理机制
- [ ] 添加单元测试
- [ ] 性能优化和内存管理

---

这个架构设计确保了Entry/Comm目录的高内聚、低耦合，同时为UI层提供了简洁易用的接口。通过分层设计和设计模式的应用，实现了良好的可扩展性和可维护性。
