#ifndef RECORDING_MANAGER_NAPI_H
#define RECORDING_MANAGER_NAPI_H

#include <napi/native_api.h>
#include <memory>
#include "bishare_recording.h"

namespace OHOS {
    namespace BiShare {
        namespace Interfaces {

            /**
             * RecordingManager NAPI包装类
             * 
             * 职责：
             * 1. 提供JavaScript可调用的RecordingManager类
             * 2. 实现单例模式
             * 3. 封装录制相关的NAPI方法
             */
            class RecordingManagerNapi {
            public:
                /**
                 * 初始化RecordingManager类到NAPI环境
                 */
                static napi_value Init(napi_env env, napi_value exports);

                /**
                 * 创建RecordingManager实例
                 */
                static napi_value New(napi_env env, napi_callback_info info);

                /**
                 * 获取RecordingManager单例实例
                 */
                static napi_value GetInstance(napi_env env, napi_callback_info info);

                /**
                 * 开始屏幕录制
                 */
                static napi_value StartScreenRecord(napi_env env, napi_callback_info info);

                /**
                 * 停止屏幕录制
                 */
                static napi_value StopScreenRecord(napi_env env, napi_callback_info info);

                /**
                 * 截图
                 */
                static napi_value Screenshot(napi_env env, napi_callback_info info);

                /**
                 * 析构函数
                 */
                static napi_value Destructor(napi_env env, napi_callback_info info);

            private:
                /**
                 * 获取原生RecordingManager实例
                 */
                static std::shared_ptr<BiShareRecordManager> GetNativeManager();

                /**
                 * 从NAPI值中提取RecordingManagerNapi实例
                 */
                static RecordingManagerNapi* GetInstanceFromThis(napi_env env, napi_value thisArg);

                /**
                 * 单例实例
                 */
                static napi_ref constructor_;
                static std::shared_ptr<BiShareRecordManager> nativeManager_;

                /**
                 * 私有构造函数
                 */
                RecordingManagerNapi();
                ~RecordingManagerNapi();

                // 实例数据
                std::shared_ptr<BiShareRecordManager> recordManager_;
            };

        } // namespace Interfaces
    } // namespace BiShare
} // namespace OHOS

#endif // RECORDING_MANAGER_NAPI_H
