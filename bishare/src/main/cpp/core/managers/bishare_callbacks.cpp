#include "bishare_callbacks.h"
#include "bishare_logger.h"
#include "bishare_napi.h"
#include <thread>
#include <sstream>
#include <unistd.h>
#include <sstream>


namespace OHOS {
    namespace BiShare {

        static constexpr const char* CALLBACKS_TAG = "BiShareCallbacks";

        // 辅助函数：安全地将线程ID转换为字符串
        static std::string ThreadIdToString(std::thread::id id) {
            std::ostringstream oss;
            oss << id;
            return oss.str();
        }

        // Initialize static instance
        std::shared_ptr<BiShareCallbacks> BiShareCallbacks::instance_ = nullptr;

        BiShareCallbacks::BiShareCallbacks(std::weak_ptr<BiShareNapi> napiInstance)
            : napiInstance_(napiInstance), isRunning_(false) {
            // 不在构造函数中设置静态实例，避免双重删除问题
            BiShareLogger::Info(CALLBACKS_TAG, "🏗️ BiShareCallbacks构造函数开始执行");
            BiShareLogger::Info(CALLBACKS_TAG, "📍 [构造] this指针地址: %p", this);
            BiShareLogger::Info(CALLBACKS_TAG, "📍 [构造] napiInstance是否有效: %s", napiInstance.expired() ? "否" : "是");
            BiShareLogger::Info(CALLBACKS_TAG, "📍 [构造] 当前静态实例状态: %s", instance_ ? "已存在" : "空");
            BiShareLogger::Info(CALLBACKS_TAG, "✅ BiShareCallbacks构造函数执行完成");
        }

        BiShareCallbacks::~BiShareCallbacks() {
            BiShareLogger::Info(CALLBACKS_TAG, "🔥 BiShareCallbacks析构函数开始执行");
            BiShareLogger::Info(CALLBACKS_TAG, "📍 [析构] this指针地址: %p", this);
            BiShareLogger::Info(CALLBACKS_TAG, "📍 [析构] 当前运行状态: %s", isRunning_ ? "运行中" : "已停止");
            BiShareLogger::Info(CALLBACKS_TAG, "📍 [析构] 静态实例状态: %s", instance_ ? "存在" : "空");

            // Stop the callback threads
            BiShareLogger::Info(CALLBACKS_TAG, "🛑 [析构] 停止回调线程...");
            isRunning_ = false;

            // Notify the threads to exit
            BiShareLogger::Info(CALLBACKS_TAG, "📢 [析构] 通知线程退出...");
            eventCallbackCondition_.notify_all();
            packetCallbackCondition_.notify_all();

            // Wait for the threads to exit
            BiShareLogger::Info(CALLBACKS_TAG, "⏳ [析构] 等待事件回调线程结束...");
            if (eventCallbackThread_.joinable()) {
                eventCallbackThread_.join();
                BiShareLogger::Info(CALLBACKS_TAG, "✅ [析构] 事件回调线程已结束");
            } else {
                BiShareLogger::Info(CALLBACKS_TAG, "ℹ️ [析构] 事件回调线程不可join");
            }

            BiShareLogger::Info(CALLBACKS_TAG, "⏳ [析构] 等待数据包回调线程结束...");
            if (packetCallbackThread_.joinable()) {
                packetCallbackThread_.join();
                BiShareLogger::Info(CALLBACKS_TAG, "✅ [析构] 数据包回调线程已结束");
            } else {
                BiShareLogger::Info(CALLBACKS_TAG, "ℹ️ [析构] 数据包回调线程不可join");
            }

            // Clear the static instance
            BiShareLogger::Info(CALLBACKS_TAG, "🧹 [析构] 清理静态实例...");
            if (instance_.get() == this) {
                instance_ = nullptr;
                BiShareLogger::Info(CALLBACKS_TAG, "✅ [析构] 静态实例已清理");
            } else {
                BiShareLogger::Warn(CALLBACKS_TAG, "⚠️ [析构] 静态实例指向其他对象，不清理");
            }

            BiShareLogger::Info(CALLBACKS_TAG, "🏁 BiShareCallbacks析构函数执行完成");
        }

        void BiShareCallbacks::SetStaticInstance(std::shared_ptr<BiShareCallbacks> instance) {
            BiShareLogger::Info(CALLBACKS_TAG, "🔧 SetStaticInstance开始执行");
            BiShareLogger::Info(CALLBACKS_TAG, "📍 [静态实例] 传入实例地址: %p", instance.get());
            BiShareLogger::Info(CALLBACKS_TAG, "📍 [静态实例] 传入实例引用计数: %ld", instance.use_count());
            BiShareLogger::Info(CALLBACKS_TAG, "📍 [静态实例] 当前静态实例: %p", instance_.get());

            if (instance_) {
                BiShareLogger::Info(CALLBACKS_TAG, "📍 [静态实例] 当前静态实例引用计数: %ld", instance_.use_count());
                BiShareLogger::Warn(CALLBACKS_TAG, "⚠️ [静态实例] 覆盖现有静态实例");
            }

            instance_ = instance;

            BiShareLogger::Info(CALLBACKS_TAG, "📍 [静态实例] 设置后实例地址: %p", instance_.get());
            BiShareLogger::Info(CALLBACKS_TAG, "📍 [静态实例] 设置后引用计数: %ld", instance_.use_count());
            BiShareLogger::Info(CALLBACKS_TAG, "✅ BiShareCallbacks静态实例设置完成");
        }

        bool BiShareCallbacks::Initialize() {
            BiShareLogger::Info(CALLBACKS_TAG, "🚀 BiShareCallbacks::Initialize开始执行");
            BiShareLogger::Info(CALLBACKS_TAG, "📍 [初始化] this指针地址: %p", this);
            BiShareLogger::Info(CALLBACKS_TAG, "📍 [初始化] 当前运行状态: %s", isRunning_ ? "运行中" : "停止");
            BiShareLogger::Info(CALLBACKS_TAG, "📍 [初始化] 静态实例状态: %s", instance_ ? "存在" : "空");

            try {
                // Start the callback threads
                BiShareLogger::Info(CALLBACKS_TAG, "🔄 [初始化] 设置运行状态为true...");
                isRunning_ = true;

                // Start the event callback thread
                BiShareLogger::Info(CALLBACKS_TAG, "🧵 [初始化] 启动事件回调线程...");
                eventCallbackThread_ = std::thread(&BiShareCallbacks::EventCallbackThreadFunc, this);
                BiShareLogger::Info(CALLBACKS_TAG, "✅ [初始化] 事件回调线程已启动，线程ID: %s",
                    ThreadIdToString(eventCallbackThread_.get_id()).c_str());

                // Start the packet callback thread
                BiShareLogger::Info(CALLBACKS_TAG, "🧵 [初始化] 启动数据包回调线程...");
                packetCallbackThread_ = std::thread(&BiShareCallbacks::PacketCallbackThreadFunc, this);
                BiShareLogger::Info(CALLBACKS_TAG, "✅ [初始化] 数据包回调线程已启动，线程ID: %s",
                    ThreadIdToString(packetCallbackThread_.get_id()).c_str());

                BiShareLogger::Info(CALLBACKS_TAG, "🎉 BiShareCallbacks初始化完成，回调线程已启动");
                return true;
            } catch (const std::exception& e) {
                BiShareLogger::Error(CALLBACKS_TAG, "❌ [初始化] BiShareCallbacks初始化异常: %s", e.what());
                isRunning_ = false;
                return false;
            }
        }

        bool BiShareCallbacks::RegisterEventCallback(napi_env env, napi_value callback, int eventType,
                                                     napi_ref *callbackRef) {
            // Create a reference to the callback
            napi_create_reference(env, callback, 1, callbackRef);

            return true;
        }

        bool BiShareCallbacks::UnregisterEventCallback(napi_env env, napi_ref callbackRef, int eventType) {
            // Delete the reference
            napi_delete_reference(env, callbackRef);

            return true;
        }

        void BiShareCallbacks::OnEventCallback(int type, const char *value, int len) {
            BiShareLogger::Info(CALLBACKS_TAG, "📞 OnEventCallback被调用 - type: %d, len: %d", type, len);
            BiShareLogger::Info(CALLBACKS_TAG, "📍 [事件回调] 调用线程ID: %s",
                ThreadIdToString(std::this_thread::get_id()).c_str());
            BiShareLogger::Info(CALLBACKS_TAG, "📍 [事件回调] 静态实例状态: %s (地址: %p)",
                instance_ ? "存在" : "空", instance_.get());

            // 安全检查参数
            if (value == nullptr || len < 0) {
                BiShareLogger::Error(CALLBACKS_TAG, "❌ [事件回调] 无效参数: value=%p, len=%d", value, len);
                return;
            }

            BiShareLogger::Info(CALLBACKS_TAG, "📍 [事件回调] 参数验证通过");
            BiShareLogger::Info(CALLBACKS_TAG, "📍 [事件回调] value内容: %{public}s", value);

            // Check if instance exists
            if (instance_ == nullptr) {
                BiShareLogger::Error(CALLBACKS_TAG, "❌ [事件回调] BiShareCallbacks静态实例为空");
                return;
            }

            BiShareLogger::Info(CALLBACKS_TAG, "📍 [事件回调] 静态实例引用计数: %ld", instance_.use_count());

            try {
                BiShareLogger::Info(CALLBACKS_TAG, "🔄 [事件回调] 调用EnqueueEventCallback...");
                // Enqueue the event callback
                instance_->EnqueueEventCallback(type, value, len);
                BiShareLogger::Info(CALLBACKS_TAG, "✅ [事件回调] EnqueueEventCallback调用成功");
            } catch (const std::exception& e) {
                BiShareLogger::Error(CALLBACKS_TAG, "❌ [事件回调] OnEventCallback异常: %s", e.what());
            }
        }

        void BiShareCallbacks::OnPacketCallback(int session, int type, const char *buffer, int len, int width,
                                                int height, int time) {
            BiShareLogger::Info(CALLBACKS_TAG, "OnPacketCallback - session: %d, type: %d, len: %d, width: %d, height: %d, time: %d", session, type, len, width, height, time);

            // 安全检查参数
            if (buffer == nullptr || len < 0) {
                BiShareLogger::Error(CALLBACKS_TAG, "OnPacketCallback - 无效参数: buffer=%p, len=%d", buffer, len);
                return;
            }

            // Check if instance exists
            if (instance_ == nullptr) {
                BiShareLogger::Error(CALLBACKS_TAG, "BiShareCallbacks instance is null");
                return;
            }

            try {
                // Enqueue the packet callback
                instance_->EnqueuePacketCallback(session, type, buffer, len, width, height, time);
            } catch (const std::exception& e) {
                BiShareLogger::Error(CALLBACKS_TAG, "OnPacketCallback异常: %s", e.what());
            }
        }

        void BiShareCallbacks::EnqueueEventCallback(int type, const char *value, int len) {
            try {
                // 安全检查参数
                if (value == nullptr || len < 0) {
                    BiShareLogger::Error(CALLBACKS_TAG, "EnqueueEventCallback - 无效参数");
                    return;
                }

                // Create event callback data
                EventCallbackData eventData;
                eventData.eventType = type;
                eventData.eventData = std::string(value, len);
                eventData.dataLength = len;

                // Enqueue the event callback
                {
                    std::lock_guard<std::mutex> lock(eventCallbackMutex_);
                    eventCallbackQueue_.push(eventData);
                }

                // Notify the event callback thread
                eventCallbackCondition_.notify_one();

                BiShareLogger::Info(CALLBACKS_TAG, "事件回调已入队，类型: %d", type);
            } catch (const std::exception& e) {
                BiShareLogger::Error(CALLBACKS_TAG, "EnqueueEventCallback异常: %s", e.what());
            }
        }

        void BiShareCallbacks::EnqueuePacketCallback(int session, int type, const char *buffer, int len, int width,
                                                     int height, int time) {
            // Create packet callback data
            PacketCallbackData packetData;
            packetData.session = session;
            packetData.bufferType = type;
            packetData.buffer.assign(buffer, buffer + len);
            packetData.width = width;
            packetData.height = height;
            packetData.timestamp = time;

            // Enqueue the packet callback
            {
                std::lock_guard<std::mutex> lock(packetCallbackMutex_);
                packetCallbackQueue_.push(packetData);
            }

            // Notify the packet callback thread
            packetCallbackCondition_.notify_one();
        }

        void BiShareCallbacks::EventCallbackThreadFunc() {
            while (isRunning_) {
                EventCallbackData eventData;

                // Wait for an event callback
                {
                    std::unique_lock<std::mutex> lock(eventCallbackMutex_);
                    eventCallbackCondition_.wait(lock, [this] { return !isRunning_ || !eventCallbackQueue_.empty(); });

                    // Check if we need to exit
                    if (!isRunning_) {
                        break;
                    }

                    // Get the event callback
                    if (eventCallbackQueue_.empty()) {
                        continue;
                    }

                    eventData = eventCallbackQueue_.front();
                    eventCallbackQueue_.pop();
                }

                // Get the NAPI instance from the weak pointer
                auto napiInstance = napiInstance_.lock();
                if (!napiInstance) {
                    BiShareLogger::Error(CALLBACKS_TAG, "NAPI instance is null");
                    continue;
                }

                // Find all callbacks for this event type
                std::vector<BiShareNapi::EventInfo> callbacks;
                {
                    pthread_mutex_lock(&napiInstance->eventCallbacksMutex_);
                    callbacks = napiInstance->eventCallbacks_[eventData.eventType];
                    pthread_mutex_unlock(&napiInstance->eventCallbacksMutex_);
                }

                // Call each callback with the event data
                for (const auto &callbackInfo : callbacks) {
                    napi_env env = callbackInfo.env;
                    napi_ref callbackRef = callbackInfo.callbackRef;

                    // Create the event data object
                    napi_value eventObject;
                    napi_create_object(env, &eventObject);

                    // Set event type
                    napi_value eventType;
                    napi_create_int32(env, eventData.eventType, &eventType);
                    napi_set_named_property(env, eventObject, "type", eventType);

                    // Set event data
                    napi_value eventDataValue;
                    napi_create_string_utf8(env, eventData.eventData.c_str(), eventData.eventData.length(),
                                            &eventDataValue);
                    napi_set_named_property(env, eventObject, "data", eventDataValue);

                    // Get the callback function
                    napi_value callback;
                    napi_get_reference_value(env, callbackRef, &callback);

                    // Call the callback function
                    napi_value global, result;
                    napi_get_global(env, &global);

                    // Prepare arguments
                    napi_value args[1] = {eventObject};

                    // Call the function
                    napi_status status = napi_call_function(env, global, callback, 1, args, &result);
                    if (status != napi_ok) {
                        BiShareLogger::Error(CALLBACKS_TAG, "Failed to call event callback function");
                    }
                }
            }
        }

        void BiShareCallbacks::PacketCallbackThreadFunc() {
            while (isRunning_) {
                PacketCallbackData packetData;

                // Wait for a packet callback
                {
                    std::unique_lock<std::mutex> lock(packetCallbackMutex_);
                    packetCallbackCondition_.wait(lock,
                                                  [this] { return !isRunning_ || !packetCallbackQueue_.empty(); });

                    // Check if we need to exit
                    if (!isRunning_) {
                        break;
                    }

                    // Get the packet callback
                    if (packetCallbackQueue_.empty()) {
                        continue;
                    }

                    packetData = packetCallbackQueue_.front();
                    packetCallbackQueue_.pop();
                }

                // Get the NAPI instance from the weak pointer
                auto napiInstance = napiInstance_.lock();
                if (!napiInstance) {
                    BiShareLogger::Error(CALLBACKS_TAG, "NAPI instance is null");
                    continue;
                }

                // Find all callbacks for the packet event type (we'll use EVENT_SCREEN_RECORD for packets)
                std::vector<BiShareNapi::EventInfo> callbacks;
                {
                    pthread_mutex_lock(&napiInstance->eventCallbacksMutex_);
                    callbacks = napiInstance->eventCallbacks_[EVENT_SCREEN_RECORD];
                    pthread_mutex_unlock(&napiInstance->eventCallbacksMutex_);
                }

                // Call each callback with the packet data
                for (const auto &callbackInfo : callbacks) {
                    napi_env env = callbackInfo.env;
                    napi_ref callbackRef = callbackInfo.callbackRef;

                    // Create the packet data object
                    napi_value packetObject;
                    napi_create_object(env, &packetObject);

                    // Set session
                    napi_value session;
                    napi_create_int32(env, packetData.session, &session);
                    napi_set_named_property(env, packetObject, "session", session);

                    // Set buffer type
                    napi_value bufferType;
                    napi_create_int32(env, packetData.bufferType, &bufferType);
                    napi_set_named_property(env, packetObject, "bufferType", bufferType);

                    // Set width
                    napi_value width;
                    napi_create_int32(env, packetData.width, &width);
                    napi_set_named_property(env, packetObject, "width", width);

                    // Set height
                    napi_value height;
                    napi_create_int32(env, packetData.height, &height);
                    napi_set_named_property(env, packetObject, "height", height);

                    // Set timestamp
                    napi_value timestamp;
                    napi_create_int32(env, packetData.timestamp, &timestamp);
                    napi_set_named_property(env, packetObject, "timestamp", timestamp);

                    // Set buffer (ArrayBuffer)
                    napi_value buffer;
                    void *bufferData;
                    napi_create_arraybuffer(env, packetData.buffer.size(), &bufferData, &buffer);
                    memcpy(bufferData, packetData.buffer.data(), packetData.buffer.size());
                    napi_set_named_property(env, packetObject, "buffer", buffer);

                    // Get the callback function
                    napi_value callback;
                    napi_get_reference_value(env, callbackRef, &callback);

                    // Call the callback function
                    napi_value global, result;
                    napi_get_global(env, &global);

                    // Prepare arguments
                    napi_value args[1] = {packetObject};

                    // Call the function
                    napi_status status = napi_call_function(env, global, callback, 1, args, &result);
                    if (status != napi_ok) {
                        BiShareLogger::Error(CALLBACKS_TAG, "Failed to call packet callback function");
                    }
                }
            }
        }

    } // namespace BiShare
} // namespace OHOS