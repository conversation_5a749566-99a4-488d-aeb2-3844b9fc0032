#include "direct_executors/device_direct_executor.h"
#include "bishare_napi.h"
#include "bishare_logger.h"
#include "bishare-service.h"
#include "bishare_status_codes.h"

namespace OHOS {
    namespace BiShare {

        const char* DeviceDirectExecutor::TAG = "DeviceDirectExecutor";

        void DeviceDirectExecutor::RegisterAll() {
            BiShareLogger::Info(TAG, "📝 [注册] 开始注册设备管理直接执行器...");

            // 注册设备发现和管理功能
            RegisterDirectExecutor("DiscoverDevices", ExecuteDiscoverDevices);
            RegisterDirectExecutor("GetDiscoveredDevices", ExecuteGetDiscoveredDevices);
            RegisterDirectExecutor("ClearDiscoveredDevices", ExecuteClearDiscoveredDevices);
            RegisterDirectExecutor("SetDeviceInfo", ExecuteSetDeviceInfo);
            RegisterDirectExecutor("FindRemoteDevice", ExecuteFindRemoteDevice);

            // 注册设备模型管理功能
            RegisterDirectExecutor("SetDeviceModel", ExecuteSetDeviceModel);
            RegisterDirectExecutor("GetDeviceModel", ExecuteGetDeviceModel);
            RegisterDirectExecutor("ResetDeviceModel", ExecuteResetDeviceModel);

            BiShareLogger::Info(TAG, "✅ [注册] 设备管理直接执行器注册完成");
        }

        void DeviceDirectExecutor::ExecuteDiscoverDevices(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(TAG, "🚀 [直接执行] 开始执行DiscoverDevices操作");

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务未初始化");
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return;
            }

            BiShareLogger::Info(TAG, "🔧 [直接执行] 开始发现设备...");

            // 调用原生设备发现函数
            workData->result = bishare_service_discovery_device();

            BiShareLogger::Info(TAG, "📊 [直接执行] 设备发现结果: %d (%s)",
                static_cast<int>(workData->result), err2str(workData->result));

            if (workData->result == BS_OK) {
                workData->successMessage = "设备发现启动成功";
                BiShareLogger::Info(TAG, "🎉 [直接执行] 设备发现启动成功");
            } else {
                workData->errorMessage = std::string("设备发现失败: ") +
                    std::string(err2str(workData->result));
                BiShareLogger::Error(TAG, "❌ [直接执行] 设备发现失败: %s",
                    err2str(workData->result));
            }

            BiShareLogger::Info(TAG, "🏁 [直接执行] DiscoverDevices操作执行完成");
        }

        void DeviceDirectExecutor::ExecuteGetDiscoveredDevices(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(TAG, "🚀 [直接执行] 开始执行GetDiscoveredDevices操作");

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务未初始化");
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return;
            }

            BiShareLogger::Info(TAG, "🔧 [直接执行] 获取已发现的设备...");

            // 调用原生函数获取设备列表
            workData->result = bishare_service_get_discovery_device();

            BiShareLogger::Info(TAG, "📊 [直接执行] 获取设备列表结果: %d (%s)",
                static_cast<int>(workData->result), err2str(workData->result));

            if (workData->result == BS_OK) {
                workData->successMessage = "获取设备列表成功";
                BiShareLogger::Info(TAG, "🎉 [直接执行] 获取设备列表成功");
            } else {
                workData->errorMessage = std::string("获取设备列表失败: ") +
                    std::string(err2str(workData->result));
                BiShareLogger::Error(TAG, "❌ [直接执行] 获取设备列表失败: %s",
                    err2str(workData->result));
            }

            BiShareLogger::Info(TAG, "🏁 [直接执行] GetDiscoveredDevices操作执行完成");
        }

        void DeviceDirectExecutor::ExecuteClearDiscoveredDevices(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(TAG, "🚀 [直接执行] 开始执行ClearDiscoveredDevices操作");

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务未初始化");
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return;
            }

            BiShareLogger::Info(TAG, "🔧 [直接执行] 清除已发现设备列表...");

            // 调用原生清除设备列表函数
            workData->result = bishare_service_clear_discovery_device();

            BiShareLogger::Info(TAG, "📊 [直接执行] 清除设备列表结果: %d (%s)",
                static_cast<int>(workData->result), err2str(workData->result));

            if (workData->result == BS_OK) {
                workData->successMessage = "设备列表清除成功";
                BiShareLogger::Info(TAG, "🎉 [直接执行] 设备列表清除成功");
            } else {
                workData->errorMessage = std::string("设备列表清除失败: ") +
                    std::string(err2str(workData->result));
                BiShareLogger::Error(TAG, "❌ [直接执行] 设备列表清除失败: %s",
                    err2str(workData->result));
            }

            BiShareLogger::Info(TAG, "🏁 [直接执行] ClearDiscoveredDevices操作执行完成");
        }

        void DeviceDirectExecutor::ExecuteSetDeviceInfo(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(TAG, "🚀 [直接执行] 开始执行SetDeviceInfo操作");

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务未初始化");
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return;
            }

            // 解析参数
            const std::string& deviceName = workData->data.stringParam1;
            const std::string& password = workData->data.stringParam2;

            BiShareLogger::Info(TAG, "🔧 [直接执行] 设置设备信息...");
            BiShareLogger::Info(TAG, "📍 [直接执行] 设备名称: %s", deviceName.c_str());

            // 调用原生设置设备信息函数
            workData->result = bishare_service_set_device_info(deviceName.c_str(), password.c_str());

            BiShareLogger::Info(TAG, "📊 [直接执行] 设置设备信息结果: %d (%s)",
                static_cast<int>(workData->result), err2str(workData->result));

            if (workData->result == BS_OK) {
                workData->successMessage = "设备信息设置成功";
                BiShareLogger::Info(TAG, "🎉 [直接执行] 设备信息设置成功");
            } else {
                workData->errorMessage = std::string("设备信息设置失败: ") +
                    std::string(err2str(workData->result));
                BiShareLogger::Error(TAG, "❌ [直接执行] 设备信息设置失败: %s",
                    err2str(workData->result));
            }

            BiShareLogger::Info(TAG, "🏁 [直接执行] SetDeviceInfo操作执行完成");
        }

        void DeviceDirectExecutor::ExecuteFindRemoteDevice(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(TAG, "🚀 [直接执行] 开始执行FindRemoteDevice操作");

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务未初始化");
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return;
            }

            // 解析参数
            const std::string& pincode = workData->data.stringParam1;

            BiShareLogger::Info(TAG, "🔧 [直接执行] 查找远程设备...");
            BiShareLogger::Info(TAG, "📍 [直接执行] PIN码: %s", pincode.c_str());

            // 调用原生查找远程设备函数
            workData->result = bishare_service_find_remote_device(pincode.c_str());

            BiShareLogger::Info(TAG, "📊 [直接执行] 查找远程设备结果: %d (%s)",
                static_cast<int>(workData->result), err2str(workData->result));

            if (workData->result == BS_OK) {
                workData->successMessage = "远程设备查找启动成功";
                BiShareLogger::Info(TAG, "🎉 [直接执行] 远程设备查找启动成功");
            } else {
                workData->errorMessage = std::string("远程设备查找失败: ") +
                    std::string(err2str(workData->result));
                BiShareLogger::Error(TAG, "❌ [直接执行] 远程设备查找失败: %s",
                    err2str(workData->result));
            }

            BiShareLogger::Info(TAG, "🏁 [直接执行] FindRemoteDevice操作执行完成");
        }

        void DeviceDirectExecutor::ExecuteSetDeviceModel(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(TAG, "🚀 [直接执行] 开始执行SetDeviceModel操作");

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务未初始化");
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return;
            }

            // 解析参数
            const std::string& deviceModel = workData->data.stringParam1;

            BiShareLogger::Info(TAG, "🔧 [直接执行] 设置设备模型...");
            BiShareLogger::Info(TAG, "📍 [直接执行] 设备模型: %s", deviceModel.c_str());

            // 调用原生设置设备模型函数
            workData->result = bishare_service_set_device_model(deviceModel.c_str());

            BiShareLogger::Info(TAG, "📊 [直接执行] 设置设备模型结果: %d (%s)",
                static_cast<int>(workData->result), err2str(workData->result));

            if (workData->result == BS_OK) {
                workData->successMessage = "设备模型设置成功";
                BiShareLogger::Info(TAG, "🎉 [直接执行] 设备模型设置成功");
            } else {
                workData->errorMessage = std::string("设备模型设置失败: ") +
                    std::string(err2str(workData->result));
                BiShareLogger::Error(TAG, "❌ [直接执行] 设备模型设置失败: %s",
                    err2str(workData->result));
            }

            BiShareLogger::Info(TAG, "🏁 [直接执行] SetDeviceModel操作执行完成");
        }

        void DeviceDirectExecutor::ExecuteGetDeviceModel(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(TAG, "🚀 [直接执行] 开始执行GetDeviceModel操作");

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务未初始化");
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return;
            }

            BiShareLogger::Info(TAG, "🔧 [直接执行] 获取设备模型...");

            // 调用原生获取设备模型函数（返回const char*）
            const char* deviceModel = bishare_service_get_device_model();

            if (deviceModel != nullptr) {
                workData->result = BS_OK;
                workData->successMessage = std::string("设备模型获取成功: ") + std::string(deviceModel);
                // 将结果存储在stringParam1中，供后续处理使用
                workData->data.stringParam1 = std::string(deviceModel);
                BiShareLogger::Info(TAG, "🎉 [直接执行] 设备模型获取成功: %s", deviceModel);
            } else {
                workData->result = BS_PARAMS_ERROR;  // 使用现有的错误码
                workData->errorMessage = "设备模型获取失败：返回空指针";
                BiShareLogger::Error(TAG, "❌ [直接执行] 设备模型获取失败：返回空指针");
            }

            BiShareLogger::Info(TAG, "🏁 [直接执行] GetDeviceModel操作执行完成");
        }

        void DeviceDirectExecutor::ExecuteResetDeviceModel(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(TAG, "🚀 [直接执行] 开始执行ResetDeviceModel操作");

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务未初始化");
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return;
            }

            BiShareLogger::Info(TAG, "🔧 [直接执行] 重置设备模型...");

            // 调用原生重置设备模型函数
            workData->result = bishare_service_reset_device_model();

            BiShareLogger::Info(TAG, "📊 [直接执行] 重置设备模型结果: %d (%s)",
                static_cast<int>(workData->result), err2str(workData->result));

            if (workData->result == BS_OK) {
                workData->successMessage = "设备模型重置成功";
                BiShareLogger::Info(TAG, "🎉 [直接执行] 设备模型重置成功");
            } else {
                workData->errorMessage = std::string("设备模型重置失败: ") +
                    std::string(err2str(workData->result));
                BiShareLogger::Error(TAG, "❌ [直接执行] 设备模型重置失败: %s",
                    err2str(workData->result));
            }

            BiShareLogger::Info(TAG, "🏁 [直接执行] ResetDeviceModel操作执行完成");
        }

        // 全局注册函数
        void RegisterDeviceDirectExecutors() {
            DeviceDirectExecutor::RegisterAll();
        }

    } // namespace BiShare
} // namespace OHOS
