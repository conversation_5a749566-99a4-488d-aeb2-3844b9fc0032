06-15 16:53:23.642  2859-2859    A00201/BiShareFacade           com.aaa.bbb         I  开始初始化BiShareFacade...
06-15 16:53:23.642  2859-2859    A00201/BiShareFacade           com.aaa.bbb         I  创建服务管理器...
06-15 16:53:23.642  2859-2859    A00201/BiShareFacade           com.aaa.bbb         I  服务管理器初始化成功
06-15 16:53:23.642  2859-2859    A00201/BiShareFacade           com.aaa.bbb         I  创建回调管理器...
06-15 16:53:23.642  2859-2859    A00201/BiShareFacade           com.aaa.bbb         I  回调管理器初始化成功
06-15 16:53:23.642  2859-2859    A00201/BiShareFacade           com.aaa.bbb         I  BiShareFacade初始化完成
06-15 16:53:23.642  2859-2859    A00201/RecordingManagerNapi    com.aaa.bbb         I  初始化RecordingManager类
06-15 16:53:23.642  2859-2859    A00201/RecordingManagerNapi    com.aaa.bbb         I  RecordingManager类初始化成功
06-15 16:53:23.642  2859-2859    A00201/DeviceManagerNapi       com.aaa.bbb         I  初始化DeviceManager类
06-15 16:53:23.642  2859-2859    A00201/DeviceManagerNapi       com.aaa.bbb         I  DeviceManager类初始化成功
06-15 16:53:23.642  2859-2859    A00201/BiShareManagerNapi      com.aaa.bbb         I  初始化BiShareManager类
06-15 16:53:23.642  2859-2859    A00201/BiShareManagerNapi      com.aaa.bbb         I  BiShareManager类初始化成功
06-15 16:53:23.644  2859-2859    A03d00/JSAPP                   com.aaa.bbb         I  Callee constructor is OK string
06-15 16:53:23.644  2859-2859    A03d00/JSAPP                   com.aaa.bbb         I  Ability::constructor callee is object [object Object]
06-15 16:53:23.645  2859-2859    C01304/AbilityManagerService   com.aaa.bbb         I  [ui_ability_thread.cpp(AttachInner:170)]LoadLifecycle: Attach uiability.
06-15 16:53:23.645  2859-2859    C057c2/IPCObjectProxy          com.aaa.bbb         I  AddDeathRecipient 393: success, handle:10 desc:ohos.aafwk.AbilityManager
06-15 16:53:23.647  2859-2859    C02b12/AFWK                    com.aaa.bbb         I  [napi_audio_volume_group_manager.cpp] [Construct]Construct() 1
06-15 16:53:23.648  2859-2859    C02b12/AFWK                    com.aaa.bbb         I  [audio_group_manager.cpp] [Init]AudioGroupManager::init set networkId LocalDevice.
06-15 16:53:23.649  2859-2859    C01305/Appkit                  com.aaa.bbb         E  [application_context.cpp(NotifyApplicationForeground:254)]applicationStateCallback is nullptr
06-15 16:53:23.649  2859-2859    C03f00/ArkCompiler             com.aaa.bbb         I  [gc] app is not inBackground
06-15 16:53:23.649  2859-2859    C03f00/ArkCompiler             com.aaa.bbb         I  [gc] Heap Growing Type HIGH_THROUGHPUT
06-15 16:53:23.649  2859-2859    C01707/CONCUR                  com.aaa.bbb         E  [Qos] qoslevel 3 apply for tid 2869 failure
06-15 16:53:23.649  2859-2859    C01707/CONCUR                  com.aaa.bbb         E  [Qos] qoslevel 3 apply for tid 2870 failure
06-15 16:53:23.649  2859-2859    C01707/CONCUR                  com.aaa.bbb         E  [Qos] qoslevel 3 apply for tid 2871 failure
06-15 16:53:23.649  2859-2859    C01707/CONCUR                  com.aaa.bbb         E  [Qos] qoslevel 3 apply for tid 2872 failure
06-15 16:53:23.650  2859-2861    C01304/AbilityManagerService   com.aaa.bbb         I  [ui_ability_thread.cpp(ScheduleAbilityTransaction:344)]Lifecycle: name:EntryAbility,targeState:5,isNewWant:0
06-15 16:53:23.652  2859-2859    C01304/AbilityManagerService   com.aaa.bbb         E  [ui_ability.cpp(HandleCreateAsRecovery:479)]AppRecovery not recovery restart.
06-15 16:53:23.655  2859-2859    A00000/testTag                 com.aaa.bbb         I  Ability onCreate
06-15 16:53:23.655  2859-2859    C01304/AbilityManagerService   com.aaa.bbb         E  [ui_ability_impl.cpp(CheckAndRestore:287)]hasSaveData_ is false.
06-15 16:53:23.655  2859-2859    C01304/AbilityManagerService   com.aaa.bbb         I  [js_ui_ability.cpp(DoOnForegroundForSceneIsNull:725)]JsUIAbility::DoOnForegroundForSceneIsNull displayId  is  0
06-15 16:53:23.655  2859-2859    C04200/WindowScene             com.aaa.bbb         I  <46>Init: [WMSMain]WindowScene init with normal option!
06-15 16:53:23.659  2859-2859    C057c2/IPCObjectProxy          com.aaa.bbb         I  AddDeathRecipient 393: success, handle:15 desc:OHOS.IWindowManager
06-15 16:53:23.662  2859-2859    C057c2/IPCObjectProxy          com.aaa.bbb         I  AddDeathRecipient 393: success, handle:16 desc:ohos.multimodalinput.IConnectManager
06-15 16:53:23.662  2859-2859    C02800/MultimodalInputConnectManager com.aaa.bbb         I  in ConnectMultimodalInputService, Get multimodalinput service successful
06-15 16:53:23.663  2859-2859    C02800/MMIClient               com.aaa.bbb         I  in OnConnected, Connection to server succeeded, fd:47
06-15 16:53:23.663  2859-2859    C02800/MMIClient               com.aaa.bbb         I  in AddFdListener, server was listening
06-15 16:53:23.663  2859-2859    C02800/MMIClient               com.aaa.bbb         I  in StartEventRunner, File fd is in listening
06-15 16:53:23.663  2859-2859    C04200/SingletonContainer      com.aaa.bbb         E  <82>GetSingleton: can not get OHOS::Rosen::WindowInfoReporter
06-15 16:53:23.663  2859-2859    C01304/AbilityManagerService   com.aaa.bbb         E  [ui_ability.cpp(ShouldRecoverState:230)]AppRecovery not recovery restart.
06-15 16:53:23.664  2859-2859    A00000/testTag                 com.aaa.bbb         I  Ability onWindowStageCreate
06-15 16:53:23.664  2859-2878    C057d4/DBinderBaseInvoker      com.aaa.bbb         I  DBinderDatabusInvoker 36: create
06-15 16:53:23.665  2859-2859    C01719/ffrt                    com.aaa.bbb         E  42:TrivalOpenQosCtrlNode:62 task 2859 belong to user 20010032 open qos node failed
06-15 16:53:23.665  2859-2859    C04200/WindowScene             com.aaa.bbb         I  <146>GoForeground: [WMSMain]reason:0
06-15 16:53:23.672  2859-2859    A00000/testTag                 com.aaa.bbb         I  Ability onForeground
06-15 16:53:23.673  2859-2859    A0ff00/BiShare.DeviceManager   com.aaa.bbb         I  DeviceManager created
06-15 16:53:23.673  2859-2859    A0ff00/BiShare.RecordingManager com.aaa.bbb         I  RecordingManager created
06-15 16:53:23.673  2859-2859    A0ff00/BiShare.EventManager    com.aaa.bbb         I  EventManager created
06-15 16:53:23.673  2859-2859    A0ff00/BiShare.BiShareManager  com.aaa.bbb         I  BiShareManager created
06-15 16:53:23.673  2859-2859    A00201/RecordingManagerNapi    com.aaa.bbb         I  获取RecordingManager单例实例
06-15 16:53:23.673  2859-2859    A00201/BiShareCallbacks        com.aaa.bbb         I  🏗️ BiShareCallbacks构造函数开始执行
06-15 16:53:23.673  2859-2859    A00201/BiShareCallbacks        com.aaa.bbb         I  📍 [构造] this指针地址: 0x7f9dda6b38
06-15 16:53:23.673  2859-2859    A00201/BiShareCallbacks        com.aaa.bbb         I  📍 [构造] napiInstance是否有效: 否
06-15 16:53:23.673  2859-2859    A00201/BiShareCallbacks        com.aaa.bbb         I  📍 [构造] 当前静态实例状态: 空
06-15 16:53:23.673  2859-2859    A00201/BiShareCallbacks        com.aaa.bbb         I  ✅ BiShareCallbacks构造函数执行完成
06-15 16:53:23.673  2859-2859    A0001b/InfoService             com.aaa.bbb         I  EventManager --> 添加设备事件监听器
06-15 16:53:23.674  2859-2859    A00201/BiShareNapiInterface    com.aaa.bbb         I  🎯 收到Initialize调用，开始智能路由检测...
06-15 16:53:23.674  2859-2859    A00201/BiShareNapiInterface    com.aaa.bbb         I  📋 Initialize参数数量: 4
06-15 16:53:23.674  2859-2859    A00201/BiShareNapiInterface    com.aaa.bbb         I  🔍 检测最后一个参数类型: 非function
06-15 16:53:23.674  2859-2859    A00201/BiShareNapiInterface    com.aaa.bbb         I  🎯 [路由] 未检测到回调函数，选择Promise模式执行Initialize
06-15 16:53:23.674  2859-2859    A00201/BiShareServiceOps       com.aaa.bbb         I  📋 [参数解析] 开始解析isConsole参数...
06-15 16:53:23.674  2859-2859    A00201/BiShareServiceOps       com.aaa.bbb         I  ✅ [参数解析] isConsole: true
06-15 16:53:23.674  2859-2859    A00201/BiShareServiceOps       com.aaa.bbb         I  📋 [参数解析] 开始解析isFile参数...
06-15 16:53:23.674  2859-2859    A00201/BiShareServiceOps       com.aaa.bbb         I  ✅ [参数解析] isFile: true
06-15 16:53:23.674  2859-2859    A00201/BiShareServiceOps       com.aaa.bbb         I  📋 [参数解析] 开始解析logPath参数...
06-15 16:53:23.674  2859-2859    A00201/BiShareServiceOps       com.aaa.bbb         I  📍 [参数解析] logPath长度: 54
06-15 16:53:23.674  2859-2859    A00201/BiShareServiceOps       com.aaa.bbb         I  ✅ [参数解析] logPath解析成功: /data/storage/el2/base/files/service_1749977603000.log
06-15 16:53:23.674  2859-2859    A00201/BiShareServiceOps       com.aaa.bbb         I  📋 [参数解析] 开始解析priority参数...
06-15 16:53:23.674  2859-2859    A00201/BiShareServiceOps       com.aaa.bbb         I  ✅ [参数解析] priority: 7
06-15 16:53:23.674  2859-2859    A00201/BiShareOperations       com.aaa.bbb         I  🔧 [主线程] CreateAsyncWork开始执行
06-15 16:53:23.674  2859-2859    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] workData地址: 0x7f9e6a3e80
06-15 16:53:23.674  2859-2859    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] 操作名称: Initialize
06-15 16:53:23.674  2859-2859    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] execute回调地址: 0x7e8ad38230
06-15 16:53:23.674  2859-2859    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] complete回调地址: 0x7e8ad38df8
06-15 16:53:23.674  2859-2859    A00201/BiShareOperations       com.aaa.bbb         I  🔄 [CreateAsyncWork] 创建Promise...
06-15 16:53:23.674  2859-2859    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] Promise创建状态: 0
06-15 16:53:23.674  2859-2859    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] deferred地址: 0x7f9d0a1da0
06-15 16:53:23.674  2859-2859    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] promise地址: 0x7fa04902f8
06-15 16:53:23.674  2859-2859    A00201/BiShareOperations       com.aaa.bbb         I  🔄 [CreateAsyncWork] 创建资源名称...
06-15 16:53:23.674  2859-2859    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] 资源名称创建状态: 0
06-15 16:53:23.674  2859-2859    A00201/BiShareOperations       com.aaa.bbb         I  🔄 [CreateAsyncWork] 创建异步工作...
06-15 16:53:23.674  2859-2859    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] 异步工作创建状态: 0
06-15 16:53:23.674  2859-2859    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] async_work地址: 0x7f9d3773d0
06-15 16:53:23.674  2859-2859    A00201/BiShareOperations       com.aaa.bbb         I  🔄 [CreateAsyncWork] 排队异步工作...
06-15 16:53:23.674  2859-2859    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] 异步工作排队状态: 0
06-15 16:53:23.674  2859-2859    A00201/BiShareOperations       com.aaa.bbb         I  ✅ [CreateAsyncWork] 异步工作已成功排队
06-15 16:53:23.674  2859-2859    A00201/BiShareOperations       com.aaa.bbb         I  🏁 [CreateAsyncWork] CreateAsyncWork执行完成
06-15 16:53:23.675  2859-2887    A00201/BiShareOperations       com.aaa.bbb         I  🚀 [后台线程] ExecuteCallback开始执行
06-15 16:53:23.675  2859-2887    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] 线程ID: 548092246448
06-15 16:53:23.675  2859-2887    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] data地址: 0x7f9e6a3e80
06-15 16:53:23.675  2859-2887    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] env地址: 0x7fa0d1f560
06-15 16:53:23.675  2859-2887    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] workData地址: 0x7f9e6a3e80
06-15 16:53:23.675  2859-2887    A00201/BiShareOperations       com.aaa.bbb         I  🔍 [后台线程] 验证workData有效性...
06-15 16:53:23.675  2859-2887    A00201/BiShareOperations       com.aaa.bbb         I  ✅ [后台线程] workData有效，操作名称: Initialize
06-15 16:53:23.675  2859-2887    A00201/BiShareOperations       com.aaa.bbb         I  🔍 [后台线程] 检查Operation实例指针...
06-15 16:53:23.675  2859-2887    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] operation地址: 0x7f9d0a1d70
06-15 16:53:23.675  2859-2887    A00201/BiShareOperations       com.aaa.bbb         I  ✅ [后台线程] Operation实例指针有效，准备调用ExecuteOperation
06-15 16:53:23.675  2859-2887    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] 操作名称: Initialize
06-15 16:53:23.675  2859-2887    A00201/BiShareOperations       com.aaa.bbb         I  🔍 [后台线程] 验证operation指针有效性...
06-15 16:53:23.675  2859-2887    A00201/BiShareOperations       com.aaa.bbb         I  ✅ [后台线程] operation对象验证通过
06-15 16:53:23.675  2859-2887    A00201/BiShareOperations       com.aaa.bbb         I  🔄 [后台线程] 调用 Initialize::ExecuteOperation...
06-15 16:53:23.675  2859-2887    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] 调用前workData->result: 0
06-15 16:53:23.675  2859-2887    A00201/BiShareOperations       com.aaa.bbb         W  ⚠️ [后台线程] Initialize操作使用虚函数调用，可能存在风险
06-15 16:53:23.675  2859-2887    A00201/BiShareOperations       com.aaa.bbb         I  🔄 [后台线程] 调用 Initialize::ExecuteOperation...
06-15 16:53:23.675  2859-2859    C015b0/NetMgrSubsystem         com.aaa.bbb         I  NETMANAGER_BASE [connection_module.cpp 139] ParseNetConnectionParams no params
06-15 16:53:23.675  2859-2887    C03f00/MUSL-SIGCHAIN           com.aaa.bbb         E  signal_chain_handler call 2 rd sigchain action for signal: 6
06-15 16:53:23.675  2859-2887    C02d11/DfxSignalHandler        com.aaa.bbb         I  DFX_SigchainHandler :: sig(6), pid(2859), tid(2887).
06-15 16:53:23.675  2859-2887    C02d11/DfxSignalHandler        com.aaa.bbb         I  Try block main thread.
06-15 16:53:23.675  2859-2859    C02d11/DfxSignalHandler        com.aaa.bbb         I  Crash(3) in child thread(2859), lock main thread.
06-15 16:53:23.675  2859-2887    C02d11/DfxSignalHandler        com.aaa.bbb         I  DFX_SigchainHandler :: sig(6), pid(2859), processName(com.aaa.bbb), threadName(OS_FFRT_2_1).
06-15 16:53:23.676  2859-2878    C057c2/IPCObjectProxy          com.aaa.bbb         I  AddDeathRecipient 393: success, handle:17 desc:OHOS.NetManagerStandard.INetConnService
06-15 16:53:23.686  2859-2887    C02d11/DfxSignalHandler        com.aaa.bbb         I  Start wait for VmProcess(2892) exit.
06-15 16:53:23.686  2859-2887    C02d11/DfxSignalHandler        com.aaa.bbb         I  (2887) wait VmProcess(2892) exit.
06-15 16:53:23.686  2859-2861    C015b0/NetConnManager          com.aaa.bbb         W  [OnNetAvailable-(net_conn_callback_stub.cpp:66)]sent raw data is less than 32k
06-15 16:53:23.687  2859-2861    C015b0/NetMgrSubsystem         com.aaa.bbb         I  NETMANAGER_BASE [net_conn_callback_observer.cpp 36] no event listener find netAvailable
06-15 16:53:23.687  2859-2861    C015b0/NetConnManager          com.aaa.bbb         W  [OnNetCapabilitiesChange-(net_conn_callback_stub.cpp:85)]sent raw data is less than 32k
06-15 16:53:23.687  2859-2861    C015b0/NetMgrSubsystem         com.aaa.bbb         I  NETMANAGER_BASE [net_conn_callback_observer.cpp 56] no event listener find netCapabilitiesChange
06-15 16:53:23.687  2859-2861    C015b0/NetConnManager          com.aaa.bbb         W  [OnNetConnectionPropertiesChange-(net_conn_callback_stub.cpp:135)]sent raw data is less than 32k
06-15 16:53:23.687  2859-2878    C015b0/NetMgrSubsystem         com.aaa.bbb         I  NETMANAGER_BASE [connection_exec.cpp 728] Register result 0
06-15 16:53:23.687  2859-2861    C015b0/NetMgrSubsystem         com.aaa.bbb         I  NETMANAGER_BASE [net_conn_callback_observer.cpp 79] no event listener find netConnectionPropertiesChange
06-15 16:53:23.901  2859-2887    C02d11/DfxSignalHandler        com.aaa.bbb         I  (2887) wait for (2892) return with ret(2892) status(0)
06-15 16:53:23.901  2859-2887    C02d11/DfxSignalHandler        com.aaa.bbb         I  Finish handle signal(6) in 2859:2887
06-15 16:53:23.901  2859-2859    C02d11/DfxSignalHandler        com.aaa.bbb         I  Crash in child thread(2859), exit main thread.
06-15 16:53:23.901  2859-2887    C03f00/MUSL-SIGCHAIN           com.aaa.bbb         E  signal_chain_handler call usr sigaction for signal: 6
06-15 16:53:23.901  2859-2904    C01719/ffrt                    com.aaa.bbb         E  <<<=== ffrt black box(BBOX) start ===>>>
06-15 16:53:23.901  2859-2904    C01719/ffrt                    com.aaa.bbb         E  <<<=== current status ===>>>
06-15 16:53:23.901  2859-2904    C01719/ffrt                    com.aaa.bbb         E  <<<=== task counter ===>>>
06-15 16:53:23.901  2859-2904    C01719/ffrt                    com.aaa.bbb         E  FFRT BBOX TaskSubmitCounter:6 TaskEnQueueCounter:6 TaskDoneCounter:1
06-15 16:53:23.901  2859-2904    C01719/ffrt                    com.aaa.bbb         E  FFRT BBOX TaskRunCounter:6 TaskSwitchCounter:0 TaskFinishCounter:5
06-15 16:53:23.901  2859-2904    C01719/ffrt                    com.aaa.bbb         E  FFRT BBOX TaskWakeCounterInc:0, TaskPendingCounter:0
06-15 16:53:23.901  2859-2904    C01719/ffrt                    com.aaa.bbb         E  TaskRunCounter is not equal to TaskSwitchCounter + TaskFinishCounter
06-15 16:53:23.901  2859-2904    C01719/ffrt                    com.aaa.bbb         E  <<<=== worker status ===>>>
06-15 16:53:23.901  2859-2904    C01719/ffrt                    com.aaa.bbb         E  qos 2: worker tid 2878 is running nothing
06-15 16:53:23.901  2859-2904    C01719/ffrt                    com.aaa.bbb         E  qos 3: worker tid 2881 is running nothing
06-15 16:53:23.901  2859-2904    C01719/ffrt                    com.aaa.bbb         E  <<<=== ready queue status ===>>>
06-15 16:53:23.901  2859-2904    C01719/ffrt                    com.aaa.bbb         E  <<<=== ffrt black box(BBOX) finish ===>>>
06-15 16:53:23.901  2859-2859    C01304/AbilityManagerService   com.aaa.bbb         E  [ui_ability_impl.cpp(operator():387)]applicationContext or lifecycleCallback is nullptr.
06-15 16:53:23.902  2859-2859    C03900/Ace                     com.aaa.bbb         I  [ui_content_impl.cpp(OHOS_ACE_CreateUIContent)-(-1:-1:undefined)] Ace lib loaded, CreateUIContent.
06-15 16:53:23.902  2859-2859    C03934/AceUIEvent              com.aaa.bbb         I  [ui_event_impl.cpp(InitHandler)-(-1:-1:undefined)] report ace loaded
06-15 16:53:23.902  2859-2859    C03f00/MUSL-LDSO               com.aaa.bbb         E  dlopen_impl load library header failed for libha_ace_engine.z.so
06-15 16:53:23.902  2859-2859    C03934/AceUIEvent              com.aaa.bbb         W  [ui_event_impl.cpp(InitHandler)-(-1:-1:undefined)] Failed to open shared library libha_ace_engine.z.so, reason: Error loading shared library libha_ace_engine.z.so: No such file or directoryn
06-15 16:53:23.902  2859-2887    C03f00/MUSL-SIGCHAIN           com.aaa.bbb         E  signal_chain_handler call 2 rd sigchain action for signal: 6
06-15 16:53:23.902  2859-2887    C02d11/DfxSignalHandler        com.aaa.bbb         I  DFX_SigchainHandler :: sig(6), pid(2859), tid(2887).
06-15 16:53:23.902  2859-2887    C03f00/MUSL-SIGCHAIN           com.aaa.bbb         E  signal_chain_handler SIG_DFL handler for signal: 6
06-15 16:53:23.902  2859-2887    C03f00/MUSL-SIGCHAIN           com.aaa.bbb         E  pid(2859) rethrow sig(6) success.
