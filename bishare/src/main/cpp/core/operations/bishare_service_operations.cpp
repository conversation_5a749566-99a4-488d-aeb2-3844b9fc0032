#include "bishare_operation_impls.h"
#include "bishare_napi.h"
#include "bishare_logger.h"
#include "bishare_utils.h"
#include "bishare_callbacks.h"
#include "bishare-service.h"
#include <thread>
#include <sstream>


namespace OHOS {
    namespace BiShare {

        static constexpr const char *SERVICE_OPS_TAG = "BiShareServiceOps";

        // 辅助函数：安全地将线程ID转换为字符串
        static std::string ThreadIdToString(std::thread::id id) {
            std::ostringstream oss;
            oss << id;
            return oss.str();
        }

        // InitializeOperation 实现

        bool InitializeOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 5;
            napi_value argv[5];
            napi_value thisArg;
            void *data;
            
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);
            
            if (argc < 4) {
                BiShareLogger::Error(SERVICE_OPS_TAG, "Initialize参数数量不足，需要4个参数");
                return false;
            }

            // 解析isConsole参数
            BiShareLogger::Info(SERVICE_OPS_TAG, "📋 [参数解析] 开始解析isConsole参数...");
            bool isConsole = false;
            napi_status status = napi_get_value_bool(env, argv[0], &isConsole);
            if (status != napi_ok) {
                BiShareLogger::Error(SERVICE_OPS_TAG, "❌ [参数解析] 获取isConsole参数失败，status: %d", status);
                return false;
            }
            workData->data.boolParam1 = isConsole;
            BiShareLogger::Info(SERVICE_OPS_TAG, "✅ [参数解析] isConsole: %s", isConsole ? "true" : "false");

            // 解析isFile参数
            BiShareLogger::Info(SERVICE_OPS_TAG, "📋 [参数解析] 开始解析isFile参数...");
            bool isFile = false;
            status = napi_get_value_bool(env, argv[1], &isFile);
            if (status != napi_ok) {
                BiShareLogger::Error(SERVICE_OPS_TAG, "❌ [参数解析] 获取isFile参数失败，status: %d", status);
                return false;
            }
            workData->data.boolParam2 = isFile;
            BiShareLogger::Info(SERVICE_OPS_TAG, "✅ [参数解析] isFile: %s", isFile ? "true" : "false");

            // 解析logPath参数
            BiShareLogger::Info(SERVICE_OPS_TAG, "📋 [参数解析] 开始解析logPath参数...");
            size_t logPathLength = 0;
            status = napi_get_value_string_utf8(env, argv[2], nullptr, 0, &logPathLength);
            if (status != napi_ok) {
                BiShareLogger::Error(SERVICE_OPS_TAG, "❌ [参数解析] 获取logPath长度失败，status: %d", status);
                return false;
            }
            BiShareLogger::Info(SERVICE_OPS_TAG, "📍 [参数解析] logPath长度: %zu", logPathLength);

            if (logPathLength == 0) {
                BiShareLogger::Warn(SERVICE_OPS_TAG, "⚠️ [参数解析] logPath为空，使用默认路径");
                workData->data.stringParam1 = "/tmp/bishare.log";
            } else {
                try {
                    char* logPathBuffer = new char[logPathLength + 1];
                    if (logPathBuffer == nullptr) {
                        BiShareLogger::Error(SERVICE_OPS_TAG, "❌ [参数解析] 分配logPath缓冲区失败");
                        return false;
                    }

                    status = napi_get_value_string_utf8(env, argv[2], logPathBuffer, logPathLength + 1, &logPathLength);
                    if (status != napi_ok) {
                        BiShareLogger::Error(SERVICE_OPS_TAG, "❌ [参数解析] 获取logPath内容失败，status: %d", status);
                        delete[] logPathBuffer;
                        return false;
                    }

                    workData->data.stringParam1 = std::string(logPathBuffer);
                    delete[] logPathBuffer;
                    BiShareLogger::Info(SERVICE_OPS_TAG, "✅ [参数解析] logPath解析成功: %s", workData->data.stringParam1.c_str());
                } catch (const std::exception& e) {
                    BiShareLogger::Error(SERVICE_OPS_TAG, "❌ [参数解析] logPath解析异常: %s", e.what());
                    return false;
                }
            }

            // 解析priority参数
            BiShareLogger::Info(SERVICE_OPS_TAG, "📋 [参数解析] 开始解析priority参数...");
            int32_t priority = LOG_INFO;  // 默认值
            status = napi_get_value_int32(env, argv[3], &priority);
            if (status != napi_ok) {
                BiShareLogger::Error(SERVICE_OPS_TAG, "❌ [参数解析] 获取priority参数失败，status: %d", status);
                return false;
            }

            // 验证priority范围
            if (priority < LOG_EMERG || priority > LOG_DEBUG) {
                BiShareLogger::Warn(SERVICE_OPS_TAG, "⚠️ [参数解析] priority值超出范围: %d，使用默认值", priority);
                priority = LOG_INFO;
            }

            workData->data.priority = static_cast<log_priority_t>(priority);
            BiShareLogger::Info(SERVICE_OPS_TAG, "✅ [参数解析] priority: %d", priority);

            // 如果有第5个参数，则为回调函数
            if (argc >= 5) {
                napi_valuetype valueType;
                napi_typeof(env, argv[4], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[4], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void InitializeOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(SERVICE_OPS_TAG, "🚀 [异步] 开始执行InitializeOperation::ExecuteOperation");
            BiShareLogger::Info(SERVICE_OPS_TAG, "📍 [异步初始化] 线程ID: %s",
                ThreadIdToString(std::this_thread::get_id()).c_str());
            BiShareLogger::Info(SERVICE_OPS_TAG, "📍 [异步初始化] workData地址: %p", workData);
            BiShareLogger::Info(SERVICE_OPS_TAG, "📍 [异步初始化] env地址: %p", env);
            BiShareLogger::Info(SERVICE_OPS_TAG, "📍 [异步初始化] operation地址: %p", workData->operation);

            // 安全检查
            if (workData == nullptr) {
                BiShareLogger::Error(SERVICE_OPS_TAG, "❌ [异步初始化] workData为空指针");
                return;
            }

            if (env == nullptr) {
                BiShareLogger::Error(SERVICE_OPS_TAG, "❌ [异步初始化] env为空指针");
                workData->result = BS_PARAMS_ERROR;
                workData->errorMessage = "NAPI环境无效";
                return;
            }

            BiShareLogger::Info(SERVICE_OPS_TAG, "✅ [异步初始化] 参数安全检查通过");

            // 检查是否已初始化
            BiShareLogger::Info(SERVICE_OPS_TAG, "🔍 [异步初始化] 检查服务初始化状态...");
            bool isInitialized = BiShareNapi::IsInitialized();
            BiShareLogger::Info(SERVICE_OPS_TAG, "📍 [异步初始化] 当前初始化状态: %s", isInitialized ? "已初始化" : "未初始化");

            if (isInitialized) {
                BiShareLogger::Info(SERVICE_OPS_TAG, "✅ [异步初始化] BiShare服务已经初始化过了，跳过初始化");
                workData->result = BS_OK;
                workData->successMessage = "服务已经初始化";
                return;
            }

            // 验证和转换参数
            BiShareLogger::Info(SERVICE_OPS_TAG, "🔧 [异步初始化] 验证和转换参数...");
            BiShareLogger::Info(SERVICE_OPS_TAG, "📍 [异步初始化] 原始参数:");
            BiShareLogger::Info(SERVICE_OPS_TAG, "  - boolParam1: %s", workData->data.boolParam1 ? "true" : "false");
            BiShareLogger::Info(SERVICE_OPS_TAG, "  - boolParam2: %s", workData->data.boolParam2 ? "true" : "false");
            BiShareLogger::Info(SERVICE_OPS_TAG, "  - stringParam1: %s", workData->data.stringParam1.c_str());
            BiShareLogger::Info(SERVICE_OPS_TAG, "  - priority: %d", workData->data.priority);

            bool_type_t isConsole = workData->data.boolParam1 ? BOOL_TRUE : BOOL_FALSE;
            bool_type_t isFile = workData->data.boolParam2 ? BOOL_TRUE : BOOL_FALSE;

            BiShareLogger::Info(SERVICE_OPS_TAG, "📍 [异步初始化] 转换后参数:");
            BiShareLogger::Info(SERVICE_OPS_TAG, "  - isConsole: %d", isConsole);
            BiShareLogger::Info(SERVICE_OPS_TAG, "  - isFile: %d", isFile);

            BiShareLogger::Info(SERVICE_OPS_TAG, "🔧 [异步] 开始初始化BiShare服务...");
            BiShareLogger::Info(SERVICE_OPS_TAG, "📋 [异步] 初始化参数详情:");
            BiShareLogger::Info(SERVICE_OPS_TAG, "   - 控制台日志: %s", isConsole == BOOL_TRUE ? "启用" : "禁用");
            BiShareLogger::Info(SERVICE_OPS_TAG, "   - 文件日志: %s", isFile == BOOL_TRUE ? "启用" : "禁用");
            BiShareLogger::Info(SERVICE_OPS_TAG, "   - 日志路径: %s", workData->data.stringParam1.c_str());
            BiShareLogger::Info(SERVICE_OPS_TAG, "   - 日志优先级: %d", workData->data.priority);

            // 调用原生初始化函数
            BiShareLogger::Info(SERVICE_OPS_TAG, "🔄 [异步初始化] 准备调用bishare_service_init...");
            BiShareLogger::Info(SERVICE_OPS_TAG, "📍 [异步初始化] 调用参数验证:");
            BiShareLogger::Info(SERVICE_OPS_TAG, "  - isConsole: %d", isConsole);
            BiShareLogger::Info(SERVICE_OPS_TAG, "  - isFile: %d", isFile);
            BiShareLogger::Info(SERVICE_OPS_TAG, "  - filePath: %s", workData->data.stringParam1.c_str());
            BiShareLogger::Info(SERVICE_OPS_TAG, "  - priority: %d", workData->data.priority);

            // 验证参数有效性
            if (workData->data.stringParam1.empty()) {
                BiShareLogger::Error(SERVICE_OPS_TAG, "❌ [异步初始化] 日志路径为空");
                workData->result = BS_PARAMS_ERROR;
                workData->errorMessage = "日志路径参数无效";
                return;
            }

            try {
                BiShareLogger::Info(SERVICE_OPS_TAG, "🔄 [异步初始化] 调用bishare_service_init...");
                workData->result = bishare_service_init(isConsole, isFile,
                    workData->data.stringParam1.c_str(), workData->data.priority);
                BiShareLogger::Info(SERVICE_OPS_TAG, "✅ [异步初始化] bishare_service_init调用完成");
            } catch (const std::exception& e) {
                BiShareLogger::Error(SERVICE_OPS_TAG, "❌ [异步初始化] bishare_service_init调用异常: %s", e.what());
                workData->result = BS_OPS_ERROR;
                workData->errorMessage = std::string("原生服务初始化异常: ") + e.what();
                return;
            } catch (...) {
                BiShareLogger::Error(SERVICE_OPS_TAG, "❌ [异步初始化] bishare_service_init调用未知异常");
                workData->result = BS_OPS_ERROR;
                workData->errorMessage = "原生服务初始化未知异常";
                return;
            }

            BiShareLogger::Info(SERVICE_OPS_TAG, "📊 [异步] 原生服务初始化结果: %d (%s)",
                static_cast<int>(workData->result), err2str(workData->result));

            // 如果初始化成功，注册回调并设置状态
            if (workData->result == BS_OK) {
                BiShareLogger::Info(SERVICE_OPS_TAG, "✅ [异步] 原生服务初始化成功，设置初始化状态...");
                BiShareNapi::SetInitialized(true);

                // 获取BiShareNapi实例并初始化回调系统
                BiShareLogger::Info(SERVICE_OPS_TAG, "🔗 [异步初始化] 开始初始化回调系统...");
                BiShareLogger::Info(SERVICE_OPS_TAG, "📍 [异步初始化] 获取BiShareNapi实例...");
                auto* napiInstance = BiShareNapi::GetInstance();
                BiShareLogger::Info(SERVICE_OPS_TAG, "📍 [异步初始化] BiShareNapi实例地址: %p", napiInstance);

                if (napiInstance) {
                    BiShareLogger::Info(SERVICE_OPS_TAG, "📍 [异步初始化] 获取回调系统实例...");
                    auto callbacks = napiInstance->GetCallbacks();
                    BiShareLogger::Info(SERVICE_OPS_TAG, "📍 [异步初始化] 回调系统实例地址: %p", callbacks.get());

                    if (callbacks) {
                        BiShareLogger::Info(SERVICE_OPS_TAG, "📍 [异步初始化] 回调系统实例引用计数: %ld", callbacks.use_count());

                        // 设置静态实例
                        BiShareLogger::Info(SERVICE_OPS_TAG, "🔧 [异步初始化] 设置静态回调实例...");
                        BiShareCallbacks::SetStaticInstance(callbacks);

                        // 注册回调
                        BiShareLogger::Info(SERVICE_OPS_TAG, "🔗 [异步初始化] 开始注册事件回调...");
                        BiShareLogger::Info(SERVICE_OPS_TAG, "📍 [异步初始化] 事件回调函数地址: %p", BiShareCallbacks::OnEventCallback);
                        bstatus_t eventResult = bishare_service_register_event_callback(BiShareCallbacks::OnEventCallback);
                        BiShareLogger::Info(SERVICE_OPS_TAG, "📊 [异步初始化] 事件回调注册结果: %d (%s)",
                            static_cast<int>(eventResult), err2str(eventResult));

                        BiShareLogger::Info(SERVICE_OPS_TAG, "🔗 [异步初始化] 开始注册数据包回调...");
                        BiShareLogger::Info(SERVICE_OPS_TAG, "📍 [异步初始化] 数据包回调函数地址: %p", BiShareCallbacks::OnPacketCallback);
                        bstatus_t packetResult = bishare_service_register_packet_callback(BiShareCallbacks::OnPacketCallback);
                        BiShareLogger::Info(SERVICE_OPS_TAG, "📊 [异步初始化] 数据包回调注册结果: %d (%s)",
                            static_cast<int>(packetResult), err2str(packetResult));

                        BiShareLogger::Info(SERVICE_OPS_TAG, "📊 [异步初始化] 回调注册汇总 - 事件: %d, 数据包: %d",
                            static_cast<int>(eventResult), static_cast<int>(packetResult));

                        if (eventResult == BS_OK && packetResult == BS_OK) {
                            BiShareLogger::Info(SERVICE_OPS_TAG, "✅ [异步初始化] 所有回调注册成功");
                        } else {
                            BiShareLogger::Warn(SERVICE_OPS_TAG, "⚠️ [异步初始化] 部分回调注册失败");
                        }
                    } else {
                        BiShareLogger::Error(SERVICE_OPS_TAG, "❌ [异步初始化] 回调系统实例为空");
                    }
                } else {
                    BiShareLogger::Error(SERVICE_OPS_TAG, "❌ [异步初始化] BiShareNapi实例为空");
                }

                workData->successMessage = "BiShare服务初始化成功";
                BiShareLogger::Info(SERVICE_OPS_TAG, "🎉 [异步] BiShare服务初始化完全成功，回调已注册");
            } else {
                workData->errorMessage = std::string("BiShare服务初始化失败: ") +
                    std::string(err2str(workData->result));
                BiShareLogger::Error(SERVICE_OPS_TAG, "❌ [异步] BiShare服务初始化失败: %s",
                    err2str(workData->result));
            }

            BiShareLogger::Info(SERVICE_OPS_TAG, "🏁 [异步] InitializeOperation::ExecuteOperation 执行完成");
        }

        napi_value InitializeOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

        // 同步执行方法实现
        bool InitializeOperation::ParseArgumentsSync(napi_env env, napi_callback_info info) {
            size_t argc = 5;
            napi_value argv[5];
            napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

            if (argc < 4) {
                BiShareLogger::Error(SERVICE_OPS_TAG, "Initialize同步模式参数数量不足，需要4个参数");
                return false;
            }

            return true;
        }

        napi_value InitializeOperation::ExecuteOperationSync(napi_env env, napi_callback_info info) {
            try {
                BiShareLogger::Info(SERVICE_OPS_TAG, "🚀 [同步] 开始执行InitializeOperation::ExecuteOperationSync");

                // 检查是否已初始化
                if (BiShareNapi::IsInitialized()) {
                    BiShareLogger::Info(SERVICE_OPS_TAG, "✅ [同步] BiShare服务已经初始化过了，直接返回成功");
                    napi_value success;
                    napi_get_boolean(env, true, &success);
                    return success;
                }

                BiShareLogger::Info(SERVICE_OPS_TAG, "🔧 [同步] 开始同步初始化BiShare服务...");

                // 解析参数
                BiShareLogger::Info(SERVICE_OPS_TAG, "📋 [同步] 开始解析初始化参数...");
                size_t argc = 5;
                napi_value argv[5];
                napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

                // 解析isConsole参数
                bool isConsole;
                napi_get_value_bool(env, argv[0], &isConsole);
                BiShareLogger::Info(SERVICE_OPS_TAG, "   - 控制台日志参数: %s", isConsole ? "启用" : "禁用");

                // 解析isFile参数
                bool isFile;
                napi_get_value_bool(env, argv[1], &isFile);
                BiShareLogger::Info(SERVICE_OPS_TAG, "   - 文件日志参数: %s", isFile ? "启用" : "禁用");

                // 解析logPath参数
                size_t logPathLength;
                napi_get_value_string_utf8(env, argv[2], nullptr, 0, &logPathLength);
                char* logPathBuffer = new char[logPathLength + 1];
                napi_get_value_string_utf8(env, argv[2], logPathBuffer, logPathLength + 1, &logPathLength);
                std::string logPath(logPathBuffer);
                delete[] logPathBuffer;
                BiShareLogger::Info(SERVICE_OPS_TAG, "   - 日志路径参数: %s", logPath.c_str());

                // 解析priority参数
                int32_t priority;
                napi_get_value_int32(env, argv[3], &priority);
                BiShareLogger::Info(SERVICE_OPS_TAG, "   - 日志优先级参数: %d", priority);

                // 转换参数
                bool_type_t isConsoleParam = isConsole ? BOOL_TRUE : BOOL_FALSE;
                bool_type_t isFileParam = isFile ? BOOL_TRUE : BOOL_FALSE;

                BiShareLogger::Info(SERVICE_OPS_TAG, "📋 [同步] 参数解析完成，准备调用原生服务...");

                // 直接调用原生初始化函数
                BiShareLogger::Info(SERVICE_OPS_TAG, "🔄 [同步] 调用原生服务初始化函数 bishare_service_init...");
                bstatus_t result = bishare_service_init(isConsoleParam, isFileParam,
                    logPath.c_str(), static_cast<log_priority_t>(priority));

                BiShareLogger::Info(SERVICE_OPS_TAG, "📊 [同步] 原生服务初始化结果: %d (%s)",
                    static_cast<int>(result), err2str(result));

                if (result == BS_OK) {
                    BiShareNapi::SetInitialized(true);

                    // 获取BiShareNapi实例并初始化回调系统
                    auto* napiInstance = BiShareNapi::GetInstance();
                    if (napiInstance && napiInstance->GetCallbacks()) {
                        // 设置静态实例
                        BiShareCallbacks::SetStaticInstance(napiInstance->GetCallbacks());

                        // 注册回调
                        bstatus_t eventResult = bishare_service_register_event_callback(BiShareCallbacks::OnEventCallback);
                        bstatus_t packetResult = bishare_service_register_packet_callback(BiShareCallbacks::OnPacketCallback);

                        BiShareLogger::Info(SERVICE_OPS_TAG, "同步BiShare服务初始化成功，回调已注册 - 事件: %d, 数据包: %d",
                            static_cast<int>(eventResult), static_cast<int>(packetResult));
                    } else {
                        BiShareLogger::Error(SERVICE_OPS_TAG, "同步模式：无法获取NAPI实例或回调系统");
                    }

                    napi_value success;
                    napi_get_boolean(env, true, &success);
                    return success;
                } else {
                    std::string errorMsg = std::string("BiShare服务初始化失败: ") + std::string(err2str(result));
                    BiShareLogger::Error(SERVICE_OPS_TAG, "同步BiShare服务初始化失败: %s", err2str(result));

                    napi_value error, message;
                    napi_create_string_utf8(env, errorMsg.c_str(), NAPI_AUTO_LENGTH, &message);
                    napi_create_error(env, nullptr, message, &error);
                    return error;
                }

            } catch (const std::exception& e) {
                BiShareLogger::Error(SERVICE_OPS_TAG, "同步初始化异常: %s", e.what());
                napi_value error, message;
                napi_create_string_utf8(env, e.what(), NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
        }

        // ReleaseOperation 实现

        bool ReleaseOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 1;
            napi_value argv[1];
            napi_value thisArg;
            void *data;
            
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);
            
            // 如果有回调函数参数
            if (argc >= 1) {
                napi_valuetype valueType;
                napi_typeof(env, argv[0], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[0], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void ReleaseOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(SERVICE_OPS_TAG, "🚀 [异步] 开始执行ReleaseOperation::ExecuteOperation");

            // 检查是否已初始化
            if (!CheckServiceInitialized(workData)) {
                BiShareLogger::Error(SERVICE_OPS_TAG, "❌ [异步] BiShare服务未初始化，无法释放");
                return;
            }

            BiShareLogger::Info(SERVICE_OPS_TAG, "🔧 [异步] 开始释放BiShare服务...");

            // 调用原生释放函数
            BiShareLogger::Info(SERVICE_OPS_TAG, "🔄 [异步] 调用原生服务释放函数 bishare_service_release...");
            workData->result = bishare_service_release();

            BiShareLogger::Info(SERVICE_OPS_TAG, "📊 [异步] 原生服务释放结果: %d (%s)",
                static_cast<int>(workData->result), err2str(workData->result));

            if (workData->result == BS_OK) {
                BiShareLogger::Info(SERVICE_OPS_TAG, "✅ [异步] 原生服务释放成功，设置初始化状态为false...");
                BiShareNapi::SetInitialized(false);
                workData->successMessage = "BiShare服务释放成功";
                BiShareLogger::Info(SERVICE_OPS_TAG, "🎉 [异步] BiShare服务释放完全成功");
            } else {
                workData->errorMessage = std::string("BiShare服务释放失败: ") +
                    std::string(err2str(workData->result));
                BiShareLogger::Error(SERVICE_OPS_TAG, "❌ [异步] BiShare服务释放失败: %s",
                    err2str(workData->result));
            }

            BiShareLogger::Info(SERVICE_OPS_TAG, "🏁 [异步] ReleaseOperation::ExecuteOperation 执行完成");
        }

        napi_value ReleaseOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

        // 同步执行方法实现
        bool ReleaseOperation::ParseArgumentsSync(napi_env env, napi_callback_info info) {
            // Release操作不需要额外参数
            return true;
        }

        napi_value ReleaseOperation::ExecuteOperationSync(napi_env env, napi_callback_info info) {
            try {
                BiShareLogger::Info(SERVICE_OPS_TAG, "🚀 [同步] 开始执行ReleaseOperation::ExecuteOperationSync");

                // 检查是否已初始化
                if (!BiShareNapi::IsInitialized()) {
                    BiShareLogger::Error(SERVICE_OPS_TAG, "❌ [同步] BiShare服务未初始化，无法释放");
                    napi_value error, message;
                    napi_create_string_utf8(env, "BiShare服务未初始化", NAPI_AUTO_LENGTH, &message);
                    napi_create_error(env, nullptr, message, &error);
                    return error;
                }

                BiShareLogger::Info(SERVICE_OPS_TAG, "🔧 [同步] 开始同步释放BiShare服务...");

                // 直接调用原生释放函数
                BiShareLogger::Info(SERVICE_OPS_TAG, "🔄 [同步] 调用原生服务释放函数 bishare_service_release...");
                bstatus_t result = bishare_service_release();

                BiShareLogger::Info(SERVICE_OPS_TAG, "📊 [同步] 原生服务释放结果: %d (%s)",
                    static_cast<int>(result), err2str(result));

                if (result == BS_OK) {
                    BiShareLogger::Info(SERVICE_OPS_TAG, "✅ [同步] 原生服务释放成功，设置初始化状态为false...");
                    BiShareNapi::SetInitialized(false);
                    BiShareLogger::Info(SERVICE_OPS_TAG, "🎉 [同步] BiShare服务释放完全成功");

                    napi_value success;
                    napi_get_boolean(env, true, &success);
                    return success;
                } else {
                    std::string errorMsg = std::string("BiShare服务释放失败: ") + std::string(err2str(result));
                    BiShareLogger::Error(SERVICE_OPS_TAG, "❌ [同步] BiShare服务释放失败: %s", err2str(result));

                    napi_value error, message;
                    napi_create_string_utf8(env, errorMsg.c_str(), NAPI_AUTO_LENGTH, &message);
                    napi_create_error(env, nullptr, message, &error);
                    return error;
                }

            } catch (const std::exception& e) {
                BiShareLogger::Error(SERVICE_OPS_TAG, "❌ [同步] 同步释放异常: %s", e.what());
                napi_value error, message;
                napi_create_string_utf8(env, e.what(), NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
        }

        // OnEventOperation 实现

        napi_value OnEventOperation::Execute(napi_env env, napi_callback_info info) {
            // 这是一个同步操作，直接处理事件监听注册
            size_t argc = 2;
            napi_value argv[2];
            napi_value thisArg;
            void *data;
            
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);
            
            if (argc < 2) {
                napi_value error, message;
                napi_create_string_utf8(env, "OnEvent需要2个参数：事件类型和回调函数", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            // 获取事件类型
            int32_t eventType;
            napi_get_value_int32(env, argv[0], &eventType);

            // 检查回调函数
            napi_valuetype valueType;
            napi_typeof(env, argv[1], &valueType);
            if (valueType != napi_function) {
                napi_value error, message;
                napi_create_string_utf8(env, "第二个参数必须是回调函数", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            // 注册事件回调
            auto callbacks = BiShareNapi::GetInstance()->GetCallbacks();
            napi_ref callbackRef;
            bool success = callbacks->RegisterEventCallback(env, argv[1], eventType, &callbackRef);

            if (success) {
                BiShareLogger::Info(SERVICE_OPS_TAG, "事件监听注册成功，类型: %d", eventType);
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            } else {
                napi_value error, message;
                napi_create_string_utf8(env, "事件监听注册失败", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
        }

        bool OnEventOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            // 这个方法在同步执行中不会被调用
            return true;
        }

        void OnEventOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 这个方法在同步执行中不会被调用
        }

        napi_value OnEventOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            // 这个方法在同步执行中不会被调用
            napi_value result;
            napi_get_undefined(env, &result);
            return result;
        }

        // OffEventOperation 实现

        napi_value OffEventOperation::Execute(napi_env env, napi_callback_info info) {
            // 这是一个同步操作，直接处理事件监听注销
            size_t argc = 2;
            napi_value argv[2];
            napi_value thisArg;
            void *data;

            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

            if (argc < 2) {
                napi_value error, message;
                napi_create_string_utf8(env, "OffEvent需要2个参数：事件类型和回调函数", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            // 解析事件类型
            int32_t eventType;
            napi_valuetype valueType;
            napi_typeof(env, argv[0], &valueType);
            if (valueType != napi_number) {
                napi_value error, message;
                napi_create_string_utf8(env, "第一个参数必须是数字类型的事件类型", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
            napi_get_value_int32(env, argv[0], &eventType);

            // 验证回调函数
            napi_typeof(env, argv[1], &valueType);
            if (valueType != napi_function) {
                napi_value error, message;
                napi_create_string_utf8(env, "第二个参数必须是回调函数", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            // 注销事件回调
            auto callbacks = BiShareNapi::GetInstance()->GetCallbacks();
            napi_ref callbackRef;
            napi_create_reference(env, argv[1], 1, &callbackRef);
            bool success = callbacks->UnregisterEventCallback(env, callbackRef, eventType);

            if (success) {
                BiShareLogger::Info(SERVICE_OPS_TAG, "事件监听注销成功，类型: %d", eventType);
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            } else {
                napi_value error, message;
                napi_create_string_utf8(env, "事件监听注销失败", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
        }

        bool OffEventOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            // 这个方法在同步执行中不会被调用
            return true;
        }

        void OffEventOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 这个方法在同步执行中不会被调用
        }

        napi_value OffEventOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            // 这个方法在同步执行中不会被调用
            napi_value result;
            napi_get_undefined(env, &result);
            return result;
        }

        // OnceEventOperation 实现

        napi_value OnceEventOperation::Execute(napi_env env, napi_callback_info info) {
            // 这是一个同步操作，直接处理一次性事件监听注册
            size_t argc = 2;
            napi_value argv[2];
            napi_value thisArg;
            void *data;

            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

            if (argc < 2) {
                napi_value error, message;
                napi_create_string_utf8(env, "OnceEvent需要2个参数：事件类型和回调函数", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            // 解析事件类型
            int32_t eventType;
            napi_valuetype valueType;
            napi_typeof(env, argv[0], &valueType);
            if (valueType != napi_number) {
                napi_value error, message;
                napi_create_string_utf8(env, "第一个参数必须是数字类型的事件类型", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
            napi_get_value_int32(env, argv[0], &eventType);

            // 验证回调函数
            napi_typeof(env, argv[1], &valueType);
            if (valueType != napi_function) {
                napi_value error, message;
                napi_create_string_utf8(env, "第二个参数必须是回调函数", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            // 注册一次性事件回调（使用普通事件回调，但标记为一次性）
            auto callbacks = BiShareNapi::GetInstance()->GetCallbacks();
            napi_ref callbackRef;
            bool success = callbacks->RegisterEventCallback(env, argv[1], eventType, &callbackRef);

            if (success) {
                BiShareLogger::Info(SERVICE_OPS_TAG, "一次性事件监听注册成功，类型: %d", eventType);
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            } else {
                napi_value error, message;
                napi_create_string_utf8(env, "一次性事件监听注册失败", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
        }

        bool OnceEventOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            // 这个方法在同步执行中不会被调用
            return true;
        }

        void OnceEventOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 这个方法在同步执行中不会被调用
        }

        napi_value OnceEventOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            // 这个方法在同步执行中不会被调用
            napi_value result;
            napi_get_undefined(env, &result);
            return result;
        }

    } // namespace BiShare
} // namespace OHOS
