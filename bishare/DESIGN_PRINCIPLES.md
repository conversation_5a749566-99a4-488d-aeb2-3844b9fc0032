# BiShare模块设计原理

## 🎯 设计目标

BiShare模块是一个基于OpenHarmony NAPI的跨设备文件共享和通信框架，旨在提供：

1. **统一的设备发现和连接机制**
2. **高效的文件传输能力**
3. **实时的消息通信功能**
4. **完整的事件监听体系**
5. **灵活的录制和回放功能**

## 🏗️ 架构设计原理

### 1. 分层架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Entry Module)                      │
├─────────────────────────────────────────────────────────────┤
│                  TypeScript接口层                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ DeviceManager│ │NetworkManager│ │RecordingMgr │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    NAPI桥接层                               │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              BiShareNapiInterface                       │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    C++核心层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   设备管理   │ │   网络通信   │ │   文件传输   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

### 2. 核心设计原则

#### 2.1 单一职责原则 (SRP)
- **BiShareManager**: 统一的入口管理器，负责整体生命周期
- **DeviceManager**: 专门负责设备发现、连接、状态管理
- **NetworkManager**: 专门负责网络配置和状态监控
- **RecordingManager**: 专门负责录制和回放功能
- **FileManager**: 专门负责文件传输和管理

#### 2.2 开闭原则 (OCP)
- 通过Operation模式支持功能扩展
- 新功能通过新增Operation类实现，无需修改核心代码
- 事件系统支持新事件类型的动态注册

#### 2.3 依赖倒置原则 (DIP)
- 高层模块不依赖低层模块，都依赖抽象
- NAPI层定义抽象接口，C++层实现具体功能
- 通过接口隔离不同层次的依赖

#### 2.4 接口隔离原则 (ISP)
- 不同功能模块定义独立的接口
- 客户端只依赖它需要的接口
- 避免接口污染和不必要的依赖

## 🔧 核心组件设计

### 1. BiShareManager (核心管理器)

```typescript
class BiShareManager {
  // 单例模式，确保全局唯一实例
  static getInstance(): BiShareManager
  
  // 生命周期管理
  initialize(options: InitOptions): Promise<boolean>
  release(): Promise<boolean>
  
  // 功能模块获取
  getDeviceManager(): DeviceManager
  getNetworkManager(): NetworkManager
  getRecordingManager(): RecordingManager
}
```

**设计理念**:
- **统一入口**: 所有功能通过BiShareManager访问
- **生命周期管理**: 统一管理所有子模块的初始化和释放
- **资源协调**: 协调各模块间的资源使用和状态同步

### 2. Operation模式设计

```typescript
abstract class BaseOperation<T> {
  abstract execute(): Promise<BiShareResult<T>>
  abstract executeSync(): BiShareResult<T>
}

// 具体实现
class GetDiscoveredDevicesOperation extends BaseOperation<ExtendedDeviceInfo[]> {
  execute(): Promise<BiShareResult<ExtendedDeviceInfo[]>>
  executeSync(): BiShareResult<ExtendedDeviceInfo[]>
}
```

**设计理念**:
- **命令模式**: 将请求封装为对象，支持撤销、重做等操作
- **同步/异步统一**: 同一操作支持同步和异步两种执行方式
- **结果标准化**: 统一的结果格式，便于错误处理和状态管理

### 3. 事件系统设计

```typescript
// 事件类型枚举
enum EventType {
  DEVICE_DISCOVERED = 'device_discovered',
  DEVICE_CONNECTED = 'device_connected',
  FILE_TRANSFER_PROGRESS = 'file_transfer_progress'
}

// 事件监听器接口
interface EventListener<T> {
  onEvent(eventType: EventType, data: T): void
}

// 事件管理器
class EventManager {
  addEventListener<T>(eventType: EventType, listener: EventListener<T>): void
  removeEventListener<T>(eventType: EventType, listener: EventListener<T>): void
  dispatchEvent<T>(eventType: EventType, data: T): void
}
```

**设计理念**:
- **观察者模式**: 支持多个监听器订阅同一事件
- **类型安全**: 强类型的事件数据，编译时检查
- **解耦设计**: 事件发布者和订阅者完全解耦

## 🔄 数据流设计

### 1. 请求处理流程

```
应用层请求 → TypeScript接口 → NAPI桥接 → C++实现 → 系统调用
     ↓
结果返回 ← TypeScript接口 ← NAPI桥接 ← C++实现 ← 系统响应
```

### 2. 事件传播流程

```
系统事件 → C++事件处理 → NAPI事件桥接 → TypeScript事件分发 → 应用层回调
```

### 3. 状态同步机制

```
C++状态变更 → NAPI状态同步 → TypeScript状态更新 → 应用层状态通知
```

## 🛡️ 错误处理设计

### 1. 分层错误处理

```typescript
interface BiShareError {
  code: string          // 错误代码
  message: string       // 错误描述
  details?: any        // 详细信息
  stack?: string       // 调用栈
}

interface BiShareResult<T> {
  success: boolean
  data?: T
  error?: BiShareError
}
```

### 2. 错误传播机制

- **C++层**: 使用异常和错误码
- **NAPI层**: 转换为JavaScript异常
- **TypeScript层**: 封装为BiShareResult
- **应用层**: 统一的错误处理接口

## 🔒 线程安全设计

### 1. 线程模型

- **主线程**: UI操作和轻量级同步操作
- **工作线程**: 文件传输、网络通信等耗时操作
- **事件线程**: 事件处理和回调执行

### 2. 同步机制

- **互斥锁**: 保护共享资源
- **读写锁**: 优化读多写少的场景
- **原子操作**: 简单状态的无锁更新

## 📊 性能优化设计

### 1. 内存管理

- **对象池**: 复用频繁创建的对象
- **智能指针**: 自动内存管理
- **缓存机制**: 减少重复计算和IO操作

### 2. 网络优化

- **连接复用**: 复用TCP连接
- **数据压缩**: 减少传输数据量
- **并发控制**: 限制并发连接数

### 3. 文件传输优化

- **分块传输**: 大文件分块处理
- **断点续传**: 支持传输中断恢复
- **校验机制**: 确保数据完整性

## 🔮 扩展性设计

### 1. 插件机制

- 支持动态加载新功能模块
- 标准化的插件接口
- 插件生命周期管理

### 2. 协议扩展

- 可插拔的通信协议
- 向后兼容的版本管理
- 协议协商机制

### 3. 平台适配

- 抽象的平台接口层
- 平台特定的实现
- 统一的上层API

## 📝 设计模式应用

1. **单例模式**: BiShareManager全局唯一实例
2. **工厂模式**: Operation对象的创建
3. **观察者模式**: 事件监听和通知
4. **命令模式**: Operation操作封装
5. **适配器模式**: 不同平台的接口适配
6. **策略模式**: 不同传输协议的选择
7. **状态模式**: 设备连接状态管理

这种设计确保了BiShare模块的高内聚、低耦合，同时具备良好的可扩展性和可维护性。

## 📚 相关文档

- [BiShare模块接入指南](./INTEGRATION_GUIDE.md) - 详细的功能扩展和接入方式
- [API参考文档](./API_REFERENCE.md) - 完整的API接口说明
- [开发者指南](./DEVELOPER_GUIDE.md) - 开发最佳实践和注意事项
