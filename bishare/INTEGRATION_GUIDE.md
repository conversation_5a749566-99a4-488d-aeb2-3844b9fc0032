# BiShare模块接入指南

## 🎯 概述

本文档详细说明如何在BiShare模块中新增管理类、方法和功能。BiShare模块采用分层架构和Operation模式，支持灵活的功能扩展。

## 🏗️ 接入架构

### 接入层次结构

```
1. TypeScript接口层 (src/main/ets/)
   ├── interfaces/     # 接口定义
   ├── managers/       # 管理器实现
   └── operations/     # 操作类实现

2. NAPI桥接层 (src/main/cpp/)
   ├── napi_interface/ # NAPI接口实现
   └── operations/     # C++操作类

3. C++核心层 (src/main/cpp/core/)
   ├── managers/       # 核心管理器
   └── services/       # 底层服务
```

## 📋 接入步骤

### 1. 新增管理器类

#### 步骤1: 定义TypeScript接口

在 `src/main/ets/interfaces/` 目录下创建接口文件：

```typescript
// src/main/ets/interfaces/INewManager.ets
export interface INewManager {
  /**
   * 初始化管理器
   */
  initialize(): Promise<boolean>;
  
  /**
   * 释放管理器资源
   */
  release(): Promise<boolean>;
  
  /**
   * 新功能方法
   */
  newFunction(param: string): Promise<BiShareResult<string>>;
  
  /**
   * 同步版本的新功能方法
   */
  newFunctionSync(param: string): BiShareResult<string>;
}
```

#### 步骤2: 实现TypeScript管理器

在 `src/main/ets/managers/` 目录下创建管理器实现：

```typescript
// src/main/ets/managers/NewManager.ets
import { INewManager } from '../interfaces/INewManager';
import { BiShareResult, BiShareError } from '../interfaces/BiShareTypes';
import { NewFunctionOperation } from '../operations/NewFunctionOperation';

export class NewManager implements INewManager {
  private isInitialized: boolean = false;
  
  async initialize(): Promise<boolean> {
    try {
      // 初始化逻辑
      this.isInitialized = true;
      return true;
    } catch (error) {
      return false;
    }
  }
  
  async release(): Promise<boolean> {
    try {
      // 释放资源逻辑
      this.isInitialized = false;
      return true;
    } catch (error) {
      return false;
    }
  }
  
  async newFunction(param: string): Promise<BiShareResult<string>> {
    if (!this.isInitialized) {
      return {
        success: false,
        error: {
          code: 'MANAGER_NOT_INITIALIZED',
          message: 'NewManager not initialized'
        }
      };
    }
    
    const operation = new NewFunctionOperation(param);
    return await operation.execute();
  }
  
  newFunctionSync(param: string): BiShareResult<string> {
    if (!this.isInitialized) {
      return {
        success: false,
        error: {
          code: 'MANAGER_NOT_INITIALIZED',
          message: 'NewManager not initialized'
        }
      };
    }
    
    const operation = new NewFunctionOperation(param);
    return operation.executeSync();
  }
}
```

#### 步骤3: 创建Operation类

在 `src/main/ets/operations/` 目录下创建操作类：

```typescript
// src/main/ets/operations/NewFunctionOperation.ets
import { BaseOperation } from './BaseOperation';
import { BiShareResult } from '../interfaces/BiShareTypes';

export class NewFunctionOperation extends BaseOperation<string> {
  private param: string;
  
  constructor(param: string) {
    super();
    this.param = param;
  }
  
  async execute(): Promise<BiShareResult<string>> {
    try {
      // 调用NAPI接口
      const result = await this.callNativeFunction('newFunction', this.param);
      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'OPERATION_FAILED',
          message: error.message
        }
      };
    }
  }
  
  executeSync(): BiShareResult<string> {
    try {
      // 调用同步NAPI接口
      const result = this.callNativeFunctionSync('newFunctionSync', this.param);
      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'OPERATION_FAILED',
          message: error.message
        }
      };
    }
  }
}
```

#### 步骤4: 实现NAPI接口

在 `src/main/cpp/napi_interface/` 目录下添加NAPI实现：

```cpp
// src/main/cpp/napi_interface/new_manager_napi.cpp
#include "new_manager_napi.h"
#include "../core/managers/NewManagerCore.h"

napi_value NewManagerNapi::NewFunction(napi_env env, napi_callback_info info) {
    size_t argc = 1;
    napi_value args[1];
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);
    
    // 获取参数
    std::string param = GetStringFromNapi(env, args[0]);
    
    // 调用核心实现
    auto result = NewManagerCore::GetInstance()->NewFunction(param);
    
    // 返回结果
    return CreateStringNapi(env, result);
}

napi_value NewManagerNapi::NewFunctionSync(napi_env env, napi_callback_info info) {
    // 同步版本实现
    return NewFunction(env, info);
}
```

#### 步骤5: 实现C++核心逻辑

在 `src/main/cpp/core/managers/` 目录下创建核心管理器：

```cpp
// src/main/cpp/core/managers/NewManagerCore.h
#pragma once
#include <string>
#include <memory>

class NewManagerCore {
public:
    static std::shared_ptr<NewManagerCore> GetInstance();
    
    bool Initialize();
    bool Release();
    std::string NewFunction(const std::string& param);
    
private:
    NewManagerCore() = default;
    static std::shared_ptr<NewManagerCore> instance_;
    bool isInitialized_ = false;
};

// src/main/cpp/core/managers/NewManagerCore.cpp
#include "NewManagerCore.h"

std::shared_ptr<NewManagerCore> NewManagerCore::instance_ = nullptr;

std::shared_ptr<NewManagerCore> NewManagerCore::GetInstance() {
    if (!instance_) {
        instance_ = std::shared_ptr<NewManagerCore>(new NewManagerCore());
    }
    return instance_;
}

bool NewManagerCore::Initialize() {
    // 初始化逻辑
    isInitialized_ = true;
    return true;
}

bool NewManagerCore::Release() {
    // 释放逻辑
    isInitialized_ = false;
    return true;
}

std::string NewManagerCore::NewFunction(const std::string& param) {
    if (!isInitialized_) {
        throw std::runtime_error("NewManagerCore not initialized");
    }
    
    // 实际功能实现
    return "Processed: " + param;
}
```

#### 步骤6: 注册到BiShareManager

在 `BiShareManager.ets` 中添加新管理器：

```typescript
// src/main/ets/core/BiShareManager.ets
import { NewManager } from '../managers/NewManager';

export class BiShareManager {
  private newManager: NewManager;
  
  constructor() {
    this.newManager = new NewManager();
  }
  
  async initialize(options: InitOptions): Promise<boolean> {
    // 初始化新管理器
    const newManagerInit = await this.newManager.initialize();
    if (!newManagerInit) {
      return false;
    }
    
    return true;
  }
  
  async release(): Promise<boolean> {
    // 释放新管理器
    await this.newManager.release();
    return true;
  }
  
  getNewManager(): NewManager {
    return this.newManager;
  }
}
```

### 2. 新增单独方法

#### 步骤1: 在现有管理器中添加方法

```typescript
// 在现有管理器中添加新方法
export class DeviceManager {
  async newDeviceFunction(deviceId: string): Promise<BiShareResult<DeviceInfo>> {
    const operation = new NewDeviceFunctionOperation(deviceId);
    return await operation.execute();
  }
}
```

#### 步骤2: 创建对应的Operation

```typescript
// src/main/ets/operations/NewDeviceFunctionOperation.ets
export class NewDeviceFunctionOperation extends BaseOperation<DeviceInfo> {
  // 实现具体逻辑
}
```

#### 步骤3: 添加NAPI接口和C++实现

按照上述步骤添加相应的NAPI和C++实现。

### 3. 新增事件类型

#### 步骤1: 定义事件类型

```typescript
// src/main/ets/interfaces/BiShareTypes.ets
export enum EventType {
  // 现有事件...
  NEW_EVENT = 'new_event'
}

export interface NewEventData {
  eventId: string;
  eventData: any;
}
```

#### 步骤2: 创建事件监听器接口

```typescript
// src/main/ets/interfaces/INewEventListener.ets
export interface INewEventListener {
  onNewEvent(data: NewEventData): void;
}
```

#### 步骤3: 在EventManager中添加支持

```typescript
// src/main/ets/managers/EventManager.ets
export class EventManager {
  private newEventListeners: Set<INewEventListener> = new Set();
  
  addNewEventListener(listener: INewEventListener): void {
    this.newEventListeners.add(listener);
  }
  
  removeNewEventListener(listener: INewEventListener): void {
    this.newEventListeners.delete(listener);
  }
  
  private handleNewEvent(data: NewEventData): void {
    this.newEventListeners.forEach(listener => {
      listener.onNewEvent(data);
    });
  }
}
```

## 🔧 配置更新

### 1. 更新CMakeLists.txt

```cmake
# 添加新的源文件
set(SOURCES
    # 现有文件...
    src/main/cpp/core/managers/NewManagerCore.cpp
    src/main/cpp/napi_interface/new_manager_napi.cpp
)
```

### 2. 更新模块导出

```typescript
// src/main/ets/index.ets
export { NewManager } from './managers/NewManager';
export { INewManager } from './interfaces/INewManager';
export { NewEventData, INewEventListener } from './interfaces/INewEventListener';
```

### 3. 更新类型定义

```typescript
// index.d.ts
export declare class NewManager {
  initialize(): Promise<boolean>;
  release(): Promise<boolean>;
  newFunction(param: string): Promise<BiShareResult<string>>;
  newFunctionSync(param: string): BiShareResult<string>;
}
```

## ✅ 验证清单

新增功能后，请确保：

- [ ] TypeScript接口定义完整
- [ ] Operation类实现了同步和异步版本
- [ ] NAPI接口正确桥接
- [ ] C++核心逻辑实现完整
- [ ] 错误处理机制完善
- [ ] 单元测试覆盖
- [ ] 文档更新完整
- [ ] 编译通过无错误
- [ ] 功能测试验证通过

## 📚 最佳实践

1. **遵循命名规范** - 使用一致的命名风格
2. **完善错误处理** - 每个层次都要有适当的错误处理
3. **编写单元测试** - 确保功能的正确性和稳定性
4. **更新文档** - 及时更新API文档和使用说明
5. **性能考虑** - 注意内存使用和执行效率
6. **线程安全** - 确保多线程环境下的安全性

通过遵循这个接入指南，可以确保新功能与现有架构保持一致，同时具备良好的可维护性和扩展性。
