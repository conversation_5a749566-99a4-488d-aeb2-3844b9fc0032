#ifndef NETWORK_DIRECT_EXECUTOR_H
#define NETWORK_DIRECT_EXECUTOR_H

#include "bishare_operations.h"

namespace OHOS {
    namespace BiShare {

        /**
         * 网络管理直接执行器
         * 负责网络配置、路径管理等功能
         */
        class NetworkDirectExecutor {
        public:
            // 注册所有网络管理相关的直接执行函数
            static void RegisterAll();

            // 具体的直接执行函数
            static void ExecuteSetNetworkInfo(napi_env env, AsyncWorkData* workData);
            static void ExecuteGetRootPath(napi_env env, AsyncWorkData* workData);
            static void ExecuteGetCurrentDirector(napi_env env, AsyncWorkData* workData);

        private:
            static const char* TAG;
        };

        // 全局注册函数（供bishare_operations.cpp调用）
        void RegisterNetworkDirectExecutors();

    } // namespace BiShare
} // namespace OHOS

#endif // NETWORK_DIRECT_EXECUTOR_H
