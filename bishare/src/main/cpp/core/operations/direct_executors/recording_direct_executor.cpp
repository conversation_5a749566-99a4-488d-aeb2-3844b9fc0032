#include "direct_executors/recording_direct_executor.h"
#include "bishare_napi.h"
#include "bishare_logger.h"
#include "bishare-service.h"

namespace OHOS {
    namespace BiShare {

        const char* RecordingDirectExecutor::TAG = "RecordingDirectExecutor";

        void RecordingDirectExecutor::RegisterAll() {
            BiShareLogger::Info(TAG, "📝 [注册] 开始注册录制功能直接执行器...");
            
            // 注册现有的录制功能
            RegisterDirectExecutor("StartScreenRecord", ExecuteStartScreenRecord);
            RegisterDirectExecutor("StopScreenRecord", ExecuteStopScreenRecord);
            RegisterDirectExecutor("StartCapture", ExecuteStartCapture);
            RegisterDirectExecutor("Screenshot", ExecuteScreenshot);
            RegisterDirectExecutor("SetSize", ExecuteSetSize);
            RegisterDirectExecutor("SetDefaultAudioOutputDevice", ExecuteSetDefaultAudioOutputDevice);
            
            // 注册未来的视频录制功能（示例）
            RegisterDirectExecutor("StartVideoRecord", ExecuteStartVideoRecord);
            RegisterDirectExecutor("StopVideoRecord", ExecuteStopVideoRecord);
            
            BiShareLogger::Info(TAG, "✅ [注册] 录制功能直接执行器注册完成");
        }

        void RecordingDirectExecutor::ExecuteStartScreenRecord(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(TAG, "🚀 [直接执行] 开始执行StartScreenRecord操作");

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务未初始化");
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return;
            }

            // 解析参数
            int32_t session = workData->data.intParam1;
            int32_t displayId = workData->data.intParam2;
            int32_t direction = workData->data.intParam3;

            BiShareLogger::Info(TAG, "🔧 [直接执行] 开始屏幕录制...");
            BiShareLogger::Info(TAG, "📍 [直接执行] 参数: session=%d, displayId=%d, direction=%d", 
                session, displayId, direction);

            // 调用原生屏幕录制函数
            workData->result = bishare_service_start_screen_record(session, displayId, direction);

            BiShareLogger::Info(TAG, "📊 [直接执行] 屏幕录制启动结果: %d (%s)",
                static_cast<int>(workData->result), err2str(workData->result));

            if (workData->result == BS_OK) {
                workData->successMessage = "屏幕录制启动成功";
                BiShareLogger::Info(TAG, "🎉 [直接执行] 屏幕录制启动成功");
            } else {
                workData->errorMessage = std::string("屏幕录制启动失败: ") + 
                    std::string(err2str(workData->result));
                BiShareLogger::Error(TAG, "❌ [直接执行] 屏幕录制启动失败: %s",
                    err2str(workData->result));
            }

            BiShareLogger::Info(TAG, "🏁 [直接执行] StartScreenRecord操作执行完成");
        }

        void RecordingDirectExecutor::ExecuteStopScreenRecord(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(TAG, "🚀 [直接执行] 开始执行StopScreenRecord操作");

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务未初始化");
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return;
            }

            // 解析参数
            int32_t session = workData->data.intParam1;
            int32_t displayId = workData->data.intParam2;
            int32_t direction = workData->data.intParam3;

            BiShareLogger::Info(TAG, "🔧 [直接执行] 停止屏幕录制...");

            // 调用原生停止录制函数
            workData->result = bishare_service_stop_screen_record(session, displayId, direction);

            BiShareLogger::Info(TAG, "📊 [直接执行] 屏幕录制停止结果: %d (%s)",
                static_cast<int>(workData->result), err2str(workData->result));

            if (workData->result == BS_OK) {
                workData->successMessage = "屏幕录制停止成功";
                BiShareLogger::Info(TAG, "🎉 [直接执行] 屏幕录制停止成功");
            } else {
                workData->errorMessage = std::string("屏幕录制停止失败: ") + 
                    std::string(err2str(workData->result));
                BiShareLogger::Error(TAG, "❌ [直接执行] 屏幕录制停止失败: %s",
                    err2str(workData->result));
            }

            BiShareLogger::Info(TAG, "🏁 [直接执行] StopScreenRecord操作执行完成");
        }

        void RecordingDirectExecutor::ExecuteStartCapture(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(TAG, "🚀 [直接执行] 开始执行StartCapture操作");

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务未初始化");
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return;
            }

            BiShareLogger::Info(TAG, "🔧 [直接执行] 开始捕获...");

            // 调用原生开始捕获函数
            workData->result = bishare_service_start_capture();

            BiShareLogger::Info(TAG, "📊 [直接执行] 开始捕获结果: %d (%s)",
                static_cast<int>(workData->result), err2str(workData->result));

            if (workData->result == BS_OK) {
                workData->successMessage = "捕获启动成功";
                BiShareLogger::Info(TAG, "🎉 [直接执行] 捕获启动成功");
            } else {
                workData->errorMessage = std::string("捕获启动失败: ") + 
                    std::string(err2str(workData->result));
                BiShareLogger::Error(TAG, "❌ [直接执行] 捕获启动失败: %s",
                    err2str(workData->result));
            }

            BiShareLogger::Info(TAG, "🏁 [直接执行] StartCapture操作执行完成");
        }

        void RecordingDirectExecutor::ExecuteScreenshot(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(TAG, "🚀 [直接执行] 开始执行Screenshot操作");

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务未初始化");
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return;
            }

            // 解析参数
            const std::string& filePath = workData->data.stringParam1;
            int32_t top = workData->data.intParam1;
            int32_t bottom = workData->data.intParam2;
            int32_t left = workData->data.intParam3;
            int32_t right = workData->data.intParam4;

            BiShareLogger::Info(TAG, "🔧 [直接执行] 开始截图...");
            BiShareLogger::Info(TAG, "📍 [直接执行] 文件路径: %s", filePath.c_str());

            // 调用原生截图函数
            workData->result = bishare_service_screenshot(filePath.c_str(), top, bottom, left, right);

            BiShareLogger::Info(TAG, "📊 [直接执行] 截图结果: %d (%s)",
                static_cast<int>(workData->result), err2str(workData->result));

            if (workData->result == BS_OK) {
                workData->successMessage = "截图成功";
                BiShareLogger::Info(TAG, "🎉 [直接执行] 截图成功");
            } else {
                workData->errorMessage = std::string("截图失败: ") + 
                    std::string(err2str(workData->result));
                BiShareLogger::Error(TAG, "❌ [直接执行] 截图失败: %s",
                    err2str(workData->result));
            }

            BiShareLogger::Info(TAG, "🏁 [直接执行] Screenshot操作执行完成");
        }

        void RecordingDirectExecutor::ExecuteSetSize(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(TAG, "🚀 [直接执行] 开始执行SetSize操作");

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务未初始化");
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return;
            }

            // 解析参数
            int32_t screenWidth = workData->data.intParam1;
            int32_t screenHeight = workData->data.intParam2;
            int32_t videoWidth = workData->data.intParam3;
            int32_t videoHeight = workData->data.intParam4;

            BiShareLogger::Info(TAG, "🔧 [直接执行] 设置尺寸...");
            BiShareLogger::Info(TAG, "📍 [直接执行] 屏幕尺寸: %dx%d, 视频尺寸: %dx%d", 
                screenWidth, screenHeight, videoWidth, videoHeight);

            // 调用原生设置尺寸函数
            workData->result = bishare_service_set_size(screenWidth, screenHeight, videoWidth, videoHeight);

            BiShareLogger::Info(TAG, "📊 [直接执行] 设置尺寸结果: %d (%s)",
                static_cast<int>(workData->result), err2str(workData->result));

            if (workData->result == BS_OK) {
                workData->successMessage = "尺寸设置成功";
                BiShareLogger::Info(TAG, "🎉 [直接执行] 尺寸设置成功");
            } else {
                workData->errorMessage = std::string("尺寸设置失败: ") + 
                    std::string(err2str(workData->result));
                BiShareLogger::Error(TAG, "❌ [直接执行] 尺寸设置失败: %s",
                    err2str(workData->result));
            }

            BiShareLogger::Info(TAG, "🏁 [直接执行] SetSize操作执行完成");
        }

        void RecordingDirectExecutor::ExecuteSetDefaultAudioOutputDevice(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(TAG, "🚀 [直接执行] 开始执行SetDefaultAudioOutputDevice操作");

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务未初始化");
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return;
            }

            // 解析参数
            bool enable = workData->data.boolParam1;
            bool_type_t enableType = enable ? BOOL_TRUE : BOOL_FALSE;

            BiShareLogger::Info(TAG, "🔧 [直接执行] 设置默认音频输出设备...");
            BiShareLogger::Info(TAG, "📍 [直接执行] 启用: %s", enable ? "true" : "false");

            // 调用原生设置音频输出设备函数
            workData->result = bishare_service_set_default_audio_output_device(enableType);

            BiShareLogger::Info(TAG, "📊 [直接执行] 设置音频输出设备结果: %d (%s)",
                static_cast<int>(workData->result), err2str(workData->result));

            if (workData->result == BS_OK) {
                workData->successMessage = "音频输出设备设置成功";
                BiShareLogger::Info(TAG, "🎉 [直接执行] 音频输出设备设置成功");
            } else {
                workData->errorMessage = std::string("音频输出设备设置失败: ") + 
                    std::string(err2str(workData->result));
                BiShareLogger::Error(TAG, "❌ [直接执行] 音频输出设备设置失败: %s",
                    err2str(workData->result));
            }

            BiShareLogger::Info(TAG, "🏁 [直接执行] SetDefaultAudioOutputDevice操作执行完成");
        }

        // 未来的视频录制功能示例
        void RecordingDirectExecutor::ExecuteStartVideoRecord(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(TAG, "🚀 [直接执行] 开始执行StartVideoRecord操作");

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务未初始化");
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return;
            }

            // 解析参数
            int32_t session = workData->data.intParam1;
            int32_t displayId = workData->data.intParam2;
            int32_t direction = workData->data.intParam3;
            const std::string& outputPath = workData->data.stringParam1;

            BiShareLogger::Info(TAG, "🔧 [直接执行] 开始视频录制...");
            BiShareLogger::Info(TAG, "📍 [直接执行] 参数: session=%d, displayId=%d, direction=%d, path=%s", 
                session, displayId, direction, outputPath.c_str());

            // 这里可以调用新的视频录制API
            // workData->result = bishare_service_start_video_record(session, displayId, direction, outputPath.c_str());
            
            // 目前使用现有的屏幕录制API作为示例
            workData->result = bishare_service_start_screen_record(session, displayId, direction);

            BiShareLogger::Info(TAG, "📊 [直接执行] 视频录制启动结果: %d (%s)",
                static_cast<int>(workData->result), err2str(workData->result));

            if (workData->result == BS_OK) {
                workData->successMessage = "视频录制启动成功";
                BiShareLogger::Info(TAG, "🎉 [直接执行] 视频录制启动成功");
            } else {
                workData->errorMessage = std::string("视频录制启动失败: ") + 
                    std::string(err2str(workData->result));
                BiShareLogger::Error(TAG, "❌ [直接执行] 视频录制启动失败: %s",
                    err2str(workData->result));
            }

            BiShareLogger::Info(TAG, "🏁 [直接执行] StartVideoRecord操作执行完成");
        }

        void RecordingDirectExecutor::ExecuteStopVideoRecord(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(TAG, "🚀 [直接执行] 开始执行StopVideoRecord操作");

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务未初始化");
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return;
            }

            // 解析参数
            int32_t session = workData->data.intParam1;
            int32_t displayId = workData->data.intParam2;
            int32_t direction = workData->data.intParam3;

            BiShareLogger::Info(TAG, "🔧 [直接执行] 停止视频录制...");

            // 这里可以调用新的视频录制停止API
            // workData->result = bishare_service_stop_video_record(session, displayId, direction);
            
            // 目前使用现有的屏幕录制停止API作为示例
            workData->result = bishare_service_stop_screen_record(session, displayId, direction);

            BiShareLogger::Info(TAG, "📊 [直接执行] 视频录制停止结果: %d (%s)",
                static_cast<int>(workData->result), err2str(workData->result));

            if (workData->result == BS_OK) {
                workData->successMessage = "视频录制停止成功";
                BiShareLogger::Info(TAG, "🎉 [直接执行] 视频录制停止成功");
            } else {
                workData->errorMessage = std::string("视频录制停止失败: ") + 
                    std::string(err2str(workData->result));
                BiShareLogger::Error(TAG, "❌ [直接执行] 视频录制停止失败: %s",
                    err2str(workData->result));
            }

            BiShareLogger::Info(TAG, "🏁 [直接执行] StopVideoRecord操作执行完成");
        }

        // 全局注册函数
        void RegisterRecordingDirectExecutors() {
            RecordingDirectExecutor::RegisterAll();
        }

    } // namespace BiShare
} // namespace OHOS
