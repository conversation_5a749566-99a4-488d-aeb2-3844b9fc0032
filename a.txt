06-15 15:09:17.881  6043-6043    A00201/BiShareFacade           com.aaa.bbb         I  开始初始化BiShareFacade...
06-15 15:09:17.881  6043-6043    A00201/BiShareFacade           com.aaa.bbb         I  创建服务管理器...
06-15 15:09:17.881  6043-6043    A00201/BiShareFacade           com.aaa.bbb         I  服务管理器初始化成功
06-15 15:09:17.881  6043-6043    A00201/BiShareFacade           com.aaa.bbb         I  创建回调管理器...
06-15 15:09:17.881  6043-6043    A00201/BiShareFacade           com.aaa.bbb         I  回调管理器初始化成功
06-15 15:09:17.881  6043-6043    A00201/BiShareFacade           com.aaa.bbb         I  BiShareFacade初始化完成
06-15 15:09:17.881  6043-6043    A00201/RecordingManagerNapi    com.aaa.bbb         I  初始化RecordingManager类
06-15 15:09:17.881  6043-6043    A00201/RecordingManagerNapi    com.aaa.bbb         I  RecordingManager类初始化成功
06-15 15:09:17.881  6043-6043    A00201/DeviceManagerNapi       com.aaa.bbb         I  初始化DeviceManager类
06-15 15:09:17.881  6043-6043    A00201/DeviceManagerNapi       com.aaa.bbb         I  DeviceManager类初始化成功
06-15 15:09:17.881  6043-6043    A00201/BiShareManagerNapi      com.aaa.bbb         I  初始化BiShareManager类
06-15 15:09:17.881  6043-6043    A00201/BiShareManagerNapi      com.aaa.bbb         I  BiShareManager类初始化成功
06-15 15:09:17.883  6043-6043    A03d00/JSAPP                   com.aaa.bbb         I  Callee constructor is OK string
06-15 15:09:17.883  6043-6043    A03d00/JSAPP                   com.aaa.bbb         I  Ability::constructor callee is object [object Object]
06-15 15:09:17.884  6043-6043    C01304/AbilityManagerService   com.aaa.bbb         I  [ui_ability_thread.cpp(AttachInner:170)]LoadLifecycle: Attach uiability.
06-15 15:09:17.884  6043-6043    C057c2/IPCObjectProxy          com.aaa.bbb         I  AddDeathRecipient 393: success, handle:10 desc:ohos.aafwk.AbilityManager
06-15 15:09:17.887  6043-6043    C02b12/AFWK                    com.aaa.bbb         I  [napi_audio_volume_group_manager.cpp] [Construct]Construct() 1
06-15 15:09:17.887  6043-6043    C02b12/AFWK                    com.aaa.bbb         I  [audio_group_manager.cpp] [Init]AudioGroupManager::init set networkId LocalDevice.
06-15 15:09:17.888  6043-6043    C01305/Appkit                  com.aaa.bbb         E  [application_context.cpp(NotifyApplicationForeground:254)]applicationStateCallback is nullptr
06-15 15:09:17.888  6043-6043    C03f00/ArkCompiler             com.aaa.bbb         I  [gc] app is not inBackground
06-15 15:09:17.888  6043-6043    C03f00/ArkCompiler             com.aaa.bbb         I  [gc] Heap Growing Type HIGH_THROUGHPUT
06-15 15:09:17.888  6043-6043    C01707/CONCUR                  com.aaa.bbb         E  [Qos] qoslevel 3 apply for tid 6051 failure
06-15 15:09:17.888  6043-6043    C01707/CONCUR                  com.aaa.bbb         E  [Qos] qoslevel 3 apply for tid 6052 failure
06-15 15:09:17.888  6043-6043    C01707/CONCUR                  com.aaa.bbb         E  [Qos] qoslevel 3 apply for tid 6053 failure
06-15 15:09:17.888  6043-6043    C01707/CONCUR                  com.aaa.bbb         E  [Qos] qoslevel 3 apply for tid 6054 failure
06-15 15:09:17.895  6043-6047    C01304/AbilityManagerService   com.aaa.bbb         I  [ui_ability_thread.cpp(ScheduleAbilityTransaction:344)]Lifecycle: name:EntryAbility,targeState:5,isNewWant:0
06-15 15:09:17.895  6043-6043    C01304/AbilityManagerService   com.aaa.bbb         E  [ui_ability.cpp(HandleCreateAsRecovery:479)]AppRecovery not recovery restart.
06-15 15:09:17.896  6043-6043    A00000/testTag                 com.aaa.bbb         I  Ability onCreate
06-15 15:09:17.897  6043-6043    C01304/AbilityManagerService   com.aaa.bbb         E  [ui_ability_impl.cpp(CheckAndRestore:287)]hasSaveData_ is false.
06-15 15:09:17.897  6043-6043    C01304/AbilityManagerService   com.aaa.bbb         I  [js_ui_ability.cpp(DoOnForegroundForSceneIsNull:725)]JsUIAbility::DoOnForegroundForSceneIsNull displayId  is  0
06-15 15:09:17.897  6043-6043    C04200/WindowScene             com.aaa.bbb         I  <46>Init: [WMSMain]WindowScene init with normal option!
06-15 15:09:17.899  6043-6043    C057c2/IPCObjectProxy          com.aaa.bbb         I  AddDeathRecipient 393: success, handle:15 desc:OHOS.IWindowManager
06-15 15:09:17.900  6043-6043    C057c2/IPCObjectProxy          com.aaa.bbb         I  AddDeathRecipient 393: success, handle:16 desc:ohos.multimodalinput.IConnectManager
06-15 15:09:17.900  6043-6043    C02800/MultimodalInputConnectManager com.aaa.bbb         I  in ConnectMultimodalInputService, Get multimodalinput service successful
06-15 15:09:17.901  6043-6043    C02800/MMIClient               com.aaa.bbb         I  in OnConnected, Connection to server succeeded, fd:47
06-15 15:09:17.901  6043-6043    C02800/MMIClient               com.aaa.bbb         I  in AddFdListener, server was listening
06-15 15:09:17.901  6043-6043    C02800/MMIClient               com.aaa.bbb         I  in StartEventRunner, File fd is in listening
06-15 15:09:17.901  6043-6043    C04200/SingletonContainer      com.aaa.bbb         E  <82>GetSingleton: can not get OHOS::Rosen::WindowInfoReporter
06-15 15:09:17.901  6043-6043    C01304/AbilityManagerService   com.aaa.bbb         E  [ui_ability.cpp(ShouldRecoverState:230)]AppRecovery not recovery restart.
06-15 15:09:17.902  6043-6043    A00000/testTag                 com.aaa.bbb         I  Ability onWindowStageCreate
06-15 15:09:17.902  6043-6060    C057d4/DBinderBaseInvoker      com.aaa.bbb         I  DBinderDatabusInvoker 36: create
06-15 15:09:17.902  6043-6043    C01719/ffrt                    com.aaa.bbb         E  38:TrivalOpenQosCtrlNode:62 task 6043 belong to user 20010032 open qos node failed
06-15 15:09:17.902  6043-6043    C04200/WindowScene             com.aaa.bbb         I  <146>GoForeground: [WMSMain]reason:0
06-15 15:09:17.905  6043-6043    A00000/testTag                 com.aaa.bbb         I  Ability onForeground
06-15 15:09:17.906  6043-6043    A0ff00/BiShare.DeviceManager   com.aaa.bbb         I  DeviceManager created
06-15 15:09:17.906  6043-6043    A0ff00/BiShare.RecordingManager com.aaa.bbb         I  RecordingManager created
06-15 15:09:17.906  6043-6043    A0ff00/BiShare.EventManager    com.aaa.bbb         I  EventManager created
06-15 15:09:17.906  6043-6043    A0ff00/BiShare.BiShareManager  com.aaa.bbb         I  BiShareManager created
06-15 15:09:17.906  6043-6043    A00201/RecordingManagerNapi    com.aaa.bbb         I  获取RecordingManager单例实例
06-15 15:09:17.906  6043-6043    A00201/BiShareCallbacks        com.aaa.bbb         I  🏗️ BiShareCallbacks构造函数开始执行
06-15 15:09:17.906  6043-6043    A00201/BiShareCallbacks        com.aaa.bbb         I  📍 [构造] this指针地址: 0x7f92037678
06-15 15:09:17.906  6043-6043    A00201/BiShareCallbacks        com.aaa.bbb         I  📍 [构造] napiInstance是否有效: 否
06-15 15:09:17.906  6043-6043    A00201/BiShareCallbacks        com.aaa.bbb         I  📍 [构造] 当前静态实例状态: 空
06-15 15:09:17.906  6043-6043    A00201/BiShareCallbacks        com.aaa.bbb         I  ✅ BiShareCallbacks构造函数执行完成
06-15 15:09:17.906  6043-6043    A0001b/InfoService             com.aaa.bbb         I  EventManager --> 添加设备事件监听器
06-15 15:09:17.906  6043-6043    A00201/BiShareNapiInterface    com.aaa.bbb         I  🎯 收到Initialize调用，开始智能路由检测...
06-15 15:09:17.906  6043-6043    A00201/BiShareNapiInterface    com.aaa.bbb         I  📋 Initialize参数数量: 5
06-15 15:09:17.906  6043-6043    A00201/BiShareNapiInterface    com.aaa.bbb         I  🔍 检测最后一个参数类型: function (回调函数)
06-15 15:09:17.906  6043-6043    A00201/BiShareNapiInterface    com.aaa.bbb         I  🔄 [路由] 检测到回调函数，选择异步模式执行Initialize
06-15 15:09:17.906  6043-6043    A00201/BiShareServiceOps       com.aaa.bbb         I  📋 [参数解析] 开始解析isConsole参数...
06-15 15:09:17.906  6043-6043    A00201/BiShareServiceOps       com.aaa.bbb         I  ✅ [参数解析] isConsole: true
06-15 15:09:17.906  6043-6043    A00201/BiShareServiceOps       com.aaa.bbb         I  📋 [参数解析] 开始解析isFile参数...
06-15 15:09:17.906  6043-6043    A00201/BiShareServiceOps       com.aaa.bbb         I  ✅ [参数解析] isFile: true
06-15 15:09:17.906  6043-6043    A00201/BiShareServiceOps       com.aaa.bbb         I  📋 [参数解析] 开始解析logPath参数...
06-15 15:09:17.906  6043-6043    A00201/BiShareServiceOps       com.aaa.bbb         I  📍 [参数解析] logPath长度: 54
06-15 15:09:17.906  6043-6043    A00201/BiShareServiceOps       com.aaa.bbb         I  ✅ [参数解析] logPath解析成功: /data/storage/el2/base/files/service_1749971357000.log
06-15 15:09:17.906  6043-6043    A00201/BiShareServiceOps       com.aaa.bbb         I  📋 [参数解析] 开始解析priority参数...
06-15 15:09:17.906  6043-6043    A00201/BiShareServiceOps       com.aaa.bbb         I  ✅ [参数解析] priority: 7
06-15 15:09:17.906  6043-6043    A00201/BiShareOperations       com.aaa.bbb         I  🔧 [主线程] CreateAsyncWork开始执行
06-15 15:09:17.906  6043-6043    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] workData地址: 0x7f90688e70
06-15 15:09:17.906  6043-6043    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] 操作名称: Initialize
06-15 15:09:17.906  6043-6043    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] execute回调地址: 0x7e7cb6c3e4
06-15 15:09:17.906  6043-6043    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] complete回调地址: 0x7e7cb6ce64
06-15 15:09:17.906  6043-6043    A00201/BiShareOperations       com.aaa.bbb         I  🔄 [CreateAsyncWork] 创建Promise...
06-15 15:09:17.906  6043-6043    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] Promise创建状态: 0
06-15 15:09:17.906  6043-6043    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] deferred地址: 0x7f8f3729e0
06-15 15:09:17.906  6043-6043    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] promise地址: 0x7f921f43d8
06-15 15:09:17.906  6043-6043    A00201/BiShareOperations       com.aaa.bbb         I  🔄 [CreateAsyncWork] 创建资源名称...
06-15 15:09:17.906  6043-6043    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] 资源名称创建状态: 0
06-15 15:09:17.906  6043-6043    A00201/BiShareOperations       com.aaa.bbb         I  🔄 [CreateAsyncWork] 创建异步工作...
06-15 15:09:17.906  6043-6043    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] 异步工作创建状态: 0
06-15 15:09:17.906  6043-6043    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] async_work地址: 0x7f90689290
06-15 15:09:17.906  6043-6043    A00201/BiShareOperations       com.aaa.bbb         I  🔄 [CreateAsyncWork] 排队异步工作...
06-15 15:09:17.906  6043-6043    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] 异步工作排队状态: 0
06-15 15:09:17.906  6043-6043    A00201/BiShareOperations       com.aaa.bbb         I  ✅ [CreateAsyncWork] 异步工作已成功排队
06-15 15:09:17.906  6043-6043    A00201/BiShareOperations       com.aaa.bbb         I  🏁 [CreateAsyncWork] CreateAsyncWork执行完成
06-15 15:09:17.906  6043-6043    C015b0/NetMgrSubsystem         com.aaa.bbb         I  NETMANAGER_BASE [connection_module.cpp 139] ParseNetConnectionParams no params
06-15 15:09:17.907  6043-6072    C057c2/IPCObjectProxy          com.aaa.bbb         I  AddDeathRecipient 393: success, handle:17 desc:OHOS.NetManagerStandard.INetConnService
06-15 15:09:17.907  6043-6043    C01304/AbilityManagerService   com.aaa.bbb         E  [ui_ability_impl.cpp(operator():387)]applicationContext or lifecycleCallback is nullptr.
06-15 15:09:17.907  6043-6043    C03900/Ace                     com.aaa.bbb         I  [ui_content_impl.cpp(OHOS_ACE_CreateUIContent)-(-1:-1:undefined)] Ace lib loaded, CreateUIContent.
06-15 15:09:17.907  6043-6043    C03934/AceUIEvent              com.aaa.bbb         I  [ui_event_impl.cpp(InitHandler)-(-1:-1:undefined)] report ace loaded
06-15 15:09:17.908  6043-6043    C03f00/MUSL-LDSO               com.aaa.bbb         E  dlopen_impl load library header failed for libha_ace_engine.z.so
06-15 15:09:17.908  6043-6043    C03934/AceUIEvent              com.aaa.bbb         W  [ui_event_impl.cpp(InitHandler)-(-1:-1:undefined)] Failed to open shared library libha_ace_engine.z.so, reason: Error loading shared library libha_ace_engine.z.so: No such file or directoryn
06-15 15:09:17.908  6043-6043    C057c2/IPCObjectProxy          com.aaa.bbb         I  AddDeathRecipient 393: success, handle:18 desc:OHOS.Startup.IWatcherManager
06-15 15:09:17.912  6043-6043    C02c03/PARAM_WATCHER           com.aaa.bbb         I  [watcher_manager_kits.cpp:172]Add watcher keyPrefix persist.ace.trace.layout.enabled remoteWatcherId 21 success
06-15 15:09:17.913  6043-6043    C02c03/PARAM_WATCHER           com.aaa.bbb         I  [watcher_manager_kits.cpp:172]Add watcher keyPrefix const.security.developermode.state remoteWatcherId 21 success
06-15 15:09:17.913  6043-6049    C015b0/NetConnManager          com.aaa.bbb         W  [OnNetAvailable-(net_conn_callback_stub.cpp:66)]sent raw data is less than 32k
06-15 15:09:17.913  6043-6049    C015b0/NetMgrSubsystem         com.aaa.bbb         I  NETMANAGER_BASE [net_conn_callback_observer.cpp 36] no event listener find netAvailable
06-15 15:09:17.913  6043-6049    C015b0/NetConnManager          com.aaa.bbb         W  [OnNetCapabilitiesChange-(net_conn_callback_stub.cpp:85)]sent raw data is less than 32k
06-15 15:09:17.913  6043-6043    C057d4/DBinderBaseInvoker      com.aaa.bbb         I  DBinderDatabusInvoker 36: create
06-15 15:09:17.913  6043-6049    C015b0/NetMgrSubsystem         com.aaa.bbb         I  NETMANAGER_BASE [net_conn_callback_observer.cpp 56] no event listener find netCapabilitiesChange
06-15 15:09:17.914  6043-6049    C015b0/NetConnManager          com.aaa.bbb         W  [OnNetConnectionPropertiesChange-(net_conn_callback_stub.cpp:135)]sent raw data is less than 32k
06-15 15:09:17.914  6043-6049    C015b0/NetMgrSubsystem         com.aaa.bbb         I  NETMANAGER_BASE [net_conn_callback_observer.cpp 79] no event listener find netConnectionPropertiesChange
06-15 15:09:17.914  6043-6072    C015b0/NetMgrSubsystem         com.aaa.bbb         I  NETMANAGER_BASE [connection_exec.cpp 728] Register result 0
06-15 15:09:17.914  6043-6043    C03900/Ace                     com.aaa.bbb         I  [ace_new_pipe_judgement.cpp(InitAceNewPipeConfig)-(-2:-1:undefined)] Init RenderService UniRender Type:0
06-15 15:09:17.914  6043-6043    C03900/Ace                     com.aaa.bbb         I  [ui_content_impl.cpp(CommonInitialize)-(-2:-1:undefined)] UIContent: apiCompatibleVersion: 10, apiTargetVersion: 10, and apiReleaseType: Release, useNewPipe: 1
06-15 15:09:17.915  6043-6043    C02c03/PARAM_WATCHER           com.aaa.bbb         I  [watcher_manager_kits.cpp:172]Add watcher keyPrefix persist.sys.arkui.animationscale remoteWatcherId 21 success
06-15 15:09:17.915  6043-6043    C03900/Ace                     com.aaa.bbb         I  [localization.cpp(SetLocaleImpl)-(-2:-1:undefined)] SetLocale language tag: zh-Hans-, select language: zh-CN
06-15 15:09:17.915  6043-6043    C03900/Ace                     com.aaa.bbb         I  [ui_content_impl.cpp(CommonInitialize)-(-2:-1:undefined)] Initialize UIContent isModelJson:true
06-15 15:09:17.915  6043-6043    C03900/Ace                     com.aaa.bbb         I  [ace_container.cpp(SetHapPath)-(100000:100000:scope)] SetHapPath, Use hap path to load resource
06-15 15:09:17.916  6043-6043    C04201/DisplayManager          com.aaa.bbb         I  <1145>IDisplayModeListener register success
06-15 15:09:17.916  6043-6043    C01706/rtg_interface           com.aaa.bbb         I  rtg Open fail, errno = 2(No such file or directory), dev = /proc/self/sched_rtg_ctrl
06-15 15:09:17.916  6043-6043    C01406/OHOS::RS                com.aaa.bbb         I  RsFrameReport:[LoadLibrary] load library success!
06-15 15:09:17.916  6043-6043    C01406/OHOS::RS                com.aaa.bbb         I  RsFrameReport:[Init] dlopen libframe_ui_intf.so success!
06-15 15:09:17.916  6043-6043    C01706/ueaClient-RmeCoreSched  com.aaa.bbb         E  [Init]: do not enabled!ret: -1
06-15 15:09:17.916  6043-6043    C01706/ueaClient-FrameMsgMgr   com.aaa.bbb         I  [Init]:inited success!
06-15 15:09:17.916  6043-6043    C01706/ueaClient-FrameUiIntf   com.aaa.bbb         I  [Init]:ret:1, inited:1
06-15 15:09:17.917  6043-6043    C057c2/IPCObjectProxy          com.aaa.bbb         I  AddDeathRecipient 393: success, handle:19 desc:ohos.accessibility.IAccessibleAbilityManagerService
06-15 15:09:17.918  6043-6043    C01d03/accessibility_acfwk     com.aaa.bbb         I  [accessibility_config_impl.cpp(SubscribeConfigObserver:321)]id = [0]
06-15 15:09:17.918  6043-6060    C01719/ffrt                    com.aaa.bbb         E  58:TrivalOpenQosCtrlNode:62 task 6043 belong to user 20010032 open qos node failed
06-15 15:09:17.918  6043-6060    A00201/BiShareOperations       com.aaa.bbb         I  🚀 [后台线程] ExecuteCallback开始执行
06-15 15:09:17.918  6043-6060    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] 线程ID: 547892492720
06-15 15:09:17.918  6043-6060    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] data地址: 0x7f90688e70
06-15 15:09:17.918  6043-6060    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] env地址: 0x7f92b45560
06-15 15:09:17.918  6043-6060    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] workData地址: 0x7f90688e70
06-15 15:09:17.918  6043-6060    A00201/BiShareOperations       com.aaa.bbb         I  🔍 [后台线程] 验证workData有效性...
06-15 15:09:17.918  6043-6060    A00201/BiShareOperations       com.aaa.bbb         I  ✅ [后台线程] workData有效，操作名称: Initialize
06-15 15:09:17.918  6043-6060    A00201/BiShareOperations       com.aaa.bbb         I  🔍 [后台线程] 检查Operation实例指针...
06-15 15:09:17.918  6043-6060    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] operation地址: 0x7f8f3729b0
06-15 15:09:17.918  6043-6060    A00201/BiShareOperations       com.aaa.bbb         I  ✅ [后台线程] Operation实例指针有效，准备调用ExecuteOperation
06-15 15:09:17.918  6043-6060    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] 操作名称: Initialize
06-15 15:09:17.918  6043-6060    A00201/BiShareOperations       com.aaa.bbb         I  🔍 [后台线程] 验证operation指针有效性...
06-15 15:09:17.918  6043-6060    A00201/BiShareOperations       com.aaa.bbb         I  ✅ [后台线程] operation对象验证通过
06-15 15:09:17.918  6043-6060    A00201/BiShareOperations       com.aaa.bbb         I  🔄 [后台线程] 调用 Initialize::ExecuteOperation...
06-15 15:09:17.918  6043-6060    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] 调用前workData->result: 0
06-15 15:09:17.918  6043-6060    C03f00/MUSL-SIGCHAIN           com.aaa.bbb         E  signal_chain_handler call 2 rd sigchain action for signal: 5
06-15 15:09:17.918  6043-6060    C02d11/DfxSignalHandler        com.aaa.bbb         I  DFX_SigchainHandler :: sig(5), pid(6043), tid(6060).
06-15 15:09:17.918  6043-6060    C02d11/DfxSignalHandler        com.aaa.bbb         I  Try block main thread.
06-15 15:09:17.918  6043-6043    C02d11/DfxSignalHandler        com.aaa.bbb         I  Crash(3) in child thread(6043), lock main thread.
06-15 15:09:17.918  6043-6060    C02d11/DfxSignalHandler        com.aaa.bbb         I  DFX_SigchainHandler :: sig(5), pid(6043), processName(com.aaa.bbb), threadName(OS_FFRT_2_0).
06-15 15:09:17.925  6043-6060    C02d11/DfxSignalHandler        com.aaa.bbb         I  Start wait for VmProcess(6078) exit.
06-15 15:09:17.925  6043-6060    C02d11/DfxSignalHandler        com.aaa.bbb         I  (6060) wait VmProcess(6078) exit.
06-15 15:09:17.928  6043-6079    C057c1/IPCWorkThread           com.aaa.bbb         I  ThreadHandler 60: proto:0 policy:0 name:OS_IPC_3_6079
06-15 15:09:18.381  6043-6060    C02d11/DfxSignalHandler        com.aaa.bbb         I  (6060) wait for (6078) return with ret(6078) status(0)
06-15 15:09:18.381  6043-6060    C02d11/DfxSignalHandler        com.aaa.bbb         I  Finish handle signal(5) in 6043:6060
06-15 15:09:18.381  6043-6060    C03f00/MUSL-SIGCHAIN           com.aaa.bbb         E  signal_chain_handler call usr sigaction for signal: 5
06-15 15:09:18.381  6043-6043    C02d11/DfxSignalHandler        com.aaa.bbb         I  Crash in child thread(6043), exit main thread.
06-15 15:09:18.381  6043-6091    C01719/ffrt                    com.aaa.bbb         E  <<<=== ffrt black box(BBOX) start ===>>>
06-15 15:09:18.381  6043-6091    C01719/ffrt                    com.aaa.bbb         E  <<<=== current status ===>>>
06-15 15:09:18.381  6043-6091    C01719/ffrt                    com.aaa.bbb         E  <<<=== task counter ===>>>
06-15 15:09:18.381  6043-6091    C01719/ffrt                    com.aaa.bbb         E  FFRT BBOX TaskSubmitCounter:6 TaskEnQueueCounter:6 TaskDoneCounter:1
06-15 15:09:18.381  6043-6091    C01719/ffrt                    com.aaa.bbb         E  FFRT BBOX TaskRunCounter:6 TaskSwitchCounter:0 TaskFinishCounter:5
06-15 15:09:18.381  6043-6091    C01719/ffrt                    com.aaa.bbb         E  FFRT BBOX TaskWakeCounterInc:0, TaskPendingCounter:0
06-15 15:09:18.381  6043-6091    C01719/ffrt                    com.aaa.bbb         E  TaskRunCounter is not equal to TaskSwitchCounter + TaskFinishCounter
06-15 15:09:18.382  6043-6091    C01719/ffrt                    com.aaa.bbb         E  <<<=== worker status ===>>>
06-15 15:09:18.382  6043-6091    C01719/ffrt                    com.aaa.bbb         E  qos 2: worker tid 6074 is running nothing
06-15 15:09:18.382  6043-6091    C01719/ffrt                    com.aaa.bbb         E  qos 2: worker tid 6072 is running nothing
06-15 15:09:18.382  6043-6091    C01719/ffrt                    com.aaa.bbb         E  qos 3: worker tid 6063 is running nothing
06-15 15:09:18.382  6043-6091    C01719/ffrt                    com.aaa.bbb         E  <<<=== ready queue status ===>>>
06-15 15:09:18.382  6043-6091    C01719/ffrt                    com.aaa.bbb         E  <<<=== ffrt black box(BBOX) finish ===>>>
06-15 15:09:18.382  6043-6043    C02c03/PARAM_WATCHER           com.aaa.bbb         I  [watcher_manager_kits.cpp:172]Add watcher keyPrefix persist.sys.graphic.animationscale remoteWatcherId 21 success
06-15 15:09:18.382  6043-6060    C03f00/MUSL-SIGCHAIN           com.aaa.bbb         E  signal_chain_handler call 2 rd sigchain action for signal: 5
06-15 15:09:18.382  6043-6060    C02d11/DfxSignalHandler        com.aaa.bbb         I  DFX_SigchainHandler :: sig(5), pid(6043), tid(6060).
06-15 15:09:18.382  6043-6060    C03f00/MUSL-SIGCHAIN           com.aaa.bbb         E  signal_chain_handler SIG_DFL handler for signal: 5
06-15 15:09:18.382  6043-6060    C03f00/MUSL-SIGCHAIN           com.aaa.bbb         E  pid(6043) rethrow sig(5) success.
