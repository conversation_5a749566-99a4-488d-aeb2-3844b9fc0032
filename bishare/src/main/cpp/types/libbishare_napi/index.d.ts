
/**
 * BiShare NAPI Type Definitions
 *
 * This is the COMPLETE and ONLY TypeScript definition file for the BiShare NAPI module.
 * All types, interfaces, constants, and function signatures are defined here.
 *
 * Architecture:
 * - C++ Implementation: interfaces/napi/bishare_napi_interface.cpp
 * - TypeScript Definitions: types/libbishare_napi/index.d.ts (THIS FILE)
 *
 * This file should be kept in sync with the C++ implementation.
 */

// ============================================================================
// Constants (should match bishare_napi_interface.cpp)
// ============================================================================

/** BiShare version */
export const VERSION: string;

/** Log priority levels */
export const LOG_PRIORITY_DEBUG: number;
export const LOG_PRIORITY_INFO: number;
export const LOG_PRIORITY_WARN: number;
export const LOG_PRIORITY_ERROR: number;

/** Event types */
export const EVENT_DEVICE_INFO: number;
export const EVENT_DEVICE_STATUS: number;
export const EVENT_CONNECT_STATUS: number;

/** Error codes (exported from C++ layer) */
export const BS_OK: number;
export const BS_ERROR: number;
export const BS_NOT_INIT: number;
export const BS_INVALID_PARAM: number;
export const BS_NOT_SUPPORTED: number;
export const BS_PERMISSION_DENIED: number;
export const BS_DEVICE_NOT_FOUND: number;
export const BS_CONNECTION_FAILED: number;
export const BS_TIMEOUT: number;
export const BS_INSUFFICIENT_MEMORY: number;

// ============================================================================
// Interfaces
// ============================================================================

/**
 * NAPI Event data structure
 */
export interface NapiEventData {
  type: number;
  data: string;
}

/**
 * NAPI Recording options interface
 */
export interface NapiRecordingOptions {
  session: number;
  displayId: number;
  direction: number;
}

/**
 * NAPI Screenshot options interface
 */
export interface NapiScreenshotOptions {
  filePath: string;
  top: number;
  bottom: number;
  left: number;
  right: number;
}

/**
 * NAPI Device information interface (for C++ layer authentication)
 */
export interface NapiDeviceInfo {
  name: string;
  password: string;
}

/**
 * NAPI 网络类型枚举
 */
export enum NapiNetworkType {
  /** 以太网 */
  Ethernet = 0,
  /** WiFi */
  Wlan = 1,
  /** 移动网络 */
  Mobile = 2
}

/**
 * NAPI 网络信息配置接口
 */
export interface NapiNetworkInfoOptions {
  /** 网络类型 */
  networkType: NapiNetworkType;
  /** IP地址 */
  addr: string;
  /** MAC地址 */
  mac: string;
}

/**
 * NAPI 日志优先级枚举
 */
export enum NapiBlogPriority {
  /** 调试级别 */
  DEBUG = 3,
  /** 信息级别 */
  INFO = 4,
  /** 警告级别 */
  WARN = 5,
  /** 错误级别 */
  ERROR = 6
}

/**
 * NAPI 初始化选项接口
 */
export interface NapiInitOptions {
  /** 是否启用控制台日志 */
  isConsole: boolean;
  /** 是否启用文件日志 */
  isFile: boolean;
  /** 日志文件路径 */
  filePath: string;
  /** 日志优先级 */
  priority: NapiBlogPriority;
}

/**
 * 通用回调函数类型
 */
export type Callback<T> = (error: Error | null, result: T) => void;

/**
 * NAPI 事件回调函数类型
 */
export type NapiEventCallback = (data: NapiEventData) => void;

/**
 * NAPI 事件类型枚举
 */
export enum NapiEventType {
  /** 设备信息事件 */
  DEVICE_INFO = 1,
  /** 设备状态事件 */
  DEVICE_STATUS = 2,
  /** 连接状态事件 */
  CONNECT_STATUS = 3
}

/**
 * Recording Manager class
 *
 * Note: This class definition should match the C++ implementation
 * in bishare_napi_interface.cpp
 */
export class RecordingManager {
  static getInstance(): RecordingManager;

  startScreenRecord(options: NapiRecordingOptions): Promise<boolean>;
  stopScreenRecord(options: NapiRecordingOptions): Promise<boolean>;
  screenshot(options: NapiScreenshotOptions): Promise<boolean>;
}

/**
 * EtsDeviceManager class - Device management wrapper
 */
export class EtsDeviceManager {
  static getInstance(): EtsDeviceManager;

  discoverDevices(): Promise<boolean>;
  clearDiscoveredDevices(): Promise<boolean>;
  getDiscoveredDevices(): Promise<any[]>;
  setDeviceInfo(name: string, password: string): Promise<boolean>;
  setDeviceModel(model: string): Promise<boolean>;
  getDeviceModel(): string;
  resetDeviceModel(): Promise<boolean>;
  findRemoteDevice(pincode: string): Promise<boolean>;
  getRootPath(): string;
  getCurrentDirector(): string;
  setNetworkInfo(networkType: number, addr: string, mac: string): Promise<boolean>;
}

/**
 * BiShareManager class - Main BiShare service manager
 */
export class BiShareManager {
  static getInstance(): BiShareManager;

  initialize(isConsole: boolean, isFile: boolean, filePath: string, priority: number): Promise<boolean>;
  release(): Promise<boolean>;
  setDeviceInfo(options: { name: string; password: string }): Promise<boolean>;
  on(eventType: number, callback: (data: NapiEventData) => void): void;
  off(eventType: number, callback: (data: NapiEventData) => void): void;
  once(eventType: number, callback: (data: NapiEventData) => void): void;
}

// ============================================================================
// Main API Functions (should match bishare_napi_interface.cpp methods)
// ============================================================================

export function initialize(
  isConsole: boolean,
  isFile: boolean,
  filePath: string,
  priority: number,
  callback?: (error: Error | null, result: boolean) => void
): Promise<boolean>;

/**
 * Release BiShare service
 */
export function release(
  callback?: (error: Error | null, result: boolean) => void
): Promise<boolean>;

/**
 * Discover devices
 */
export function discoverDevices(
  callback?: (error: Error | null, result: boolean) => void
): Promise<boolean>;

/**
 * Clear discovered devices
 */
export function clearDiscoveredDevices(
  callback?: (error: Error | null, result: boolean) => void
): Promise<boolean>;

/**
 * Get discovered devices
 */
export function getDiscoveredDevices(
  callback?: (error: Error | null, result: boolean) => void
): Promise<boolean>;

/**
 * Start screen recording
 */
export function startScreenRecord(
  session: number,
  displayId: number,
  direction: number,
  callback?: (error: Error | null, result: boolean) => void
): Promise<boolean>;

/**
 * Stop screen recording
 */
export function stopScreenRecord(
  session: number,
  displayId: number,
  direction: number,
  callback?: (error: Error | null, result: boolean) => void
): Promise<boolean>;

/**
 * Start capture
 */
export function startCapture(
  callback?: (error: Error | null, result: boolean) => void
): Promise<boolean>;

/**
 * Set device information
 */
export function setDeviceInfo(
  name: string,
  password: string,
  callback?: (error: Error | null, result: boolean) => void
): Promise<boolean>;

/**
 * Set device model
 */
export function setDeviceModel(
  model: string,
  callback?: (error: Error | null, result: boolean) => void
): Promise<boolean>;

/**
 * Get device model
 */
export function getDeviceModel(): string;

/**
 * Reset device model
 */
export function resetDeviceModel(
  callback?: (error: Error | null, result: boolean) => void
): Promise<boolean>;

/**
 * Get root path
 */
export function getRootPath(): string;

/**
 * Get current directory
 */
export function getCurrentDirector(): string;

/**
 * Set screen and video size
 */
export function setSize(
  screenWidth: number,
  screenHeight: number,
  videoWidth: number,
  videoHeight: number,
  callback?: (error: Error | null, result: boolean) => void
): Promise<boolean>;

/**
 * Set default audio output device
 */
export function setDefaultAudioOutputDevice(
  enable: boolean,
  callback?: (error: Error | null, result: boolean) => void
): Promise<boolean>;

/**
 * Take a screenshot
 */
export function screenshot(
  filePath: string,
  top: number,
  bottom: number,
  left: number,
  right: number,
  callback?: (error: Error | null, result: boolean) => void
): Promise<boolean>;

/**
 * Find a remote device
 */
export function findRemoteDevice(
  pincode: string,
  callback?: (error: Error | null, result: boolean) => void
): Promise<boolean>;

/**
 * 设置本机网络参数
 */
export function setNetworkInfo(
  networkType: number,
  addr: string,
  mac: string,
  callback?: (error: Error | null, result: boolean) => void
): Promise<boolean>;

/**
 * Register an event listener
 */
export function on(eventType: number, callback: (data: NapiEventData) => void): void;

/**
 * Unregister an event listener
 */
export function off(eventType: number, callback: (data: NapiEventData) => void): void;

/**
 * Register a one-time event listener
 */
export function once(eventType: number, callback: (data: NapiEventData) => void): void;

