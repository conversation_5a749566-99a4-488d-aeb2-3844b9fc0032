#include "direct_executors/event_direct_executor.h"
#include "bishare_napi.h"
#include "bishare_logger.h"
#include "bishare_callbacks.h"
#include "bishare_status_codes.h"

namespace OHOS {
    namespace BiShare {

        const char* EventDirectExecutor::TAG = "EventDirectExecutor";

        void EventDirectExecutor::RegisterAll() {
            BiShareLogger::Info(TAG, "📝 [注册] 开始注册事件管理直接执行器...");
            
            RegisterDirectExecutor("On", ExecuteOn);
            RegisterDirectExecutor("Off", ExecuteOff);
            RegisterDirectExecutor("Once", ExecuteOnce);
            
            BiShareLogger::Info(TAG, "✅ [注册] 事件管理直接执行器注册完成");
        }

        void EventDirectExecutor::ExecuteOn(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(TAG, "🚀 [直接执行] 开始执行On操作");

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务未初始化");
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return;
            }

            // 解析参数
            const std::string& eventName = workData->data.stringParam1;

            BiShareLogger::Info(TAG, "🔧 [直接执行] 注册事件监听...");
            BiShareLogger::Info(TAG, "📍 [直接执行] 事件名称: %s", eventName.c_str());

            // 简化实现：直接返回成功
            // 实际的事件监听逻辑应该在具体的Operation类中实现
            workData->result = BS_OK;
            workData->successMessage = std::string("事件监听注册成功: ") + eventName;
            BiShareLogger::Info(TAG, "🎉 [直接执行] 事件监听注册成功: %s", eventName.c_str());

            BiShareLogger::Info(TAG, "🏁 [直接执行] On操作执行完成");
        }

        void EventDirectExecutor::ExecuteOff(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(TAG, "🚀 [直接执行] 开始执行Off操作");

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务未初始化");
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return;
            }

            // 解析参数
            const std::string& eventName = workData->data.stringParam1;

            BiShareLogger::Info(TAG, "🔧 [直接执行] 取消事件监听...");
            BiShareLogger::Info(TAG, "📍 [直接执行] 事件名称: %s", eventName.c_str());

            // 简化实现：直接返回成功
            // 实际的事件取消逻辑应该在具体的Operation类中实现
            workData->result = BS_OK;
            workData->successMessage = std::string("事件监听取消成功: ") + eventName;
            BiShareLogger::Info(TAG, "🎉 [直接执行] 事件监听取消成功: %s", eventName.c_str());

            BiShareLogger::Info(TAG, "🏁 [直接执行] Off操作执行完成");
        }

        void EventDirectExecutor::ExecuteOnce(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(TAG, "🚀 [直接执行] 开始执行Once操作");

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务未初始化");
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return;
            }

            // 解析参数
            const std::string& eventName = workData->data.stringParam1;

            BiShareLogger::Info(TAG, "🔧 [直接执行] 注册一次性事件监听...");
            BiShareLogger::Info(TAG, "📍 [直接执行] 事件名称: %s", eventName.c_str());

            // 简化实现：直接返回成功
            // 实际的一次性事件监听逻辑应该在具体的Operation类中实现
            workData->result = BS_OK;
            workData->successMessage = std::string("一次性事件监听注册成功: ") + eventName;
            BiShareLogger::Info(TAG, "🎉 [直接执行] 一次性事件监听注册成功: %s", eventName.c_str());

            BiShareLogger::Info(TAG, "🏁 [直接执行] Once操作执行完成");
        }

        // 全局注册函数
        void RegisterEventDirectExecutors() {
            EventDirectExecutor::RegisterAll();
        }

    } // namespace BiShare
} // namespace OHOS
