06-15 18:05:14.117  8970-8970    A00201/BiShareNapiInterface    com.aaa.bbb         I  🎯 收到SetNetworkInfo调用，开始智能路由检测...
06-15 18:05:14.117  8970-8970    A00201/BiShareNapiInterface    com.aaa.bbb         I  📋 SetNetworkInfo参数数量: 3
06-15 18:05:14.117  8970-8970    A00201/BiShareNapiInterface    com.aaa.bbb         I  🔍 检测最后一个参数类型: 非function
06-15 18:05:14.117  8970-8970    A00201/BiShareNapiInterface    com.aaa.bbb         I  🎯 [路由] 未检测到回调函数，选择Promise模式执行SetNetworkInfo
06-15 18:05:14.117  8970-8970    A00201/BiShareOperations       com.aaa.bbb         I  🔧 [主线程] CreateAsyncWork开始执行
06-15 18:05:14.117  8970-8970    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] workData地址: 0x7fadcc7a30
06-15 18:05:14.117  8970-8970    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] 操作名称: SetNetworkInfo
06-15 18:05:14.117  8970-8970    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] execute回调地址: 0x7e9be7ae58
06-15 18:05:14.117  8970-8970    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] complete回调地址: 0x7e9be7bae0
06-15 18:05:14.117  8970-8970    A00201/BiShareOperations       com.aaa.bbb         I  🔄 [CreateAsyncWork] 创建Promise...
06-15 18:05:14.117  8970-8970    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] Promise创建状态: 0
06-15 18:05:14.117  8970-8970    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] deferred地址: 0x7fadb328b0
06-15 18:05:14.117  8970-8970    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] promise地址: 0x7fb0f24128
06-15 18:05:14.117  8970-8970    A00201/BiShareOperations       com.aaa.bbb         I  🔄 [CreateAsyncWork] 创建资源名称...
06-15 18:05:14.118  8970-8970    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] 资源名称创建状态: 0
06-15 18:05:14.118  8970-8970    A00201/BiShareOperations       com.aaa.bbb         I  🔄 [CreateAsyncWork] 创建异步工作...
06-15 18:05:14.118  8970-8970    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] 异步工作创建状态: 0
06-15 18:05:14.118  8970-8970    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] async_work地址: 0x7faedcbca0
06-15 18:05:14.118  8970-8970    A00201/BiShareOperations       com.aaa.bbb         I  🔄 [CreateAsyncWork] 排队异步工作...
06-15 18:05:14.118  8970-8970    A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] 异步工作排队状态: 0
06-15 18:05:14.118  8970-8970    A00201/BiShareOperations       com.aaa.bbb         I  ✅ [CreateAsyncWork] 异步工作已成功排队
06-15 18:05:14.118  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  🚀 [后台线程] ExecuteCallback开始执行
06-15 18:05:14.118  8970-8970    A00201/BiShareOperations       com.aaa.bbb         I  🏁 [CreateAsyncWork] CreateAsyncWork执行完成
06-15 18:05:14.118  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] 线程ID: 548403411376
06-15 18:05:14.118  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] data地址: 0x7fadcc7a30
06-15 18:05:14.118  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] env地址: 0x7fb1862560
06-15 18:05:14.118  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] workData地址: 0x7fadcc7a30
06-15 18:05:14.118  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  🔍 [后台线程] 验证workData有效性...
06-15 18:05:14.118  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  ✅ [后台线程] workData有效，操作名称: SetNetworkInfo
06-15 18:05:14.118  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  🔍 [后台线程] 检查Operation实例指针...
06-15 18:05:14.118  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] operation地址: 0x7fadb32880
06-15 18:05:14.118  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  ✅ [后台线程] Operation实例指针有效，准备调用ExecuteOperation
06-15 18:05:14.118  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] 操作名称: SetNetworkInfo
06-15 18:05:14.118  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  🔍 [后台线程] 验证operation指针有效性...
06-15 18:05:14.118  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  ✅ [后台线程] operation对象验证通过
06-15 18:05:14.118  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  🔄 [后台线程] 调用 SetNetworkInfo::ExecuteOperation...
06-15 18:05:14.118  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] 调用前workData->result: 0
06-15 18:05:14.118  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  🚀 [后台线程] 直接执行SetNetworkInfo操作
06-15 18:05:14.118  8970-8987    A00201/NetworkDirectExecutor   com.aaa.bbb         I  🚀 [直接执行] 开始执行SetNetworkInfo操作
06-15 18:05:14.118  8970-8987    A00201/NetworkDirectExecutor   com.aaa.bbb         I  🔧 [直接执行] 设置网络信息: 类型=1, IP=*************
06-15 18:05:14.118  8970-8987    A00201/NetworkDirectExecutor   com.aaa.bbb         I  📍 [调试] 详细参数: networkType=1, IP='*************', MAC='702ad701bd2a'
06-15 18:05:14.118  8970-8987    A00201/NetworkDirectExecutor   com.aaa.bbb         I  📍 [直接执行] 验证后参数: networkType=1, IP='*************', MAC='702ad701bd2a'
06-15 18:05:14.118  8970-8987    A00201/NetworkDirectExecutor   com.aaa.bbb         I  🔄 [直接执行] 准备调用bishare_service_set_network_info...
06-15 18:05:14.118  8970-8987    A00201/NetworkDirectExecutor   com.aaa.bbb         I  🔒 [直接执行] 进入原生函数调用保护区域...
06-15 18:05:14.118  8970-8987    A00201/NetworkDirectExecutor   com.aaa.bbb         I  🔄 [直接执行] bishare_service_set_network_info调用完成
06-15 18:05:14.118  8970-8987    A00201/NetworkDirectExecutor   com.aaa.bbb         I  🔒 [直接执行] 原生函数调用保护区域退出
06-15 18:05:14.118  8970-9000    A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 8, msg: [bishare][file:BiShareService.cpp - onMessageReceived()] - WHAT_NETWORK type: 1, addr: *************.
06-15 18:05:14.118  8970-9000    A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 8, msg: [bishare][file:DeviceManager.cpp - setNetworkType()] - Reset comm
06-15 18:05:14.118  8970-9000    A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 5, msg: [bishare][file:BMessage.cpp - post()] - failed to post message as target looper for handler 0 is gone.
06-15 18:05:14.118  8970-9000    A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 8, msg: [bishare][file:DeviceManager.cpp - setBroadcastIp()] - broadcast: *************.
06-15 18:05:14.118  8970-9000    C03f00/MUSL-SIGCHAIN           com.aaa.bbb         E  signal_chain_handler call 2 rd sigchain action for signal: 11
06-15 18:05:14.118  8970-9000    C02d11/DfxSignalHandler        com.aaa.bbb         I  DFX_SigchainHandler :: sig(11), pid(8970), tid(9000).
06-15 18:05:14.118  8970-9000    C02d11/DfxSignalHandler        com.aaa.bbb         I  Try block main thread.
06-15 18:05:14.118  8970-8997    C057d4/DBinderBaseInvoker      com.aaa.bbb         I  DBinderDatabusInvoker 36: create
06-15 18:05:14.118  8970-8970    C02d11/DfxSignalHandler        com.aaa.bbb         I  Crash(3) in child thread(8970), lock main thread.
06-15 18:05:14.118  8970-9000    C02d11/DfxSignalHandler        com.aaa.bbb         I  DFX_SigchainHandler :: sig(11), pid(8970), processName(com.aaa.bbb), threadName(OS_FFRT_2_1).
06-15 18:05:14.122  8970-9000    C02d11/DfxSignalHandler        com.aaa.bbb         I  Start wait for VmProcess(9013) exit.
06-15 18:05:14.122  8970-9000    C02d11/DfxSignalHandler        com.aaa.bbb         I  (9000) wait VmProcess(9013) exit.
06-15 18:05:14.128  8970-8987    A00201/NetworkDirectExecutor   com.aaa.bbb         I  ⏱️ [直接执行] 原生库内部处理延迟完成
06-15 18:05:14.128  8970-8987    A00201/NetworkDirectExecutor   com.aaa.bbb         I  📊 [直接执行] 设置网络信息结果: 0 (No error)
06-15 18:05:14.128  8970-8987    A00201/NetworkDirectExecutor   com.aaa.bbb         I  🎉 [直接执行] 网络信息设置成功
06-15 18:05:14.128  8970-8987    A00201/NetworkDirectExecutor   com.aaa.bbb         I  🏁 [直接执行] SetNetworkInfo操作执行完成
06-15 18:05:14.128  8970-8987    A00201/NetworkDirectExecutor   com.aaa.bbb         I  🛡️ [直接执行] 等待原生库内部状态稳定...
06-15 18:05:14.132  8970-9003    C02c03/PARAM_WATCHER           com.aaa.bbb         I  [watcher_manager_kits.cpp:172]Add watcher keyPrefix debug_layer remoteWatcherId 23 success
06-15 18:05:14.138  8970-9003    C01400/OpenGLWrapper           com.aaa.bbb         I  <62>Init: initialized ver=1.4
06-15 18:05:14.146  8970-9003    C01406/OHOS::RS                com.aaa.bbb         I  debug.graphic.overdraw disable
06-15 18:05:14.146  8970-9003    C02c03/PARAM_WATCHER           com.aaa.bbb         I  [watcher_manager_kits.cpp:172]Add watcher keyPrefix debug.graphic.overdraw remoteWatcherId 23 success
06-15 18:05:14.147  8970-9003    C02c03/PARAM_WATCHER           com.aaa.bbb         I  [watcher_manager_kits.cpp:172]Add watcher keyPrefix debug.graphic.colors_overdraw remoteWatcherId 23 success
06-15 18:05:14.222  8970-8987    A00201/NetworkDirectExecutor   com.aaa.bbb         I  ✅ [直接执行] 原生库状态稳定等待完成
06-15 18:05:14.222  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  ✅ [后台线程] SetNetworkInfo操作执行完成
06-15 18:05:14.222  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] 调用后workData->result: 0
06-15 18:05:14.222  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  📊 [后台线程] SetNetworkInfo::ExecuteOperation执行完成，结果: 0 (No error)
06-15 18:05:14.222  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  🎉 [后台线程] 操作 SetNetworkInfo 执行成功
06-15 18:05:14.222  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  🏁 [后台线程] ExecuteCallback执行完成: SetNetworkInfo
06-15 18:05:14.222  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] 最终result: 0 (No error)
06-15 18:05:14.222  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] 最终errorMessage:
06-15 18:05:14.222  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] workData地址: 0x7fadcc7a30
06-15 18:05:14.223  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] workData->deferred: 0x7fadb328b0
06-15 18:05:14.223  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] workData->work: 0x7faedcbca0
06-15 18:05:14.223  8970-8987    A00201/BiShareOperations       com.aaa.bbb         I  🚀 [后台线程] ExecuteCallback即将返回，等待CompleteCallback被调用...
06-15 18:05:14.539  8970-9000    C02d11/DfxSignalHandler        com.aaa.bbb         I  (9000) wait for (9013) return with ret(9013) status(0)
06-15 18:05:14.539  8970-9000    C02d11/DfxSignalHandler        com.aaa.bbb         I  Finish handle signal(11) in 8970:9000
06-15 18:05:14.539  8970-9000    C03f00/MUSL-SIGCHAIN           com.aaa.bbb         E  signal_chain_handler call usr sigaction for signal: 11
06-15 18:05:14.539  8970-8970    C02d11/DfxSignalHandler        com.aaa.bbb         I  Crash in child thread(8970), exit main thread.
06-15 18:05:14.539  8970-9031    C01719/ffrt                    com.aaa.bbb         E  <<<=== ffrt black box(BBOX) start ===>>>
06-15 18:05:14.539  8970-9031    C01719/ffrt                    com.aaa.bbb         E  <<<=== current status ===>>>
06-15 18:05:14.539  8970-9031    C01719/ffrt                    com.aaa.bbb         E  <<<=== task counter ===>>>
06-15 18:05:14.539  8970-9031    C01719/ffrt                    com.aaa.bbb         E  FFRT BBOX TaskSubmitCounter:16 TaskEnQueueCounter:16 TaskDoneCounter:1
06-15 18:05:14.539  8970-9031    C01719/ffrt                    com.aaa.bbb         E  FFRT BBOX TaskRunCounter:16 TaskSwitchCounter:0 TaskFinishCounter:16
06-15 18:05:14.539  8970-9031    C01719/ffrt                    com.aaa.bbb         E  FFRT BBOX TaskWakeCounterInc:0, TaskPendingCounter:0
06-15 18:05:14.539  8970-9031    C01719/ffrt                    com.aaa.bbb         E  TaskRunCounter equals TaskSwitchCounter + TaskFinishCounter
06-15 18:05:14.539  8970-9031    C01719/ffrt                    com.aaa.bbb         E  <<<=== worker status ===>>>
06-15 18:05:14.539  8970-9031    C01719/ffrt                    com.aaa.bbb         E  qos 2: worker tid 9012 is running nothing
06-15 18:05:14.539  8970-9031    C01719/ffrt                    com.aaa.bbb         E  qos 2: worker tid 8997 is running nothing
06-15 18:05:14.539  8970-9031    C01719/ffrt                    com.aaa.bbb         E  qos 2: worker tid 8987 is running nothing
06-15 18:05:14.539  8970-9031    C01719/ffrt                    com.aaa.bbb         E  qos 3: worker tid 8990 is running nothing
06-15 18:05:14.539  8970-9031    C01719/ffrt                    com.aaa.bbb         E  <<<=== ready queue status ===>>>
06-15 18:05:14.539  8970-9031    C01719/ffrt                    com.aaa.bbb         E  <<<=== ffrt black box(BBOX) finish ===>>>
06-15 18:05:14.540  8970-9000    C03f00/MUSL-SIGCHAIN           com.aaa.bbb         E  signal_chain_handler call 2 rd sigchain action for signal: 11
06-15 18:05:14.540  8970-9000    C02d11/DfxSignalHandler        com.aaa.bbb         I  DFX_SigchainHandler :: sig(11), pid(8970), tid(9000).
06-15 18:05:14.540  8970-9000    C03f00/MUSL-SIGCHAIN           com.aaa.bbb         E  signal_chain_handler SIG_DFL handler for signal: 11
06-15 18:05:14.540  8970-9000    C03f00/MUSL-SIGCHAIN           com.aaa.bbb         E  pid(8970) rethrow sig(11) success.
