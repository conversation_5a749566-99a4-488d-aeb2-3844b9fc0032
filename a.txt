06-15 15:48:44.593  12027-12027  C03f00/MUSL-LDSO               com.aaa.bbb         E  do_dlsym failed: symbol not found. so=/data/storage/el1/bundle/libs/arm64/libbishare_napi.so s=NAPI_default_bishare_napi_GetABCCode v=
06-15 15:48:44.593  12027-12027  A00201/BiShareFacade           com.aaa.bbb         I  开始初始化BiShareFacade...
06-15 15:48:44.593  12027-12027  A00201/BiShareFacade           com.aaa.bbb         I  创建服务管理器...
06-15 15:48:44.593  12027-12027  A00201/BiShareFacade           com.aaa.bbb         I  服务管理器初始化成功
06-15 15:48:44.593  12027-12027  A00201/BiShareFacade           com.aaa.bbb         I  创建回调管理器...
06-15 15:48:44.593  12027-12027  A00201/BiShareFacade           com.aaa.bbb         I  回调管理器初始化成功
06-15 15:48:44.593  12027-12027  A00201/BiShareFacade           com.aaa.bbb         I  BiShareFacade初始化完成
06-15 15:48:44.593  12027-12027  A00201/RecordingManagerNapi    com.aaa.bbb         I  初始化RecordingManager类
06-15 15:48:44.593  12027-12027  A00201/RecordingManagerNapi    com.aaa.bbb         I  RecordingManager类初始化成功
06-15 15:48:44.593  12027-12027  A00201/DeviceManagerNapi       com.aaa.bbb         I  初始化DeviceManager类
06-15 15:48:44.594  12027-12027  A00201/DeviceManagerNapi       com.aaa.bbb         I  DeviceManager类初始化成功
06-15 15:48:44.594  12027-12027  A00201/BiShareManagerNapi      com.aaa.bbb         I  初始化BiShareManager类
06-15 15:48:44.594  12027-12027  A00201/BiShareManagerNapi      com.aaa.bbb         I  BiShareManager类初始化成功
06-15 15:48:44.595  12027-12027  A03d00/JSAPP                   com.aaa.bbb         I  Callee constructor is OK string
06-15 15:48:44.595  12027-12027  A03d00/JSAPP                   com.aaa.bbb         I  Ability::constructor callee is object [object Object]
06-15 15:48:44.596  12027-12027  C01304/AbilityManagerService   com.aaa.bbb         I  [ui_ability_thread.cpp(AttachInner:170)]LoadLifecycle: Attach uiability.
06-15 15:48:44.597  12027-12027  C057c2/IPCObjectProxy          com.aaa.bbb         I  AddDeathRecipient 393: success, handle:10 desc:ohos.aafwk.AbilityManager
06-15 15:48:44.598  12027-12027  C01305/Appkit                  com.aaa.bbb         E  [application_context.cpp(NotifyApplicationForeground:254)]applicationStateCallback is nullptr
06-15 15:48:44.598  12027-12027  C03f00/ArkCompiler             com.aaa.bbb         I  [gc] app is not inBackground
06-15 15:48:44.598  12027-12027  C03f00/ArkCompiler             com.aaa.bbb         I  [gc] Heap Growing Type HIGH_THROUGHPUT
06-15 15:48:44.598  12027-12027  C01707/CONCUR                  com.aaa.bbb         E  [Qos] qoslevel 3 apply for tid 12035 failure
06-15 15:48:44.598  12027-12027  C01707/CONCUR                  com.aaa.bbb         E  [Qos] qoslevel 3 apply for tid 12036 failure
06-15 15:48:44.598  12027-12027  C01707/CONCUR                  com.aaa.bbb         E  [Qos] qoslevel 3 apply for tid 12037 failure
06-15 15:48:44.598  12027-12027  C01707/CONCUR                  com.aaa.bbb         E  [Qos] qoslevel 3 apply for tid 12038 failure
06-15 15:48:44.599  12027-12027  C02b12/AFWK                    com.aaa.bbb         I  [napi_audio_volume_group_manager.cpp] [Construct]Construct() 1
06-15 15:48:44.600  12027-12027  C02b12/AFWK                    com.aaa.bbb         I  [audio_group_manager.cpp] [Init]AudioGroupManager::init set networkId LocalDevice.
06-15 15:48:44.600  12027-12030  C01304/AbilityManagerService   com.aaa.bbb         I  [ui_ability_thread.cpp(ScheduleAbilityTransaction:344)]Lifecycle: name:EntryAbility,targeState:5,isNewWant:0
06-15 15:48:44.601  12027-12027  C01304/AbilityManagerService   com.aaa.bbb         E  [ui_ability.cpp(HandleCreateAsRecovery:479)]AppRecovery not recovery restart.
06-15 15:48:44.603  12027-12027  A00000/testTag                 com.aaa.bbb         I  Ability onCreate
06-15 15:48:44.603  12027-12027  C01304/AbilityManagerService   com.aaa.bbb         E  [ui_ability_impl.cpp(CheckAndRestore:287)]hasSaveData_ is false.
06-15 15:48:44.604  12027-12027  C01304/AbilityManagerService   com.aaa.bbb         I  [js_ui_ability.cpp(DoOnForegroundForSceneIsNull:725)]JsUIAbility::DoOnForegroundForSceneIsNull displayId  is  0
06-15 15:48:44.604  12027-12027  C04200/WindowScene             com.aaa.bbb         I  <46>Init: [WMSMain]WindowScene init with normal option!
06-15 15:48:44.609  12027-12027  C057c2/IPCObjectProxy          com.aaa.bbb         I  AddDeathRecipient 393: success, handle:15 desc:OHOS.IWindowManager
06-15 15:48:44.611  12027-12027  C057c2/IPCObjectProxy          com.aaa.bbb         I  AddDeathRecipient 393: success, handle:16 desc:ohos.multimodalinput.IConnectManager
06-15 15:48:44.611  12027-12027  C02800/MultimodalInputConnectManager com.aaa.bbb         I  in ConnectMultimodalInputService, Get multimodalinput service successful
06-15 15:48:44.612  12027-12027  C02800/MMIClient               com.aaa.bbb         I  in OnConnected, Connection to server succeeded, fd:47
06-15 15:48:44.612  12027-12027  C02800/MMIClient               com.aaa.bbb         I  in AddFdListener, server was listening
06-15 15:48:44.612  12027-12027  C02800/MMIClient               com.aaa.bbb         I  in StartEventRunner, File fd is in listening
06-15 15:48:44.612  12027-12027  C04200/SingletonContainer      com.aaa.bbb         E  <82>GetSingleton: can not get OHOS::Rosen::WindowInfoReporter
06-15 15:48:44.612  12027-12027  C01304/AbilityManagerService   com.aaa.bbb         E  [ui_ability.cpp(ShouldRecoverState:230)]AppRecovery not recovery restart.
06-15 15:48:44.612  12027-12027  A00000/testTag                 com.aaa.bbb         I  Ability onWindowStageCreate
06-15 15:48:44.612  12027-12044  C057d4/DBinderBaseInvoker      com.aaa.bbb         I  DBinderDatabusInvoker 36: create
06-15 15:48:44.613  12027-12027  C01719/ffrt                    com.aaa.bbb         E  41:TrivalOpenQosCtrlNode:62 task 12027 belong to user 20010032 open qos node failed
06-15 15:48:44.613  12027-12027  C04200/WindowScene             com.aaa.bbb         I  <146>GoForeground: [WMSMain]reason:0
06-15 15:48:44.615  12027-12027  A00000/testTag                 com.aaa.bbb         I  Ability onForeground
06-15 15:48:44.616  12027-12027  A0ff00/BiShare.DeviceManager   com.aaa.bbb         I  DeviceManager created
06-15 15:48:44.616  12027-12027  A0ff00/BiShare.RecordingManager com.aaa.bbb         I  RecordingManager created
06-15 15:48:44.616  12027-12027  A0ff00/BiShare.EventManager    com.aaa.bbb         I  EventManager created
06-15 15:48:44.616  12027-12027  A0ff00/BiShare.BiShareManager  com.aaa.bbb         I  BiShareManager created
06-15 15:48:44.616  12027-12027  A00201/RecordingManagerNapi    com.aaa.bbb         I  获取RecordingManager单例实例
06-15 15:48:44.616  12027-12027  A00201/BiShareCallbacks        com.aaa.bbb         I  🏗️ BiShareCallbacks构造函数开始执行
06-15 15:48:44.616  12027-12027  A00201/BiShareCallbacks        com.aaa.bbb         I  📍 [构造] this指针地址: 0x7f92037678
06-15 15:48:44.616  12027-12027  A00201/BiShareCallbacks        com.aaa.bbb         I  📍 [构造] napiInstance是否有效: 否
06-15 15:48:44.616  12027-12027  A00201/BiShareCallbacks        com.aaa.bbb         I  📍 [构造] 当前静态实例状态: 空
06-15 15:48:44.616  12027-12027  A00201/BiShareCallbacks        com.aaa.bbb         I  ✅ BiShareCallbacks构造函数执行完成
06-15 15:48:44.616  12027-12027  A0001b/InfoService             com.aaa.bbb         I  EventManager --> 添加设备事件监听器
06-15 15:48:44.616  12027-12027  A00201/BiShareNapiInterface    com.aaa.bbb         I  🎯 收到Initialize调用，开始智能路由检测...
06-15 15:48:44.616  12027-12027  A00201/BiShareNapiInterface    com.aaa.bbb         I  📋 Initialize参数数量: 5
06-15 15:48:44.616  12027-12027  A00201/BiShareNapiInterface    com.aaa.bbb         I  🔍 检测最后一个参数类型: function (回调函数)
06-15 15:48:44.616  12027-12027  A00201/BiShareNapiInterface    com.aaa.bbb         I  🔄 [路由] 检测到回调函数，选择回调模式执行Initialize
06-15 15:48:44.616  12027-12027  A00201/BiShareServiceOps       com.aaa.bbb         I  📋 [参数解析] 开始解析isConsole参数...
06-15 15:48:44.616  12027-12027  A00201/BiShareServiceOps       com.aaa.bbb         I  ✅ [参数解析] isConsole: true
06-15 15:48:44.616  12027-12027  A00201/BiShareServiceOps       com.aaa.bbb         I  📋 [参数解析] 开始解析isFile参数...
06-15 15:48:44.616  12027-12027  A00201/BiShareServiceOps       com.aaa.bbb         I  ✅ [参数解析] isFile: true
06-15 15:48:44.616  12027-12027  A00201/BiShareServiceOps       com.aaa.bbb         I  📋 [参数解析] 开始解析logPath参数...
06-15 15:48:44.616  12027-12027  A00201/BiShareServiceOps       com.aaa.bbb         I  📍 [参数解析] logPath长度: 54
06-15 15:48:44.616  12027-12027  A00201/BiShareServiceOps       com.aaa.bbb         I  ✅ [参数解析] logPath解析成功: /data/storage/el2/base/files/service_1749973724000.log
06-15 15:48:44.617  12027-12027  A00201/BiShareServiceOps       com.aaa.bbb         I  📋 [参数解析] 开始解析priority参数...
06-15 15:48:44.617  12027-12027  A00201/BiShareServiceOps       com.aaa.bbb         I  ✅ [参数解析] priority: 7
06-15 15:48:44.617  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  🔧 [主线程] CreateAsyncWork开始执行
06-15 15:48:44.617  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] workData地址: 0x7f90688e70
06-15 15:48:44.617  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] 操作名称: Initialize
06-15 15:48:44.617  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] execute回调地址: 0x7e7cb2cb14
06-15 15:48:44.617  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] complete回调地址: 0x7e7cb2d494
06-15 15:48:44.617  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  🔄 [CreateAsyncWork] 创建Promise...
06-15 15:48:44.617  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] Promise创建状态: 0
06-15 15:48:44.617  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] deferred地址: 0x7f8f3729e0
06-15 15:48:44.617  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] promise地址: 0x7f921f43d8
06-15 15:48:44.617  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  🔄 [CreateAsyncWork] 创建资源名称...
06-15 15:48:44.617  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] 资源名称创建状态: 0
06-15 15:48:44.617  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  🔄 [CreateAsyncWork] 创建异步工作...
06-15 15:48:44.617  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] 异步工作创建状态: 0
06-15 15:48:44.617  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] async_work地址: 0x7f90689290
06-15 15:48:44.617  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  🔄 [CreateAsyncWork] 排队异步工作...
06-15 15:48:44.617  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  📍 [CreateAsyncWork] 异步工作排队状态: 0
06-15 15:48:44.617  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  ✅ [CreateAsyncWork] 异步工作已成功排队
06-15 15:48:44.617  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  🏁 [CreateAsyncWork] CreateAsyncWork执行完成
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  🚀 [后台线程] ExecuteCallback开始执行
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] 线程ID: 547862346160
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] data地址: 0x7f90688e70
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] env地址: 0x7f92b45560
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] workData地址: 0x7f90688e70
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  🔍 [后台线程] 验证workData有效性...
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  ✅ [后台线程] workData有效，操作名称: Initialize
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  🔍 [后台线程] 检查Operation实例指针...
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] operation地址: 0x7f8f3729b0
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  ✅ [后台线程] Operation实例指针有效，准备调用ExecuteOperation
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] 操作名称: Initialize
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  🔍 [后台线程] 验证operation指针有效性...
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  ✅ [后台线程] operation对象验证通过
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  🔄 [后台线程] 调用 Initialize::ExecuteOperation...
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] 调用前workData->result: 0
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  🚀 [后台线程] 直接执行Initialize操作
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  🚀 [直接执行] 开始Initialize操作
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  🔍 [直接执行] 检查服务初始化状态...
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  📍 [直接执行] 当前初始化状态: 未初始化
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  🔧 [直接执行] 验证和转换参数...
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  📍 [直接执行] 原始参数:
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I    - boolParam1: true
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I    - boolParam2: true
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I    - stringParam1: /data/storage/el2/base/files/service_1749973724000.log
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I    - priority: 7
06-15 15:48:44.617  12027-12027  C015b0/NetMgrSubsystem         com.aaa.bbb         I  NETMANAGER_BASE [connection_module.cpp 139] ParseNetConnectionParams no params
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  📍 [直接执行] 转换后参数:
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I    - isConsole: 1
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I    - isFile: 1
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  🔄 [直接执行] 准备调用bishare_service_init...
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  🎯 [直接执行] 开始原生库调用...
06-15 15:48:44.617  12027-12054  A02b66/TAG_WANGR               com.aaa.bbb         D  {BiShareService():15} BiShareService: 2.
06-15 15:48:44.617  12027-12054  A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 8, msg: [bishare][file:BiShareService.cpp - BiShareService()] - bishare_service_init: 1, 1, /data/storage/el2/base/files/service_1749973724000.log, 7.
06-15 15:48:44.617  12027-12054  A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 5, msg: [bishare][file:BString.cpp - BString()] - ctor got NULL, using empty string instead
06-15 15:48:44.617  12027-12054  A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 8, msg: [bishare][file:DeviceBeanManager.cpp - DeviceBeanManager()] - create name: , pwd: 222222.
06-15 15:48:44.617  12027-12054  A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 7, msg: [bishare][file:BiShareService.cpp - BiShareService()] - BiShareService [3.0.20240625] create: /BoeShare.
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  🎯 [直接执行] 原生库调用返回
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  📊 [直接执行] 返回结果: 0 (No error)
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  ✅ [直接执行] 原生服务初始化成功，设置初始化状态...
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  🔗 [直接执行] 开始初始化回调系统...
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  📍 [直接执行] BiShareNapi实例地址: 0x7f906cce90
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  📍 [直接执行] 回调系统实例地址: 0x7f92037678
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  🔧 [直接执行] 设置静态回调实例...
06-15 15:48:44.617  12027-12054  A00201/BiShareCallbacks        com.aaa.bbb         I  🔧 SetStaticInstance开始执行
06-15 15:48:44.617  12027-12054  A00201/BiShareCallbacks        com.aaa.bbb         I  📍 [静态实例] 传入实例地址: 0x7f92037678
06-15 15:48:44.617  12027-12054  A00201/BiShareCallbacks        com.aaa.bbb         I  📍 [静态实例] 传入实例引用计数: 3
06-15 15:48:44.617  12027-12054  A00201/BiShareCallbacks        com.aaa.bbb         I  📍 [静态实例] 当前静态实例: 0
06-15 15:48:44.617  12027-12054  A00201/BiShareCallbacks        com.aaa.bbb         I  📍 [静态实例] 设置后实例地址: 0x7f92037678
06-15 15:48:44.617  12027-12054  A00201/BiShareCallbacks        com.aaa.bbb         I  📍 [静态实例] 设置后引用计数: 4
06-15 15:48:44.617  12027-12054  A00201/BiShareCallbacks        com.aaa.bbb         I  ✅ BiShareCallbacks静态实例设置完成
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  🔗 [直接执行] 开始注册事件回调...
06-15 15:48:44.617  12027-12027  C01304/AbilityManagerService   com.aaa.bbb         E  [ui_ability_impl.cpp(operator():387)]applicationContext or lifecycleCallback is nullptr.
06-15 15:48:44.617  12027-12054  A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 7, msg: [bishare][file:BiShareService.cpp - registerEventCallback()] - registerEventCallback.
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  📊 [直接执行] 事件回调注册结果: 0 (No error)
06-15 15:48:44.617  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  🔗 [直接执行] 开始注册数据包回调...
06-15 15:48:44.617  12027-12054  A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 7, msg: [bishare][file:BiShareService.cpp - registerPacketCallback()] - registerPacketCallback.
06-15 15:48:44.618  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  📊 [直接执行] 数据包回调注册结果: 0 (No error)
06-15 15:48:44.618  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  ✅ [直接执行] 所有回调注册成功
06-15 15:48:44.618  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  🎉 [直接执行] BiShare服务初始化完全成功，回调已注册
06-15 15:48:44.618  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  🏁 [直接执行] Initialize操作执行完成
06-15 15:48:44.618  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  ✅ [后台线程] Initialize操作执行完成
06-15 15:48:44.618  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] 调用后workData->result: 0
06-15 15:48:44.618  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  📊 [后台线程] Initialize::ExecuteOperation执行完成，结果: 0 (No error)
06-15 15:48:44.618  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  🎉 [后台线程] 操作 Initialize 执行成功
06-15 15:48:44.618  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  🏁 [后台线程] ExecuteCallback执行完成: Initialize
06-15 15:48:44.618  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] 最终result: 0 (No error)
06-15 15:48:44.618  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  📍 [后台线程] 最终errorMessage:
06-15 15:48:44.618  12027-12054  A00201/BiShareOperations       com.aaa.bbb         I  🚀 [后台线程] ExecuteCallback即将返回，等待CompleteCallback被调用...
06-15 15:48:44.618  12027-12044  C057c2/IPCObjectProxy          com.aaa.bbb         I  AddDeathRecipient 393: success, handle:17 desc:OHOS.NetManagerStandard.INetConnService
06-15 15:48:44.618  12027-12027  C03900/Ace                     com.aaa.bbb         I  [ui_content_impl.cpp(OHOS_ACE_CreateUIContent)-(-1:-1:undefined)] Ace lib loaded, CreateUIContent.
06-15 15:48:44.618  12027-12027  C03934/AceUIEvent              com.aaa.bbb         I  [ui_event_impl.cpp(InitHandler)-(-1:-1:undefined)] report ace loaded
06-15 15:48:44.618  12027-12030  C015b0/NetConnManager          com.aaa.bbb         W  [OnNetAvailable-(net_conn_callback_stub.cpp:66)]sent raw data is less than 32k
06-15 15:48:44.618  12027-12030  C015b0/NetMgrSubsystem         com.aaa.bbb         I  NETMANAGER_BASE [net_conn_callback_observer.cpp 36] no event listener find netAvailable
06-15 15:48:44.618  12027-12030  C015b0/NetConnManager          com.aaa.bbb         W  [OnNetCapabilitiesChange-(net_conn_callback_stub.cpp:85)]sent raw data is less than 32k
06-15 15:48:44.618  12027-12030  C015b0/NetMgrSubsystem         com.aaa.bbb         I  NETMANAGER_BASE [net_conn_callback_observer.cpp 56] no event listener find netCapabilitiesChange
06-15 15:48:44.618  12027-12027  C03f00/MUSL-LDSO               com.aaa.bbb         E  dlopen_impl load library header failed for libha_ace_engine.z.so
06-15 15:48:44.618  12027-12027  C03934/AceUIEvent              com.aaa.bbb         W  [ui_event_impl.cpp(InitHandler)-(-1:-1:undefined)] Failed to open shared library libha_ace_engine.z.so, reason: Error loading shared library libha_ace_engine.z.so: No such file or directoryn
06-15 15:48:44.618  12027-12030  C015b0/NetConnManager          com.aaa.bbb         W  [OnNetConnectionPropertiesChange-(net_conn_callback_stub.cpp:135)]sent raw data is less than 32k
06-15 15:48:44.618  12027-12030  C015b0/NetMgrSubsystem         com.aaa.bbb         I  NETMANAGER_BASE [net_conn_callback_observer.cpp 79] no event listener find netConnectionPropertiesChange
06-15 15:48:44.619  12027-12044  C015b0/NetMgrSubsystem         com.aaa.bbb         I  NETMANAGER_BASE [connection_exec.cpp 728] Register result 0
06-15 15:48:44.621  12027-12027  C057c2/IPCObjectProxy          com.aaa.bbb         I  AddDeathRecipient 393: success, handle:18 desc:OHOS.Startup.IWatcherManager
06-15 15:48:44.622  12027-12027  C02c03/PARAM_WATCHER           com.aaa.bbb         I  [watcher_manager_kits.cpp:172]Add watcher keyPrefix persist.ace.trace.layout.enabled remoteWatcherId 26 success
06-15 15:48:44.622  12027-12027  C02c03/PARAM_WATCHER           com.aaa.bbb         I  [watcher_manager_kits.cpp:172]Add watcher keyPrefix const.security.developermode.state remoteWatcherId 26 success
06-15 15:48:44.623  12027-12027  C057d4/DBinderBaseInvoker      com.aaa.bbb         I  DBinderDatabusInvoker 36: create
06-15 15:48:44.623  12027-12027  C03900/Ace                     com.aaa.bbb         I  [ace_new_pipe_judgement.cpp(InitAceNewPipeConfig)-(-2:-1:undefined)] Init RenderService UniRender Type:0
06-15 15:48:44.623  12027-12027  C03900/Ace                     com.aaa.bbb         I  [ui_content_impl.cpp(CommonInitialize)-(-2:-1:undefined)] UIContent: apiCompatibleVersion: 10, apiTargetVersion: 10, and apiReleaseType: Release, useNewPipe: 1
06-15 15:48:44.624  12027-12027  C02c03/PARAM_WATCHER           com.aaa.bbb         I  [watcher_manager_kits.cpp:172]Add watcher keyPrefix persist.sys.arkui.animationscale remoteWatcherId 26 success
06-15 15:48:44.624  12027-12027  C03900/Ace                     com.aaa.bbb         I  [localization.cpp(SetLocaleImpl)-(-2:-1:undefined)] SetLocale language tag: zh-Hans-, select language: zh-CN
06-15 15:48:44.624  12027-12027  C03900/Ace                     com.aaa.bbb         I  [ui_content_impl.cpp(CommonInitialize)-(-2:-1:undefined)] Initialize UIContent isModelJson:true
06-15 15:48:44.624  12027-12027  C03900/Ace                     com.aaa.bbb         I  [ace_container.cpp(SetHapPath)-(100000:100000:scope)] SetHapPath, Use hap path to load resource
06-15 15:48:44.627  12027-12027  C04201/DisplayManager          com.aaa.bbb         I  <1145>IDisplayModeListener register success
06-15 15:48:44.627  12027-12027  C01706/rtg_interface           com.aaa.bbb         I  rtg Open fail, errno = 2(No such file or directory), dev = /proc/self/sched_rtg_ctrl
06-15 15:48:44.627  12027-12027  C01406/OHOS::RS                com.aaa.bbb         I  RsFrameReport:[LoadLibrary] load library success!
06-15 15:48:44.627  12027-12027  C01406/OHOS::RS                com.aaa.bbb         I  RsFrameReport:[Init] dlopen libframe_ui_intf.so success!
06-15 15:48:44.627  12027-12027  C01706/ueaClient-RmeCoreSched  com.aaa.bbb         E  [Init]: do not enabled!ret: -1
06-15 15:48:44.627  12027-12027  C01706/ueaClient-FrameMsgMgr   com.aaa.bbb         I  [Init]:inited success!
06-15 15:48:44.627  12027-12027  C01706/ueaClient-FrameUiIntf   com.aaa.bbb         I  [Init]:ret:1, inited:1
06-15 15:48:44.628  12027-12027  C057c2/IPCObjectProxy          com.aaa.bbb         I  AddDeathRecipient 393: success, handle:19 desc:ohos.accessibility.IAccessibleAbilityManagerService
06-15 15:48:44.629  12027-12027  C01d03/accessibility_acfwk     com.aaa.bbb         I  [accessibility_config_impl.cpp(SubscribeConfigObserver:321)]id = [0]
06-15 15:48:44.629  12027-12060  C02c03/PARAM_WATCHER           com.aaa.bbb         E  [watcher_manager_kits.cpp:166]Failed to add callback for persist.sys.graphic.animationscale
06-15 15:48:44.629  12027-12027  C02c03/PARAM_WATCHER           com.aaa.bbb         I  [watcher_manager_kits.cpp:172]Add watcher keyPrefix persist.sys.graphic.animationscale remoteWatcherId 26 success
06-15 15:48:44.629  12027-12060  C02c03/PARAM_WATCHER           com.aaa.bbb         E  [watcher_manager_kits.cpp:344]SystemWatchParameter is failed! keyPrefix is:persist.sys.graphic.animationscale, errNum is:110
06-15 15:48:44.629  12027-12060  C02c0b/BEGET                   com.aaa.bbb         E  [service_watcher.c:83]WatchParameter failed! the errNum is 110
06-15 15:48:44.630  12027-12027  C03900/Ace                     com.aaa.bbb         I  [jsi_view_register_impl.cpp(JsUINodeRegisterCleanUp)-(100000:100000:scope)] CleanUpIdleTask is a valid function
06-15 15:48:44.631  12027-12061  C057c2/IPCObjectProxy          com.aaa.bbb         I  AddDeathRecipient 393: success, handle:20 desc:OHOS.ResourceSchedule.ResSchedService
06-15 15:48:44.631  12027-12027  C03900/Ace                     com.aaa.bbb         I  [ace_container.cpp(AttachView)-(100000:100000:scope)] New pipeline version creating...
06-15 15:48:44.632  12027-12027  C03900/Ace                     com.aaa.bbb         I  [hap_asset_provider_impl.cpp(GetAssetList)-(100000:100000:scope)] Cannot Get File List from resources/styles/
06-15 15:48:44.632  12027-12027  C03900/Ace                     com.aaa.bbb         W  [asset_manager_impl.cpp(GetAsset)-(100000:100000:scope)] GetAsset failed, assetName = resources/styles/default.json
06-15 15:48:44.664  12027-12027  C02c03/PARAM_WATCHER           com.aaa.bbb         I  [watcher_manager_kits.cpp:172]Add watcher keyPrefix debug.graphic.frame remoteWatcherId 26 success
06-15 15:48:44.664  12027-12027  C04200/WindowImpl              com.aaa.bbb         I  <359>GetAvoidAreaByType Search Type: 1
06-15 15:48:44.664  12027-12027  C04200/WindowImpl              com.aaa.bbb         I  <359>GetAvoidAreaByType Search Type: 4
06-15 15:48:44.664  12027-12061  C02c03/PARAM_WATCHER           com.aaa.bbb         I  [watcher_manager_kits.cpp:172]Add watcher keyPrefix debug_layer remoteWatcherId 26 success
06-15 15:48:44.665  12027-12027  C03903/AceSubWindow            com.aaa.bbb         I  [ui_content_impl.cpp(InitializeDisplayAvailableRect)-(100000:100000:scope)] DisplayAvailableRect info: 0, 0, 0, 0
06-15 15:48:44.665  12027-12027  C03900/Ace                     com.aaa.bbb         I  [ui_content_impl.cpp(InitializeInner)-(-2:100000:singleton)] Initialize startUrl = pages/Index
06-15 15:48:44.665  12027-12027  C03900/Ace                     com.aaa.bbb         I  [frontend_delegate_declarative.cpp(RunPage)-(100000:100000:scope)] FrontendDelegateDeclarative RunPage url=pages/Index
06-15 15:48:44.665  12027-12027  C03900/Ace                     com.aaa.bbb         W  [asset_manager_impl.cpp(GetAsset)-(100000:100000:scope)] GetAsset failed, assetName = manifest.json
06-15 15:48:44.665  12027-12027  C03900/Ace                     com.aaa.bbb         I  [frontend_delegate_declarative.cpp(RunPage)-(100000:100000:scope)] Parse profile main_pages.json
06-15 15:48:44.665  12027-12027  C03900/Ace                     com.aaa.bbb         I  [ui_content_impl.cpp(UpdateWindowMode)-(-2:100000:singleton)] UIContentImpl: UpdateWindowMode, window mode is 1, hasDeco is 1
06-15 15:48:44.665  12027-12027  C03900/Ace                     com.aaa.bbb         I  [ui_content_impl.cpp(UpdateDecorVisible)-(-2:100000:singleton)] UIContentImpl: UpdateWindowMode, window visible is 0, hasDeco is 1
06-15 15:48:44.665  12027-12027  C03900/Ace                     com.aaa.bbb         I  [ui_content_impl.cpp(Foreground)-(-2:100000:singleton)] [com.aaa.bbb][entry]: window foreground
06-15 15:48:44.665  12027-12027  C03900/Ace                     com.aaa.bbb         I  [jsi_declarative_engine.cpp(UpdateApplicationState)-(100000:100000:scope)] Update application state , state: ON_SHOW
06-15 15:48:44.665  12027-12027  C04200/VsyncStation            com.aaa.bbb         I  <88>MainEventRunner is available
06-15 15:48:44.666  12027-12027  C03900/Ace                     com.aaa.bbb         I  [ui_content_impl.cpp(HideWindowTitleButton)-(-2:100000:singleton)] HideWindowTitleButton hideSplit: 0, hideMaximize: 0, hideMinimize: 0
06-15 15:48:44.666  12027-12027  C03900/Ace                     com.aaa.bbb         I  [ui_content_impl.cpp(UpdateViewportConfig)-(-2:100000:singleton)] UIContentImpl: UpdateViewportConfig Viewport config: size: (1920, 1080) orientation: 0 density: 1.000000 position: (0, 0)
06-15 15:48:44.667  12027-12027  A00000/testTag                 com.aaa.bbb         I  Succeeded in loading the content. Data:
06-15 15:48:44.667  12027-12027  C04200/JsWindowStage           com.aaa.bbb         I  <331>[NAPI]Window [19, harmony0] load content end, ret = 0
06-15 15:48:44.667  12027-12027  C01120/BundleMgrService        com.aaa.bbb         I  [bundle_manager.cpp(CheckToCache):2521] put applicationInfo to cache
06-15 15:48:44.667  12027-12027  C01120/BundleMgrService        com.aaa.bbb         I  [bundle_manager.cpp(CheckToCache):2525] put applicationInfo to cache locked
06-15 15:48:44.668  12027-12027  C03900/Ace                     com.aaa.bbb         I  [jsi_declarative_engine.cpp(UpdateApplicationState)-(100000:100000:scope)] Update application state , state: ON_ACTIVE
06-15 15:48:44.668  12027-12027  C03924/AceAccessibility        com.aaa.bbb         I  [ace_application_info.h(SetAccessibilityEnabled)-(100000:100000:scope)] AceApplicationInfo set accessibility enabled:0
06-15 15:48:44.668  12027-12027  C01d03/accessibility_acfwk     com.aaa.bbb         I  [accessibility_config_impl.cpp(SubscribeConfigObserver:321)]id = [3]
06-15 15:48:44.668  12027-12027  C03924/AceAccessibility        com.aaa.bbb         I  [js_accessibility_manager.cpp(OnConfigChanged)-(100000:100000:scope)] accessibility content timeout changed:0
06-15 15:48:44.668  12027-12027  C01d02/accessibility_asacfwk   com.aaa.bbb         I  [accessibility_system_ability_client_impl.cpp(SubscribeStateObserver:358)]Observer has subscribed!
06-15 15:48:44.668  12027-12027  C03924/AceAccessibility        com.aaa.bbb         I  [js_accessibility_manager.cpp(SubscribeStateObserver)-(100000:100000:scope)]  the result of SubscribeStateObserver:4001
06-15 15:48:44.668  12027-12027  C0391c/AceFocus                com.aaa.bbb         I  [pipeline_context.cpp(WindowFocus)-(100000:100000:scope)] Window id: 19 get focus.
06-15 15:48:44.668  12027-12054  C057c2/IPCObjectProxy          com.aaa.bbb         I  AddDeathRecipient 393: success, handle:22 desc:ohos.security.accesstoken.IAccessTokenManager
06-15 15:48:44.668  12027-12027  C03900/Ace                     com.aaa.bbb         I  [page_router_manager.cpp(LoadPage)-(100000:100000:scope)] Page router manager is loading page[1]: pages/Index.
06-15 15:48:44.669  12027-12027  C03900/Ace                     com.aaa.bbb         I  [jsi_declarative_engine.cpp(UpdateRootComponent)-(100000:100000:scope)] update rootComponent start
06-15 15:48:44.669  12027-12061  C01400/OpenGLWrapper           com.aaa.bbb         I  <62>Init: initialized ver=1.4
06-15 15:48:44.670  12027-12027  C03926/AceTheme                com.aaa.bbb         W  [theme_style.h(GetAttr)-(100000:100000:scope)] style text_pattern not contains bg_color_selected!
06-15 15:48:44.670  12027-12027  C03926/AceTheme                com.aaa.bbb         W  [theme_style.h(GetAttr)-(100000:100000:scope)] style text_pattern not contains linear_split_child_min_size!
06-15 15:48:44.671  12027-12027  C01406/OHOS::RS                com.aaa.bbb         E  RSNode::GetChildIdByIndex, index out of bound
06-15 15:48:44.671  12027-12027  C03933/AceKeyboard             com.aaa.bbb         I  [stage_manager.cpp(PageChangeCloseKeyboard)-(100000:100000:scope)] StageManager FrameNode notNeedSoftKeyboard.
06-15 15:48:44.671  12027-12027  C03933/AceKeyboard             com.aaa.bbb         I  [stage_manager.cpp(PageChangeCloseKeyboard)-(100000:100000:scope)] Container not ScenceBoardWindow.
06-15 15:48:44.671  12027-12027  C03933/AceKeyboard             com.aaa.bbb         I  [focus_hub.cpp(PushPageCloseKeyboard)-(100000:100000:scope)] PageChange CloseKeyboard FrameNode notNeedSoftKeyboard.
06-15 15:48:44.671  12027-12027  C01c10/ImsaKit                 com.aaa.bbb         I  line: 364, function: Close,run in
06-15 15:48:44.671  12027-12027  C01c10/ImsaKit                 com.aaa.bbb         I  line: 161, function: GetSystemAbilityProxy,get input method service proxy
06-15 15:48:44.671  12027-12027  C057c2/IPCObjectProxy          com.aaa.bbb         I  AddDeathRecipient 393: success, handle:23 desc:ohos.miscservices.inputmethod.IInputMethodSystemAbility
06-15 15:48:44.671  12027-12027  C01c10/ImsaKit                 com.aaa.bbb         I  line: 271, function: SendRequest,IMSAProxy, code = 7
06-15 15:48:44.672  12027-12027  C03933/AceKeyboard             com.aaa.bbb         I  [focus_hub.cpp(PushPageCloseKeyboard)-(100000:100000:scope)] PageChange CloseKeyboard SoftKeyboard Closes Successfully.
06-15 15:48:44.672  12027-12027  C03900/Ace                     com.aaa.bbb         I  [stage_manager.cpp(PushPage)-(100000:100000:scope)] waiting for window size
06-15 15:48:44.672  12027-12027  C03900/Ace                     com.aaa.bbb         I  [page_router_manager.cpp(LoadPage)-(100000:100000:scope)] LoadPage Success
06-15 15:48:44.672  12027-12027  C03900/Ace                     com.aaa.bbb         I  [container_modal_pattern.cpp(ShowTitle)-(100000:100000:scope)] ShowTitle isShow: 0, windowMode: 1, hasDeco: 1
06-15 15:48:44.672  12027-12027  C03900/Ace                     com.aaa.bbb         I  [container_modal_pattern.cpp(SetContainerButtonHide)-(100000:100000:scope)] Set containerModal button status successfully, hideSplit: 0, hideMaximize: 0, hideMinimize: 0
06-15 15:48:44.673  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  🔄 [主线程] CompleteCallback开始执行
06-15 15:48:44.673  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  📍 [主线程] 线程ID: 547918655568
06-15 15:48:44.673  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  📍 [主线程] napi_status: 0
06-15 15:48:44.673  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  📍 [主线程] data地址: 0x7f90688e70
06-15 15:48:44.673  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  📍 [主线程] env地址: 0x7f92b45560
06-15 15:48:44.673  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  📍 [主线程] workData地址: 0x7f90688e70
06-15 15:48:44.673  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  📍 [主线程] 操作: Initialize, 结果: 0 (No error)
06-15 15:48:44.673  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  ✅ [主线程] 操作成功，创建成功响应
06-15 15:48:44.673  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  📤 [主线程] 调用napi_resolve_deferred...
06-15 15:48:44.673  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  📍 [主线程] deferred地址: 0x7f8f3729e0
06-15 15:48:44.673  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  📍 [主线程] napiResult地址: 0x7f921f4068
06-15 15:48:44.673  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  📍 [主线程] napi_resolve_deferred返回状态: 0
06-15 15:48:44.673  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  ✅ [主线程] Promise已成功resolve
06-15 15:48:44.673  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  🧹 [主线程] 开始清理资源...
06-15 15:48:44.673  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  📍 [主线程] 删除async_work...
06-15 15:48:44.673  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  ✅ [主线程] async_work已删除
06-15 15:48:44.673  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  📍 [主线程] 删除workData...
06-15 15:48:44.673  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  📍 [主线程] workData地址: 0x7f90688e70
06-15 15:48:44.673  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  ✅ [主线程] workData已删除
06-15 15:48:44.673  12027-12027  A00201/BiShareOperations       com.aaa.bbb         I  🏁 [主线程] CompleteCallback执行完成: Initialize
06-15 15:48:44.673  12027-12027  C015b0/NetMgrSubsystem         com.aaa.bbb         I  NETMANAGER_BASE [event_listener.cpp 50] ~EventListener() is called, to delete EventListener
06-15 15:48:44.673  12027-12027  C015b0/NetMgrSubsystem         com.aaa.bbb         I  NETMANAGER_BASE [event_listener.cpp 50] ~EventListener() is called, to delete EventListener
06-15 15:48:44.673  12027-12027  C015b0/NetMgrSubsystem         com.aaa.bbb         I  NETMANAGER_BASE [event_listener.cpp 50] ~EventListener() is called, to delete EventListener
06-15 15:48:44.673  12027-12027  C015b0/NetMgrSubsystem         com.aaa.bbb         I  NETMANAGER_BASE [event_listener.cpp 50] ~EventListener() is called, to delete EventListener
06-15 15:48:44.673  12027-12027  C01719/ffrt                    com.aaa.bbb         E  76:TrivalOpenQosCtrlNode:62 task 12027 belong to user 20010032 open qos node failed
06-15 15:48:44.676  12027-12061  C01406/OHOS::RS                com.aaa.bbb         I  debug.graphic.overdraw disable
06-15 15:48:44.677  12027-12061  C02c03/PARAM_WATCHER           com.aaa.bbb         I  [watcher_manager_kits.cpp:172]Add watcher keyPrefix debug.graphic.overdraw remoteWatcherId 26 success
06-15 15:48:44.678  12027-12054  C01719/ffrt                    com.aaa.bbb         E  85:TrivalOpenQosCtrlNode:62 task 12027 belong to user 20010032 open qos node failed
06-15 15:48:44.678  12027-12054  C057d4/DBinderBaseInvoker      com.aaa.bbb         I  DBinderDatabusInvoker 36: create
06-15 15:48:44.678  12027-12061  C02c03/PARAM_WATCHER           com.aaa.bbb         I  [watcher_manager_kits.cpp:172]Add watcher keyPrefix debug.graphic.colors_overdraw remoteWatcherId 26 success
06-15 15:48:44.678  12027-12027  C01719/ffrt                    com.aaa.bbb         E  93:TrivalOpenQosCtrlNode:62 task 12027 belong to user 20010032 open qos node failed
06-15 15:48:44.678  12027-12027  C03f01/NAPI                    com.aaa.bbb         E  [(native_engine.cpp:559)(AddCleanupHook)] AddCleanupHook Failed.
06-15 15:48:44.678  12027-12027  C01120/BundleMgrService        com.aaa.bbb         I  [bundle_manager.cpp(GetBundleInfoComplete):2844] query cache GetBundleInfoComplete
06-15 15:48:44.678  12027-12027  C01120/BundleMgrService        com.aaa.bbb         I  [bundle_manager.cpp(GetBundleInfoComplete):2849] cache exsit GetBundleInfoComplete
06-15 15:48:44.679  12027-12027  A0001b/InfoService             com.aaa.bbb         I  InfoCmdManager --> getNetCapabilities networkCap [12,16]
06-15 15:48:44.679  12027-12027  A0001b/InfoService             com.aaa.bbb         I  InfoCmdManager --> getNetCapabilities bearerTypes [1]
06-15 15:48:44.679  12027-12027  C03900/Ace                     com.aaa.bbb         W  [jsi_base_utils.cpp(JsLogPrint)-(-2:100000:singleton)] ace Log: AppStorage instance missing. Use AppStorage.createInstance(initObj). Creating instance without any initialization.
06-15 15:48:44.679  12027-12027  A0001b/InfoService             com.aaa.bbb         I  InfoCmdManager --> notifyMqttNetChange isNetAvailable is true...
06-15 15:48:44.679  12027-12027  A0001b/InfoService             com.aaa.bbb         I  EntryAbility --> networkListen isConnected: true
06-15 15:48:44.679  12027-12027  A0001b/InfoService             com.aaa.bbb         W  EntryAbility --> Network connected but BiShare is not initialized yet. Skipping setNetworkInfo.
06-15 15:48:44.680  12027-12078  C01719/ffrt                    com.aaa.bbb         E  126:operator():32 [-1943012944] set priority failed ret[1] errno[0]
06-15 15:48:44.684  12027-12027  C03926/AceTheme                com.aaa.bbb         W  [theme_style.h(GetAttr)-(100000:100000:scope)] style -1 not contains image_pattern!
06-15 15:48:44.685  12027-12027  C03900/Ace                     com.aaa.bbb         I  [layout_wrapper.cpp(AvoidKeyboard)-(100000:100000:scope)] AvoidKeyboard KeyboardOffset: 0.000000, setOffset: 40.000000
06-15 15:48:44.686  12027-12027  C01406/OHOS::RS                com.aaa.bbb         E  RSNode::GetChildIdByIndex, index out of bound
06-15 15:48:44.686  12027-12027  C01406/OHOS::RS                com.aaa.bbb         E  RSNode::GetChildIdByIndex, index out of bound
06-15 15:48:44.686  12027-12027  C01406/OHOS::RS                com.aaa.bbb         E  RSNode::GetChildIdByIndex, index out of bound
06-15 15:48:44.686  12027-12027  C01406/OHOS::RS                com.aaa.bbb         E  RSNode::GetChildIdByIndex, index out of bound
06-15 15:48:44.686  12027-12027  C01406/OHOS::RS                com.aaa.bbb         E  RSNode::GetChildIdByIndex, index out of bound
06-15 15:48:44.686  12027-12027  C01406/OHOS::RS                com.aaa.bbb         E  RSNode::GetChildIdByIndex, index out of bound
06-15 15:48:44.686  12027-12027  C01406/OHOS::RS                com.aaa.bbb         E  RSNode::GetChildIdByIndex, index out of bound
06-15 15:48:44.687  12027-12027  C0391c/AceFocus                com.aaa.bbb         I  [focus_hub.cpp(OnFocusNode)-(100000:100000:scope)] Node(root/0) on focus
06-15 15:48:44.687  12027-12027  C0391c/AceFocus                com.aaa.bbb         I  [focus_hub.cpp(OnFocusNode)-(100000:100000:scope)] Node(Column/4) on focus
06-15 15:48:44.687  12027-12027  C0391c/AceFocus                com.aaa.bbb         I  [focus_hub.cpp(OnFocusNode)-(100000:100000:scope)] Node(Stack/3) on focus
06-15 15:48:44.687  12027-12027  C0391c/AceFocus                com.aaa.bbb         I  [focus_hub.cpp(OnFocusNode)-(100000:100000:scope)] Node(page/30) on focus
06-15 15:48:44.687  12027-12027  C0391c/AceFocus                com.aaa.bbb         I  [focus_hub.cpp(OnFocusNode)-(100000:100000:scope)] Node(Row/34) on focus
06-15 15:48:44.687  12027-12027  C0391c/AceFocus                com.aaa.bbb         I  [pipeline_context.cpp(RequestDefaultFocus)-(100000:100000:scope)] Target view has no default focus. Request focus on view root: Row/34 return: 1.
06-15 15:48:44.687  12027-12027  C03933/AceKeyboard             com.aaa.bbb         I  [pipeline_context.cpp(IsNotSCBWindowKeyboard)-(100000:100000:scope)] Normal WindowPage ready to close keyboard.
06-15 15:48:44.687  12027-12027  C03933/AceKeyboard             com.aaa.bbb         I  [focus_hub.cpp(IsCloseKeyboard)-(100000:100000:scope)] FrameNode(Row/34) notNeedSoftKeyboard.
06-15 15:48:44.687  12027-12027  C03933/AceKeyboard             com.aaa.bbb         I  [focus_hub.cpp(IsCloseKeyboard)-(100000:100000:scope)] SoftKeyboard Closes Successfully.
06-15 15:48:44.687  12027-12027  C01c10/ImsaKit                 com.aaa.bbb         I  line: 364, function: Close,run in
06-15 15:48:44.687  12027-12027  C01c10/ImsaKit                 com.aaa.bbb         I  line: 271, function: SendRequest,IMSAProxy, code = 7
06-15 15:48:44.688  12027-12027  A0001b/InfoService             com.aaa.bbb         I  InfoCmdManager --> getConnectionProperties interfaceName: "wlan0"
06-15 15:48:44.688  12027-12027  A0001b/InfoService             com.aaa.bbb         I  InfoCmdManager --> getConnectionProperties domains: ""
06-15 15:48:44.688  12027-12027  A0001b/InfoService             com.aaa.bbb         I  InfoCmdManager --> getConnectionProperties linkAddresses: [{"address":{"address":"************37","family":1,"port":0},"prefixLength":24},{"address":{"address":"2408:8456:3223:4cca:9eb8:b4ff:fe62:66f4","family":2,"port":0},"prefixLength":0}]
06-15 15:48:44.688  12027-12027  A0001b/InfoService             com.aaa.bbb         I  InfoCmdManager --> getConnectionProperties routes: [{"interface":"wlan0","destination":{"address":{"address":"0.0.0.0","family":1,"port":0},"prefixLength":0},"gateway":{"address":"************","prefixLength":0},"hasGateway":true,"isDefaultRoute":false},{"interface":"wlan0","destination":{"address":{"address":"::","family":2,"port":0},"prefixLength":0},"gateway":{"address":"fe80::76c1:4fff:fee0:22ae","prefixLength":0},"hasGateway":true,"isDefaultRoute":false}]
06-15 15:48:44.688  12027-12027  A0001b/InfoService             com.aaa.bbb         I  InfoCmdManager --> getConnectionProperties dnses: [{"address":"************","family":1,"port":0},{"address":"","family":1,"port":0},{"address":"fe80::76c1:4fff:fee0:22ae","family":2,"port":0},{"address":"*","family":2,"port":0}]
06-15 15:48:44.688  12027-12027  A0001b/InfoService             com.aaa.bbb         I  InfoCmdManager --> getConnectionProperties mtu: 0
06-15 15:48:44.688  12027-12077  C057d4/DBinderBaseInvoker      com.aaa.bbb         I  DBinderDatabusInvoker 36: create
06-15 15:48:44.688  12027-12027  C03f01/NAPI                    com.aaa.bbb         E  [(native_engine.cpp:559)(AddCleanupHook)] AddCleanupHook Failed.
06-15 15:48:44.688  12027-12027  C01120/BundleMgrService        com.aaa.bbb         I  [bundle_manager.cpp(GetBundleInfoComplete):2844] query cache GetBundleInfoComplete
06-15 15:48:44.688  12027-12027  C01120/BundleMgrService        com.aaa.bbb         I  [bundle_manager.cpp(GetBundleInfoComplete):2849] cache exsit GetBundleInfoComplete
06-15 15:48:44.689  12027-12027  C03f01/NAPI                    com.aaa.bbb         E  [(native_engine.cpp:559)(AddCleanupHook)] AddCleanupHook Failed.
06-15 15:48:44.689  12027-12027  C01120/BundleMgrService        com.aaa.bbb         I  [bundle_manager.cpp(GetBundleInfoComplete):2844] query cache GetBundleInfoComplete
06-15 15:48:44.689  12027-12027  C01120/BundleMgrService        com.aaa.bbb         I  [bundle_manager.cpp(GetBundleInfoComplete):2849] cache exsit GetBundleInfoComplete
06-15 15:48:44.689  12027-12027  C03f01/NAPI                    com.aaa.bbb         E  [(native_engine.cpp:559)(AddCleanupHook)] AddCleanupHook Failed.
06-15 15:48:44.689  12027-12027  C01120/BundleMgrService        com.aaa.bbb         I  [bundle_manager.cpp(GetBundleInfoComplete):2844] query cache GetBundleInfoComplete
06-15 15:48:44.689  12027-12027  C01120/BundleMgrService        com.aaa.bbb         I  [bundle_manager.cpp(GetBundleInfoComplete):2849] cache exsit GetBundleInfoComplete
06-15 15:48:44.689  12027-12027  A0001b/InfoService             com.aaa.bbb         I  EntryAbility --> checkPermissions havePermissionDeny
06-15 15:48:44.689  12027-12027  A0001b/InfoService             com.aaa.bbb         I  EntryAbility --> reqPermissionsFromUser permissions 5
06-15 15:48:44.689  12027-12027  C05a01/AccessTokenAbilityAccessCtrl com.aaa.bbb         I  [ParseRequestPermissionFromUser:1078]asyncContext.uiAbilityFlag is: 1.
06-15 15:48:44.689  12027-12027  C05a02/CommonNapi              com.aaa.bbb         I  [ParseStringArray:139]array size is 5
06-15 15:48:44.689  12027-12027  C05a01/AccessTokenAbilityAccessCtrl com.aaa.bbb         I  [ParseRequestPermissionFromUser:1088]asyncContext.permissionList size: 5.
06-15 15:48:44.690  12027-12047  C05a01/ATMProxy                com.aaa.bbb         I  [GetSelfPermissionsState:257]result from server data = 1
06-15 15:48:44.690  12027-12047  C05a01/AccessTokenAbilityAccessCtrl com.aaa.bbb         I  [RequestPermissionsFromUserExecute:1542]pop service extension dialog
06-15 15:48:44.690  12027-12047  C01304/AbilityManagerService   com.aaa.bbb         I  [ability_manager_client.cpp(RequestDialogService:974)]request is:/com.ohos.permissionmanager//com.ohos.permissionmanager.GrantAbility.
06-15 15:48:44.690  12027-12077  C01719/ffrt                    com.aaa.bbb         I  205:operator():86 submit task delay time [10000 us] has ended.
06-15 15:48:44.690  12027-12047  C01300/Ability                 com.aaa.bbb         I  [ability_manager_proxy.cpp(RequestDialogService:3702)]RequestDialogService Call
06-15 15:48:44.690  12027-12077  C01719/ffrt                    com.aaa.bbb         I  209:DecDepRef:47 Undependency completed, enter ready queue, task[4], name[t3]
06-15 15:48:44.697  12027-12047  C05a01/AccessTokenAbilityAccessCtrl com.aaa.bbb         I  [StartServiceExtension:1267]RequestDialogService end. ret=0
06-15 15:48:44.701  12027-12075  C01719/ffrt                    com.aaa.bbb         I  258:operator():86 submit task delay time [10000 us] has ended.
06-15 15:48:44.701  12027-12075  C01719/ffrt                    com.aaa.bbb         I  262:DecDepRef:47 Undependency completed, enter ready queue, task[6], name[t5]
06-15 15:48:44.902  12027-12030  C03900/Ace                     com.aaa.bbb         I  [ui_content_impl.cpp(UnFocus)-(-1:100000:singleton)] com.aaa.bbb window unfocus
06-15 15:48:44.902  12027-12027  C01304/AbilityManagerService   com.aaa.bbb         E  [ui_ability_impl.cpp(operator():387)]applicationContext or lifecycleCallback is nullptr.
06-15 15:48:44.902  12027-12027  C03900/Ace                     com.aaa.bbb         I  [jsi_declarative_engine.cpp(UpdateApplicationState)-(100000:100000:scope)] Update application state , state: ON_INACTIVE
06-15 15:48:44.902  12027-12027  C0391c/AceFocus                com.aaa.bbb         I  [pipeline_context.cpp(WindowFocus)-(100000:100000:scope)] Window id: 19 lost focus.
06-15 15:48:44.903  12027-12027  C0391c/AceFocus                com.aaa.bbb         I  [focus_hub.cpp(OnBlurNode)-(100000:100000:scope)] Node(root/0) on blur by 1
06-15 15:48:44.903  12027-12027  C0391c/AceFocus                com.aaa.bbb         I  [focus_hub.cpp(OnBlurNode)-(100000:100000:scope)] Node(Column/4) on blur by 1
06-15 15:48:44.903  12027-12027  C0391c/AceFocus                com.aaa.bbb         I  [focus_hub.cpp(OnBlurNode)-(100000:100000:scope)] Node(Stack/3) on blur by 1
06-15 15:48:44.904  12027-12027  C0391c/AceFocus                com.aaa.bbb         I  [focus_hub.cpp(OnBlurNode)-(100000:100000:scope)] Node(page/30) on blur by 1
06-15 15:48:44.904  12027-12027  C0391c/AceFocus                com.aaa.bbb         I  [focus_hub.cpp(OnBlurNode)-(100000:100000:scope)] Node(Row/34) on blur by 1
06-15 15:48:44.923  12027-12054  C01719/ffrt                    com.aaa.bbb         I  310:operator():86 submit task delay time [10000 us] has ended.
06-15 15:48:44.923  12027-12054  C01719/ffrt                    com.aaa.bbb         I  314:DecDepRef:47 Undependency completed, enter ready queue, task[8], name[t7]
06-15 15:48:44.935  12027-12044  C01719/ffrt                    com.aaa.bbb         I  362:operator():86 submit task delay time [10000 us] has ended.
06-15 15:48:44.935  12027-12044  C01719/ffrt                    com.aaa.bbb         I  366:DecDepRef:47 Undependency completed, enter ready queue, task[10], name[t9]
06-15 15:48:45.878  12027-12035  C03f00/ArkCompiler             com.aaa.bbb         I  [gc] SmartGC: finish app cold start
06-15 15:48:49.617  12027-12058  A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 8, msg: [bishare][file:DeviceStateMachine.cpp - DeviceState()] - State name: DeviceInitState
06-15 15:48:49.617  12027-12058  A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 8, msg: [bishare][file:DeviceStateMachine.cpp - DeviceState()] - State name: DeviceConnectState
06-15 15:48:49.617  12027-12058  A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 8, msg: [bishare][file:DeviceStateMachine.cpp - DeviceState()] - State name: DeviceSenderState
06-15 15:48:49.617  12027-12058  A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 8, msg: [bishare][file:DeviceStateMachine.cpp - DeviceState()] - State name: DeviceReceiverState
06-15 15:48:49.617  12027-12058  A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 4, msg: [bishare][file:DeviceStateMachine.cpp - DeviceReceiverState()] - DeviceReceiverState created
06-15 15:48:49.617  12027-12058  A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 7, msg: [bishare][file:DeviceStateMachine.cpp - setDeviceStateMachine()] - Enter state: DeviceInitState
06-15 15:48:49.617  12027-12058  A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 8, msg: [bishare][file:RtpStreamProxy.cpp - init()] - RtpStreamProxy init.
06-15 15:48:49.618  12027-12058  A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 8, msg: [bishare][file:CommManager.cpp - CommManager()] - create
06-15 15:48:49.618  12027-12058  A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 8, msg: [bishare][file:MonitorsManager.cpp - enumMonitors()] - MonitorsManager::enumMonitors
06-15 15:48:49.618  12027-12058  A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 8, msg: [bishare][file:DeviceManager.cpp - start()] - EncodecVideo h264
06-15 15:48:49.618  12027-12058  A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 8, msg: [bishare][file:DeviceManager.cpp - start()] - finished.
06-15 15:48:49.618  12027-12058  A00201/BiShareCallbacks        com.aaa.bbb         I  📞 OnEventCallback被调用 - type: 2, len: 79
06-15 15:48:49.618  12027-12058  A00201/BiShareCallbacks        com.aaa.bbb         I  📍 [事件回调] 调用线程ID: 547861559728
06-15 15:48:49.618  12027-12058  A00201/BiShareCallbacks        com.aaa.bbb         I  📍 [事件回调] 静态实例状态: 存在 (地址: 0x7f92037678)
06-15 15:48:49.618  12027-12058  A00201/BiShareCallbacks        com.aaa.bbb         I  📍 [事件回调] 参数验证通过
06-15 15:48:49.618  12027-12058  A00201/BiShareCallbacks        com.aaa.bbb         I  📍 [事件回调] value内容:
06-15 15:48:49.618  12027-12058  A00201/BiShareCallbacks        com.aaa.bbb         I  📍 [事件回调] 静态实例引用计数: 2
06-15 15:48:49.618  12027-12058  A00201/BiShareCallbacks        com.aaa.bbb         I  🔄 [事件回调] 调用EnqueueEventCallback...
06-15 15:48:49.618  12027-12058  A00201/BiShareCallbacks        com.aaa.bbb         I  事件回调已入队，类型: 2
06-15 15:48:49.618  12027-12058  A00201/BiShareCallbacks        com.aaa.bbb         I  ✅ [事件回调] EnqueueEventCallback调用成功
06-15 15:48:49.697  12027-12047  C01719/ffrt                    com.aaa.bbb         I  387:Detach:114 qos 3 thread not joinable.
06-15 15:48:49.697  12027-12047  C057c4/IPCThreadSkeleton       com.aaa.bbb         I  ~IPCThreadSkeleton 86: 12027_12047
06-15 15:48:49.923  12027-12054  C01719/ffrt                    com.aaa.bbb         I  390:Detach:114 qos 2 thread not joinable.
06-15 15:48:49.923  12027-12054  C057c4/IPCThreadSkeleton       com.aaa.bbb         I  ~IPCThreadSkeleton 86: 12027_12054
06-15 15:48:49.923  12027-12054  C057d4/DBinderBaseInvoker      com.aaa.bbb         I  ~DBinderDatabusInvoker 41: destroy
06-15 15:48:49.923  12027-12076  C01719/ffrt                    com.aaa.bbb         I  393:Detach:114 qos 2 thread not joinable.
06-15 15:48:49.923  12027-12076  C057c4/IPCThreadSkeleton       com.aaa.bbb         I  ~IPCThreadSkeleton 86: 12027_12076
06-15 15:48:49.925  12027-12075  C01719/ffrt                    com.aaa.bbb         I  396:Detach:114 qos 2 thread not joinable.
06-15 15:48:49.925  12027-12075  C057c4/IPCThreadSkeleton       com.aaa.bbb         I  ~IPCThreadSkeleton 86: 12027_12075
06-15 15:48:49.935  12027-12044  C01719/ffrt                    com.aaa.bbb         I  399:Detach:114 qos 2 thread not joinable.
06-15 15:48:49.936  12027-12044  C057c4/IPCThreadSkeleton       com.aaa.bbb         I  ~IPCThreadSkeleton 86: 12027_12044
06-15 15:48:49.936  12027-12044  C057d4/DBinderBaseInvoker      com.aaa.bbb         I  ~DBinderDatabusInvoker 41: destroy
06-15 15:48:49.936  12027-12077  C01719/ffrt                    com.aaa.bbb         I  402:Detach:114 qos 2 thread not joinable.
06-15 15:48:49.936  12027-12077  C057c4/IPCThreadSkeleton       com.aaa.bbb         I  ~IPCThreadSkeleton 86: 12027_12077
06-15 15:48:49.936  12027-12077  C057d4/DBinderBaseInvoker      com.aaa.bbb         I  ~DBinderDatabusInvoker 41: destroy
