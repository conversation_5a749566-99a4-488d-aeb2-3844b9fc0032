06-15 15:33:43.130  9808-9808    A00201/BiShareFacade           pid-9808                        I  开始初始化BiShareFacade...
06-15 15:33:43.130  9808-9808    A00201/BiShareFacade           pid-9808                        I  创建服务管理器...
06-15 15:33:43.130  9808-9808    A00201/BiShareFacade           pid-9808                        I  服务管理器初始化成功
06-15 15:33:43.130  9808-9808    A00201/BiShareFacade           pid-9808                        I  创建回调管理器...
06-15 15:33:43.130  9808-9808    A00201/BiShareFacade           pid-9808                        I  回调管理器初始化成功
06-15 15:33:43.130  9808-9808    A00201/BiShareFacade           pid-9808                        I  BiShareFacade初始化完成
06-15 15:33:43.130  9808-9808    A00201/RecordingManagerNapi    pid-9808                        I  初始化RecordingManager类
06-15 15:33:43.130  9808-9808    A00201/RecordingManagerNapi    pid-9808                        I  RecordingManager类初始化成功
06-15 15:33:43.130  9808-9808    A00201/DeviceManagerNapi       pid-9808                        I  初始化DeviceManager类
06-15 15:33:43.130  9808-9808    A00201/DeviceManagerNapi       pid-9808                        I  DeviceManager类初始化成功
06-15 15:33:43.130  9808-9808    A00201/BiShareManagerNapi      pid-9808                        I  初始化BiShareManager类
06-15 15:33:43.130  9808-9808    A00201/BiShareManagerNapi      pid-9808                        I  BiShareManager类初始化成功
06-15 15:33:43.132  9808-9808    A03d00/JSAPP                   pid-9808                        I  Callee constructor is OK string
06-15 15:33:43.132  9808-9808    A03d00/JSAPP                   pid-9808                        I  Ability::constructor callee is object [object Object]
06-15 15:33:43.133  9808-9808    C01304/AbilityManagerService   pid-9808                        I  [ui_ability_thread.cpp(AttachInner:170)]LoadLifecycle: Attach uiability.
06-15 15:33:43.133  9808-9808    C057c2/IPCObjectProxy          pid-9808                        I  AddDeathRecipient 393: success, handle:10 desc:ohos.aafwk.AbilityManager
06-15 15:33:43.135  9808-9808    C01305/Appkit                  pid-9808                        E  [application_context.cpp(NotifyApplicationForeground:254)]applicationStateCallback is nullptr
06-15 15:33:43.135  9808-9808    C03f00/ArkCompiler             pid-9808                        I  [gc] app is not inBackground
06-15 15:33:43.135  9808-9808    C03f00/ArkCompiler             pid-9808                        I  [gc] Heap Growing Type HIGH_THROUGHPUT
06-15 15:33:43.135  9808-9808    C01707/CONCUR                  pid-9808                        E  [Qos] qoslevel 3 apply for tid 9817 failure
06-15 15:33:43.135  9808-9808    C01707/CONCUR                  pid-9808                        E  [Qos] qoslevel 3 apply for tid 9818 failure
06-15 15:33:43.135  9808-9808    C01707/CONCUR                  pid-9808                        E  [Qos] qoslevel 3 apply for tid 9819 failure
06-15 15:33:43.135  9808-9808    C01707/CONCUR                  pid-9808                        E  [Qos] qoslevel 3 apply for tid 9820 failure
06-15 15:33:43.136  9808-9808    C02b12/AFWK                    pid-9808                        I  [napi_audio_volume_group_manager.cpp] [Construct]Construct() 1
06-15 15:33:43.136  9808-9808    C02b12/AFWK                    pid-9808                        I  [audio_group_manager.cpp] [Init]AudioGroupManager::init set networkId LocalDevice.
06-15 15:33:43.136  9808-9812    C01304/AbilityManagerService   pid-9808                        I  [ui_ability_thread.cpp(ScheduleAbilityTransaction:344)]Lifecycle: name:EntryAbility,targeState:5,isNewWant:0
06-15 15:33:43.137  9808-9808    C01304/AbilityManagerService   pid-9808                        E  [ui_ability.cpp(HandleCreateAsRecovery:479)]AppRecovery not recovery restart.
06-15 15:33:43.140  9808-9808    A00000/testTag                 pid-9808                        I  Ability onCreate
06-15 15:33:43.140  9808-9808    C01304/AbilityManagerService   pid-9808                        E  [ui_ability_impl.cpp(CheckAndRestore:287)]hasSaveData_ is false.
06-15 15:33:43.141  9808-9808    C01304/AbilityManagerService   pid-9808                        I  [js_ui_ability.cpp(DoOnForegroundForSceneIsNull:725)]JsUIAbility::DoOnForegroundForSceneIsNull displayId  is  0
06-15 15:33:43.141  9808-9808    C04200/WindowScene             pid-9808                        I  <46>Init: [WMSMain]WindowScene init with normal option!
06-15 15:33:43.143  9808-9808    C057c2/IPCObjectProxy          pid-9808                        I  AddDeathRecipient 393: success, handle:15 desc:OHOS.IWindowManager
06-15 15:33:43.145  9808-9808    C057c2/IPCObjectProxy          pid-9808                        I  AddDeathRecipient 393: success, handle:16 desc:ohos.multimodalinput.IConnectManager
06-15 15:33:43.145  9808-9808    C02800/MultimodalInputConnectManager pid-9808                        I  in ConnectMultimodalInputService, Get multimodalinput service successful
06-15 15:33:43.146  9808-9808    C02800/MMIClient               pid-9808                        I  in OnConnected, Connection to server succeeded, fd:47
06-15 15:33:43.146  9808-9808    C02800/MMIClient               pid-9808                        I  in AddFdListener, server was listening
06-15 15:33:43.146  9808-9808    C02800/MMIClient               pid-9808                        I  in StartEventRunner, File fd is in listening
06-15 15:33:43.146  9808-9808    C04200/SingletonContainer      pid-9808                        E  <82>GetSingleton: can not get OHOS::Rosen::WindowInfoReporter
06-15 15:33:43.147  9808-9808    C01304/AbilityManagerService   pid-9808                        E  [ui_ability.cpp(ShouldRecoverState:230)]AppRecovery not recovery restart.
06-15 15:33:43.148  9808-9808    A00000/testTag                 pid-9808                        I  Ability onWindowStageCreate
06-15 15:33:43.148  9808-9826    C057d4/DBinderBaseInvoker      pid-9808                        I  DBinderDatabusInvoker 36: create
06-15 15:33:43.149  9808-9808    C01719/ffrt                    pid-9808                        E  42:TrivalOpenQosCtrlNode:62 task 9808 belong to user 20010032 open qos node failed
06-15 15:33:43.149  9808-9808    C04200/WindowScene             pid-9808                        I  <146>GoForeground: [WMSMain]reason:0
06-15 15:33:43.154  9808-9808    A00000/testTag                 pid-9808                        I  Ability onForeground
06-15 15:33:43.155  9808-9808    A0ff00/BiShare.DeviceManager   pid-9808                        I  DeviceManager created
06-15 15:33:43.155  9808-9808    A0ff00/BiShare.RecordingManager pid-9808                        I  RecordingManager created
06-15 15:33:43.155  9808-9808    A0ff00/BiShare.EventManager    pid-9808                        I  EventManager created
06-15 15:33:43.155  9808-9808    A0ff00/BiShare.BiShareManager  pid-9808                        I  BiShareManager created
06-15 15:33:43.155  9808-9808    A00201/RecordingManagerNapi    pid-9808                        I  获取RecordingManager单例实例
06-15 15:33:43.155  9808-9808    A00201/BiShareCallbacks        pid-9808                        I  🏗️ BiShareCallbacks构造函数开始执行
06-15 15:33:43.155  9808-9808    A00201/BiShareCallbacks        pid-9808                        I  📍 [构造] this指针地址: 0x7f92037678
06-15 15:33:43.155  9808-9808    A00201/BiShareCallbacks        pid-9808                        I  📍 [构造] napiInstance是否有效: 否
06-15 15:33:43.155  9808-9808    A00201/BiShareCallbacks        pid-9808                        I  📍 [构造] 当前静态实例状态: 空
06-15 15:33:43.155  9808-9808    A00201/BiShareCallbacks        pid-9808                        I  ✅ BiShareCallbacks构造函数执行完成
06-15 15:33:43.155  9808-9808    A0001b/InfoService             pid-9808                        I  EventManager --> 添加设备事件监听器
06-15 15:33:43.156  9808-9808    A00201/BiShareNapiInterface    pid-9808                        I  🎯 收到Initialize调用，开始智能路由检测...
06-15 15:33:43.156  9808-9808    A00201/BiShareNapiInterface    pid-9808                        I  📋 Initialize参数数量: 5
06-15 15:33:43.156  9808-9808    A00201/BiShareNapiInterface    pid-9808                        I  🔍 检测最后一个参数类型: function (回调函数)
06-15 15:33:43.156  9808-9808    A00201/BiShareNapiInterface    pid-9808                        I  🔄 [路由] 检测到回调函数，选择异步模式执行Initialize
06-15 15:33:43.156  9808-9808    A00201/BiShareServiceOps       pid-9808                        I  📋 [参数解析] 开始解析isConsole参数...
06-15 15:33:43.156  9808-9808    A00201/BiShareServiceOps       pid-9808                        I  ✅ [参数解析] isConsole: true
06-15 15:33:43.156  9808-9808    A00201/BiShareServiceOps       pid-9808                        I  📋 [参数解析] 开始解析isFile参数...
06-15 15:33:43.156  9808-9808    A00201/BiShareServiceOps       pid-9808                        I  ✅ [参数解析] isFile: true
06-15 15:33:43.156  9808-9808    A00201/BiShareServiceOps       pid-9808                        I  📋 [参数解析] 开始解析logPath参数...
06-15 15:33:43.156  9808-9808    A00201/BiShareServiceOps       pid-9808                        I  📍 [参数解析] logPath长度: 54
06-15 15:33:43.156  9808-9808    A00201/BiShareServiceOps       pid-9808                        I  ✅ [参数解析] logPath解析成功: /data/storage/el2/base/files/service_1749972823000.log
06-15 15:33:43.156  9808-9808    A00201/BiShareServiceOps       pid-9808                        I  📋 [参数解析] 开始解析priority参数...
06-15 15:33:43.156  9808-9808    A00201/BiShareServiceOps       pid-9808                        I  ✅ [参数解析] priority: 7
06-15 15:33:43.156  9808-9808    A00201/BiShareOperations       pid-9808                        I  🔧 [主线程] CreateAsyncWork开始执行
06-15 15:33:43.156  9808-9808    A00201/BiShareOperations       pid-9808                        I  📍 [CreateAsyncWork] workData地址: 0x7f90688e80
06-15 15:33:43.156  9808-9808    A00201/BiShareOperations       pid-9808                        I  📍 [CreateAsyncWork] 操作名称: Initialize
06-15 15:33:43.156  9808-9808    A00201/BiShareOperations       pid-9808                        I  📍 [CreateAsyncWork] execute回调地址: 0x7e7cfecb14
06-15 15:33:43.156  9808-9808    A00201/BiShareOperations       pid-9808                        I  📍 [CreateAsyncWork] complete回调地址: 0x7e7cfed494
06-15 15:33:43.156  9808-9808    A00201/BiShareOperations       pid-9808                        I  🔄 [CreateAsyncWork] 创建Promise...
06-15 15:33:43.156  9808-9808    A00201/BiShareOperations       pid-9808                        I  📍 [CreateAsyncWork] Promise创建状态: 0
06-15 15:33:43.156  9808-9808    A00201/BiShareOperations       pid-9808                        I  📍 [CreateAsyncWork] deferred地址: 0x7f8f3729e0
06-15 15:33:43.156  9808-9808    A00201/BiShareOperations       pid-9808                        I  📍 [CreateAsyncWork] promise地址: 0x7f921f43d8
06-15 15:33:43.156  9808-9808    A00201/BiShareOperations       pid-9808                        I  🔄 [CreateAsyncWork] 创建资源名称...
06-15 15:33:43.156  9808-9808    A00201/BiShareOperations       pid-9808                        I  📍 [CreateAsyncWork] 资源名称创建状态: 0
06-15 15:33:43.156  9808-9808    A00201/BiShareOperations       pid-9808                        I  🔄 [CreateAsyncWork] 创建异步工作...
06-15 15:33:43.156  9808-9808    A00201/BiShareOperations       pid-9808                        I  📍 [CreateAsyncWork] 异步工作创建状态: 0
06-15 15:33:43.156  9808-9808    A00201/BiShareOperations       pid-9808                        I  📍 [CreateAsyncWork] async_work地址: 0x7f906892a0
06-15 15:33:43.156  9808-9808    A00201/BiShareOperations       pid-9808                        I  🔄 [CreateAsyncWork] 排队异步工作...
06-15 15:33:43.156  9808-9808    A00201/BiShareOperations       pid-9808                        I  📍 [CreateAsyncWork] 异步工作排队状态: 0
06-15 15:33:43.156  9808-9808    A00201/BiShareOperations       pid-9808                        I  ✅ [CreateAsyncWork] 异步工作已成功排队
06-15 15:33:43.156  9808-9808    A00201/BiShareOperations       pid-9808                        I  🏁 [CreateAsyncWork] CreateAsyncWork执行完成
06-15 15:33:43.156  9808-9835    A00201/BiShareOperations       pid-9808                        I  🚀 [后台线程] ExecuteCallback开始执行
06-15 15:33:43.156  9808-9835    A00201/BiShareOperations       pid-9808                        I  📍 [后台线程] 线程ID: 547862346160
06-15 15:33:43.156  9808-9808    C015b0/NetMgrSubsystem         pid-9808                        I  NETMANAGER_BASE [connection_module.cpp 139] ParseNetConnectionParams no params
06-15 15:33:43.156  9808-9835    A00201/BiShareOperations       pid-9808                        I  📍 [后台线程] data地址: 0x7f90688e80
06-15 15:33:43.156  9808-9835    A00201/BiShareOperations       pid-9808                        I  📍 [后台线程] env地址: 0x7f92b45560
06-15 15:33:43.156  9808-9835    A00201/BiShareOperations       pid-9808                        I  📍 [后台线程] workData地址: 0x7f90688e80
06-15 15:33:43.156  9808-9835    A00201/BiShareOperations       pid-9808                        I  🔍 [后台线程] 验证workData有效性...
06-15 15:33:43.156  9808-9835    A00201/BiShareOperations       pid-9808                        I  ✅ [后台线程] workData有效，操作名称: Initialize
06-15 15:33:43.156  9808-9835    A00201/BiShareOperations       pid-9808                        I  🔍 [后台线程] 检查Operation实例指针...
06-15 15:33:43.156  9808-9835    A00201/BiShareOperations       pid-9808                        I  📍 [后台线程] operation地址: 0x7f8f3729b0
06-15 15:33:43.156  9808-9835    A00201/BiShareOperations       pid-9808                        I  ✅ [后台线程] Operation实例指针有效，准备调用ExecuteOperation
06-15 15:33:43.156  9808-9835    A00201/BiShareOperations       pid-9808                        I  📍 [后台线程] 操作名称: Initialize
06-15 15:33:43.156  9808-9835    A00201/BiShareOperations       pid-9808                        I  🔍 [后台线程] 验证operation指针有效性...
06-15 15:33:43.156  9808-9835    A00201/BiShareOperations       pid-9808                        I  ✅ [后台线程] operation对象验证通过
06-15 15:33:43.156  9808-9835    A00201/BiShareOperations       pid-9808                        I  🔄 [后台线程] 调用 Initialize::ExecuteOperation...
06-15 15:33:43.156  9808-9835    A00201/BiShareOperations       pid-9808                        I  📍 [后台线程] 调用前workData->result: 0
06-15 15:33:43.156  9808-9835    A00201/BiShareOperations       pid-9808                        I  🚀 [后台线程] 直接执行Initialize操作
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  🚀 [直接执行] 开始Initialize操作
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  🔍 [直接执行] 检查服务初始化状态...
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  📍 [直接执行] 当前初始化状态: 未初始化
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  🔧 [直接执行] 验证和转换参数...
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  📍 [直接执行] 原始参数:
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I    - boolParam1: true
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I    - boolParam2: true
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I    - stringParam1: /data/storage/el2/base/files/service_1749972823000.log
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I    - priority: 7
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  📍 [直接执行] 转换后参数:
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I    - isConsole: 1
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I    - isFile: 1
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  🔄 [直接执行] 准备调用bishare_service_init...
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  🎯 [直接执行] 开始原生库调用...
06-15 15:33:43.157  9808-9835    A02b66/TAG_WANGR               pid-9808                        D  {BiShareService():15} BiShareService: 2.
06-15 15:33:43.157  9808-9835    A02b66/TAG_WANGR               pid-9808                        D  {sendNotify():55} level: 8, msg: [bishare][file:BiShareService.cpp - BiShareService()] - bishare_service_init: 1, 1, /data/storage/el2/base/files/service_1749972823000.log, 7.
06-15 15:33:43.157  9808-9835    A02b66/TAG_WANGR               pid-9808                        D  {sendNotify():55} level: 5, msg: [bishare][file:BString.cpp - BString()] - ctor got NULL, using empty string instead
06-15 15:33:43.157  9808-9835    A02b66/TAG_WANGR               pid-9808                        D  {sendNotify():55} level: 8, msg: [bishare][file:DeviceBeanManager.cpp - DeviceBeanManager()] - create name: , pwd: 222222.
06-15 15:33:43.157  9808-9835    A02b66/TAG_WANGR               pid-9808                        D  {sendNotify():55} level: 7, msg: [bishare][file:BiShareService.cpp - BiShareService()] - BiShareService [3.0.20240625] create: /BoeShare.
06-15 15:33:43.157  9808-9826    C057c2/IPCObjectProxy          pid-9808                        I  AddDeathRecipient 393: success, handle:17 desc:OHOS.NetManagerStandard.INetConnService
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  🎯 [直接执行] 原生库调用返回
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  📊 [直接执行] 返回结果: 0 (No error)
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  ✅ [直接执行] 原生服务初始化成功，设置初始化状态...
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  🔗 [直接执行] 开始初始化回调系统...
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  📍 [直接执行] BiShareNapi实例地址: 0x7f906cce90
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  📍 [直接执行] 回调系统实例地址: 0x7f92037678
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  🔧 [直接执行] 设置静态回调实例...
06-15 15:33:43.157  9808-9835    A00201/BiShareCallbacks        pid-9808                        I  🔧 SetStaticInstance开始执行
06-15 15:33:43.157  9808-9835    A00201/BiShareCallbacks        pid-9808                        I  📍 [静态实例] 传入实例地址: 0x7f92037678
06-15 15:33:43.157  9808-9835    A00201/BiShareCallbacks        pid-9808                        I  📍 [静态实例] 传入实例引用计数: 3
06-15 15:33:43.157  9808-9835    A00201/BiShareCallbacks        pid-9808                        I  📍 [静态实例] 当前静态实例: 0
06-15 15:33:43.157  9808-9835    A00201/BiShareCallbacks        pid-9808                        I  📍 [静态实例] 设置后实例地址: 0x7f92037678
06-15 15:33:43.157  9808-9835    A00201/BiShareCallbacks        pid-9808                        I  📍 [静态实例] 设置后引用计数: 4
06-15 15:33:43.157  9808-9835    A00201/BiShareCallbacks        pid-9808                        I  ✅ BiShareCallbacks静态实例设置完成
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  🔗 [直接执行] 开始注册事件回调...
06-15 15:33:43.157  9808-9835    A02b66/TAG_WANGR               pid-9808                        D  {sendNotify():55} level: 7, msg: [bishare][file:BiShareService.cpp - registerEventCallback()] - registerEventCallback.
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  📊 [直接执行] 事件回调注册结果: 0 (No error)
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  🔗 [直接执行] 开始注册数据包回调...
06-15 15:33:43.157  9808-9835    A02b66/TAG_WANGR               pid-9808                        D  {sendNotify():55} level: 7, msg: [bishare][file:BiShareService.cpp - registerPacketCallback()] - registerPacketCallback.
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  📊 [直接执行] 数据包回调注册结果: 0 (No error)
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  ✅ [直接执行] 所有回调注册成功
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  🎉 [直接执行] BiShare服务初始化完全成功，回调已注册
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  🏁 [直接执行] Initialize操作执行完成
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  ✅ [后台线程] Initialize操作执行完成
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  📍 [后台线程] 调用后workData->result: 0
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  📊 [后台线程] Initialize::ExecuteOperation执行完成，结果: 0 (No error)
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  🎉 [后台线程] 操作 Initialize 执行成功
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  🏁 [后台线程] ExecuteCallback执行完成: Initialize
06-15 15:33:43.157  9808-9835    A00201/BiShareOperations       pid-9808                        I  📍 [后台线程] 最终result: 0 (No error)
06-15 15:33:43.158  9808-9835    A00201/BiShareOperations       pid-9808                        I  📍 [后台线程] 最终errorMessage:
06-15 15:33:43.158  9808-9835    A00201/BiShareOperations       pid-9808                        I  🚀 [后台线程] ExecuteCallback即将返回，等待CompleteCallback被调用...
06-15 15:33:43.158  9808-9808    C01304/AbilityManagerService   pid-9808                        E  [ui_ability_impl.cpp(operator():387)]applicationContext or lifecycleCallback is nullptr.
06-15 15:33:43.158  9808-9808    C03900/Ace                     pid-9808                        I  [ui_content_impl.cpp(OHOS_ACE_CreateUIContent)-(-1:-1:undefined)] Ace lib loaded, CreateUIContent.
06-15 15:33:43.158  9808-9808    C03934/AceUIEvent              pid-9808                        I  [ui_event_impl.cpp(InitHandler)-(-1:-1:undefined)] report ace loaded
06-15 15:33:43.159  9808-9808    C03f00/MUSL-LDSO               pid-9808                        E  dlopen_impl load library header failed for libha_ace_engine.z.so
06-15 15:33:43.159  9808-9808    C03934/AceUIEvent              pid-9808                        W  [ui_event_impl.cpp(InitHandler)-(-1:-1:undefined)] Failed to open shared library libha_ace_engine.z.so, reason: Error loading shared library libha_ace_engine.z.so: No such file or directoryn
06-15 15:33:43.159  9808-9812    C015b0/NetConnManager          pid-9808                        W  [OnNetAvailable-(net_conn_callback_stub.cpp:66)]sent raw data is less than 32k
06-15 15:33:43.159  9808-9812    C015b0/NetMgrSubsystem         pid-9808                        I  NETMANAGER_BASE [net_conn_callback_observer.cpp 36] no event listener find netAvailable
06-15 15:33:43.159  9808-9812    C015b0/NetConnManager          pid-9808                        W  [OnNetCapabilitiesChange-(net_conn_callback_stub.cpp:85)]sent raw data is less than 32k
06-15 15:33:43.159  9808-9812    C015b0/NetMgrSubsystem         pid-9808                        I  NETMANAGER_BASE [net_conn_callback_observer.cpp 56] no event listener find netCapabilitiesChange
06-15 15:33:43.159  9808-9812    C015b0/NetConnManager          pid-9808                        W  [OnNetConnectionPropertiesChange-(net_conn_callback_stub.cpp:135)]sent raw data is less than 32k
06-15 15:33:43.160  9808-9812    C015b0/NetMgrSubsystem         pid-9808                        I  NETMANAGER_BASE [net_conn_callback_observer.cpp 79] no event listener find netConnectionPropertiesChange
06-15 15:33:43.160  9808-9808    C057c2/IPCObjectProxy          pid-9808                        I  AddDeathRecipient 393: success, handle:18 desc:OHOS.Startup.IWatcherManager
06-15 15:33:43.160  9808-9826    C015b0/NetMgrSubsystem         pid-9808                        I  NETMANAGER_BASE [connection_exec.cpp 728] Register result 0
06-15 15:33:43.162  9808-9808    C02c03/PARAM_WATCHER           pid-9808                        I  [watcher_manager_kits.cpp:172]Add watcher keyPrefix persist.ace.trace.layout.enabled remoteWatcherId 24 success
06-15 15:33:43.163  9808-9808    C02c03/PARAM_WATCHER           pid-9808                        I  [watcher_manager_kits.cpp:172]Add watcher keyPrefix const.security.developermode.state remoteWatcherId 24 success
06-15 15:33:43.164  9808-9808    C057d4/DBinderBaseInvoker      pid-9808                        I  DBinderDatabusInvoker 36: create
06-15 15:33:43.164  9808-9808    C03900/Ace                     pid-9808                        I  [ace_new_pipe_judgement.cpp(InitAceNewPipeConfig)-(-2:-1:undefined)] Init RenderService UniRender Type:0
06-15 15:33:43.164  9808-9808    C03900/Ace                     pid-9808                        I  [ui_content_impl.cpp(CommonInitialize)-(-2:-1:undefined)] UIContent: apiCompatibleVersion: 10, apiTargetVersion: 10, and apiReleaseType: Release, useNewPipe: 1
06-15 15:33:43.166  9808-9808    C02c03/PARAM_WATCHER           pid-9808                        I  [watcher_manager_kits.cpp:172]Add watcher keyPrefix persist.sys.arkui.animationscale remoteWatcherId 24 success
06-15 15:33:43.167  9808-9808    C03900/Ace                     pid-9808                        I  [localization.cpp(SetLocaleImpl)-(-2:-1:undefined)] SetLocale language tag: zh-Hans-, select language: zh-CN
06-15 15:33:43.167  9808-9808    C03900/Ace                     pid-9808                        I  [ui_content_impl.cpp(CommonInitialize)-(-2:-1:undefined)] Initialize UIContent isModelJson:true
06-15 15:33:43.167  9808-9808    C03900/Ace                     pid-9808                        I  [ace_container.cpp(SetHapPath)-(100000:100000:scope)] SetHapPath, Use hap path to load resource
06-15 15:33:43.168  9808-9808    C04201/DisplayManager          pid-9808                        I  <1145>IDisplayModeListener register success
06-15 15:33:43.168  9808-9808    C01706/rtg_interface           pid-9808                        I  rtg Open fail, errno = 2(No such file or directory), dev = /proc/self/sched_rtg_ctrl
06-15 15:33:43.168  9808-9808    C01406/OHOS::RS                pid-9808                        I  RsFrameReport:[LoadLibrary] load library success!
06-15 15:33:43.168  9808-9808    C01406/OHOS::RS                pid-9808                        I  RsFrameReport:[Init] dlopen libframe_ui_intf.so success!
06-15 15:33:43.168  9808-9808    C01706/ueaClient-RmeCoreSched  pid-9808                        E  [Init]: do not enabled!ret: -1
06-15 15:33:43.168  9808-9808    C01706/ueaClient-FrameMsgMgr   pid-9808                        I  [Init]:inited success!
06-15 15:33:43.168  9808-9808    C01706/ueaClient-FrameUiIntf   pid-9808                        I  [Init]:ret:1, inited:1
06-15 15:33:43.169  9808-9808    C057c2/IPCObjectProxy          pid-9808                        I  AddDeathRecipient 393: success, handle:19 desc:ohos.accessibility.IAccessibleAbilityManagerService
06-15 15:33:43.171  9808-9808    C01d03/accessibility_acfwk     pid-9808                        I  [accessibility_config_impl.cpp(SubscribeConfigObserver:321)]id = [0]
06-15 15:33:43.172  9808-9808    C02c03/PARAM_WATCHER           pid-9808                        I  [watcher_manager_kits.cpp:172]Add watcher keyPrefix persist.sys.graphic.animationscale remoteWatcherId 24 success
06-15 15:33:43.172  9808-9838    C02c03/PARAM_WATCHER           pid-9808                        E  [watcher_manager_kits.cpp:166]Failed to add callback for persist.sys.graphic.animationscale
06-15 15:33:43.172  9808-9838    C02c03/PARAM_WATCHER           pid-9808                        E  [watcher_manager_kits.cpp:344]SystemWatchParameter is failed! keyPrefix is:persist.sys.graphic.animationscale, errNum is:110
06-15 15:33:43.172  9808-9838    C02c0b/BEGET                   pid-9808                        E  [service_watcher.c:83]WatchParameter failed! the errNum is 110
06-15 15:33:43.174  9808-9808    C03900/Ace                     pid-9808                        I  [jsi_view_register_impl.cpp(JsUINodeRegisterCleanUp)-(100000:100000:scope)] CleanUpIdleTask is a valid function
06-15 15:33:43.174  9808-9839    C057c2/IPCObjectProxy          pid-9808                        I  AddDeathRecipient 393: success, handle:20 desc:OHOS.ResourceSchedule.ResSchedService
06-15 15:33:43.175  9808-9808    C03900/Ace                     pid-9808                        I  [ace_container.cpp(AttachView)-(100000:100000:scope)] New pipeline version creating...
06-15 15:33:43.177  9808-9808    C03900/Ace                     pid-9808                        I  [hap_asset_provider_impl.cpp(GetAssetList)-(100000:100000:scope)] Cannot Get File List from resources/styles/
06-15 15:33:43.177  9808-9808    C03900/Ace                     pid-9808                        W  [asset_manager_impl.cpp(GetAsset)-(100000:100000:scope)] GetAsset failed, assetName = resources/styles/default.json
06-15 15:33:43.181  9808-9808    C03900/Ace                     pid-9808                        I  [rosen_window.cpp(SetRootFrameNode)-(100000:100000:scope)] Rosenwindow set root frame node
06-15 15:33:43.181  9808-9808    C03900/Ace                     pid-9808                        I  [container_modal_view.cpp(BuildTitle)-(100000:100000:scope)] ContainerModalView BuildTitle called
06-15 15:33:43.181  9808-9808    C03f01/NAPI                    pid-9808                        I  [(native_module_manager.cpp:746)(LoadModuleLibrary)] path: /system/lib64/module/arkui/libdrawabledescriptor.z.so, pathKey: default, isAppModule: 0
06-15 15:33:43.183  9808-9808    C03900/Ace                     pid-9808                        I  [container_modal_view.cpp(BuildTitle)-(100000:100000:scope)] ContainerModalView BuildTitle called
06-15 15:33:43.184  9808-9808    C03926/AceTheme                pid-9808                        W  [theme_style.h(GetAttr)-(100000:100000:scope)] style -1 not contains icon_pattern!
06-15 15:33:43.184  9808-9808    C03900/Ace                     pid-9808                        I  [background_task_executor.cpp(BackgroundTaskExecutor)-(100000:100000:scope)] Create ace bg threads pool.
06-15 15:33:43.205  9808-9839    C02c03/PARAM_WATCHER           pid-9808                        I  [watcher_manager_kits.cpp:172]Add watcher keyPrefix debug_layer remoteWatcherId 24 success
06-15 15:33:43.207  9808-9839    C01400/OpenGLWrapper           pid-9808                        I  <62>Init: initialized ver=1.4
06-15 15:33:43.212  9808-9808    C02b05/ImageSource             pid-9808                        I  CreatePixelMapExtended success, imageId:1749972823188464, desiredSize: (0, 0),imageSize: (768, 768), cost 18065 us
06-15 15:33:43.212  9808-9808    C03900/Ace                     pid-9808                        I  [container_modal_pattern.cpp(SetAppIcon)-(100000:100000:scope)] SetAppIcon successfully
06-15 15:33:43.213  9808-9808    C03900/Ace                     pid-9808                        I  [container_modal_pattern.cpp(SetAppTitle)-(100000:100000:scope)] SetAppTitle successfully, title is 投屏
06-15 15:33:43.213  9808-9808    C01d02/accessibility_asacfwk   pid-9808                        E  [accessibility_system_ability_client_impl.cpp(AccessibilitySystemAbilityClientImpl:55)]accessibility service is ready.
06-15 15:33:43.214  9808-9839    C02c03/PARAM_WATCHER           pid-9808                        I  [watcher_manager_kits.cpp:172]Add watcher keyPrefix debug.graphic.frame remoteWatcherId 24 success
06-15 15:33:43.214  9808-9839    C01406/OHOS::RS                pid-9808                        I  debug.graphic.overdraw disable
06-15 15:33:43.215  9808-9839    C02c03/PARAM_WATCHER           pid-9808                        I  [watcher_manager_kits.cpp:172]Add watcher keyPrefix debug.graphic.overdraw remoteWatcherId 24 success
06-15 15:33:43.215  9808-9808    C02c03/PARAM_WATCHER           pid-9808                        I  [watcher_manager_kits.cpp:172]Add watcher keyPrefix accessibility.config.ready remoteWatcherId 24 success
06-15 15:33:43.215  9808-9808    C03924/AceAccessibility        pid-9808                        I  [ace_application_info.h(SetAccessibilityEnabled)-(100000:100000:scope)] AceApplicationInfo set accessibility enabled:0
06-15 15:33:43.215  9808-9808    C01d03/accessibility_acfwk     pid-9808                        I  [accessibility_config_impl.cpp(SubscribeConfigObserver:321)]id = [3]
06-15 15:33:43.215  9808-9808    C03924/AceAccessibility        pid-9808                        I  [js_accessibility_manager.cpp(OnConfigChanged)-(100000:100000:scope)] accessibility content timeout changed:0
06-15 15:33:43.215  9808-9808    C03924/AceAccessibility        pid-9808                        I  [js_accessibility_manager.cpp(SubscribeStateObserver)-(100000:100000:scope)]  the result of SubscribeStateObserver:0
06-15 15:33:43.215  9808-9808    C03900/Ace                     pid-9808                        I  [ui_content_impl.cpp(CommonInitialize)-(100000:100000:scope)] UIContentImpl: focus again
06-15 15:33:43.215  9808-9808    C03900/Ace                     pid-9808                        I  [ui_content_impl.cpp(Focus)-(100000:100000:scope)] com.aaa.bbb window focus
06-15 15:33:43.216  9808-9808    C03900/Ace                     pid-9808                        E  [ace_container.cpp(IsFontFileExistInPath)-(100000:100000:scope)] ERROR EACCES
06-15 15:33:43.216  9808-9808    C03900/Ace                     pid-9808                        E  [ace_container.cpp(IsFontFileExistInPath)-(100000:100000:scope)] ERROR EACCES
06-15 15:33:43.216  9808-9808    C03900/Ace                     pid-9808                        I  [ui_content_impl.cpp(CommonInitialize)-(100000:100000:scope)] SetMinPlatformVersion is 10
06-15 15:33:43.216  9808-9808    C04200/WindowImpl              pid-9808                        I  <359>GetAvoidAreaByType Search Type: 0
06-15 15:33:43.216  9808-9808    C04200/WindowImpl              pid-9808                        I  <359>GetAvoidAreaByType Search Type: 1
06-15 15:33:43.216  9808-9808    C04200/WindowImpl              pid-9808                        I  <359>GetAvoidAreaByType Search Type: 4
06-15 15:33:43.216  9808-9839    C02c03/PARAM_WATCHER           pid-9808                        I  [watcher_manager_kits.cpp:172]Add watcher keyPrefix debug.graphic.colors_overdraw remoteWatcherId 24 success
06-15 15:33:43.216  9808-9808    C03903/AceSubWindow            pid-9808                        I  [ui_content_impl.cpp(InitializeDisplayAvailableRect)-(100000:100000:scope)] DisplayAvailableRect info: 0, 0, 0, 0
06-15 15:33:43.217  9808-9808    C03900/Ace                     pid-9808                        I  [ui_content_impl.cpp(InitializeInner)-(-2:100000:singleton)] Initialize startUrl = pages/Index
06-15 15:33:43.217  9808-9808    C03900/Ace                     pid-9808                        I  [frontend_delegate_declarative.cpp(RunPage)-(100000:100000:scope)] FrontendDelegateDeclarative RunPage url=pages/Index
06-15 15:33:43.217  9808-9808    C03900/Ace                     pid-9808                        W  [asset_manager_impl.cpp(GetAsset)-(100000:100000:scope)] GetAsset failed, assetName = manifest.json
06-15 15:33:43.217  9808-9808    C03900/Ace                     pid-9808                        I  [frontend_delegate_declarative.cpp(RunPage)-(100000:100000:scope)] Parse profile main_pages.json
06-15 15:33:43.217  9808-9808    C03900/Ace                     pid-9808                        I  [ui_content_impl.cpp(UpdateWindowMode)-(-2:100000:singleton)] UIContentImpl: UpdateWindowMode, window mode is 1, hasDeco is 1
06-15 15:33:43.217  9808-9808    C03900/Ace                     pid-9808                        I  [ui_content_impl.cpp(UpdateDecorVisible)-(-2:100000:singleton)] UIContentImpl: UpdateWindowMode, window visible is 0, hasDeco is 1
06-15 15:33:43.217  9808-9808    C03900/Ace                     pid-9808                        I  [ui_content_impl.cpp(Foreground)-(-2:100000:singleton)] [com.aaa.bbb][entry]: window foreground
06-15 15:33:43.217  9808-9808    C03900/Ace                     pid-9808                        I  [jsi_declarative_engine.cpp(UpdateApplicationState)-(100000:100000:scope)] Update application state , state: ON_SHOW
06-15 15:33:43.217  9808-9808    C04200/VsyncStation            pid-9808                        I  <88>MainEventRunner is available
06-15 15:33:43.217  9808-9808    C03900/Ace                     pid-9808                        I  [ui_content_impl.cpp(HideWindowTitleButton)-(-2:100000:singleton)] HideWindowTitleButton hideSplit: 0, hideMaximize: 0, hideMinimize: 0
06-15 15:33:43.218  9808-9808    C03900/Ace                     pid-9808                        I  [ui_content_impl.cpp(UpdateViewportConfig)-(-2:100000:singleton)] UIContentImpl: UpdateViewportConfig Viewport config: size: (1920, 1080) orientation: 0 density: 1.000000 position: (0, 0)
06-15 15:33:43.218  9808-9808    A00000/testTag                 pid-9808                        I  Succeeded in loading the content. Data:
06-15 15:33:43.218  9808-9808    C04200/JsWindowStage           pid-9808                        I  <331>[NAPI]Window [17, harmony0] load content end, ret = 0
06-15 15:33:43.219  9808-9808    C01120/BundleMgrService        pid-9808                        I  [bundle_manager.cpp(CheckToCache):2521] put applicationInfo to cache
06-15 15:33:43.219  9808-9808    C01120/BundleMgrService        pid-9808                        I  [bundle_manager.cpp(CheckToCache):2525] put applicationInfo to cache locked
06-15 15:33:43.219  9808-9808    A00201/BiShareOperations       pid-9808                        I  🔄 [主线程] CompleteCallback开始执行
06-15 15:33:43.219  9808-9808    A00201/BiShareOperations       pid-9808                        I  📍 [主线程] 线程ID: 547918655568
06-15 15:33:43.219  9808-9808    A00201/BiShareOperations       pid-9808                        I  📍 [主线程] napi_status: 0
06-15 15:33:43.219  9808-9808    A00201/BiShareOperations       pid-9808                        I  📍 [主线程] data地址: 0x7f90688e80
06-15 15:33:43.219  9808-9808    A00201/BiShareOperations       pid-9808                        I  📍 [主线程] env地址: 0x7f92b45560
06-15 15:33:43.219  9808-9808    A00201/BiShareOperations       pid-9808                        I  📍 [主线程] workData地址: 0x7f90688e80
06-15 15:33:43.219  9808-9808    A00201/BiShareOperations       pid-9808                        I  📍 [主线程] 操作: Initialize, 结果: 0 (No error)
06-15 15:33:43.219  9808-9808    A00201/BiShareOperations       pid-9808                        I  ✅ [主线程] 操作成功，创建成功响应
06-15 15:33:43.219  9808-9808    A00201/BiShareOperations       pid-9808                        I  📤 [主线程] 调用napi_resolve_deferred...
06-15 15:33:43.219  9808-9808    A00201/BiShareOperations       pid-9808                        I  📍 [主线程] deferred地址: 0x7f8f3729e0
06-15 15:33:43.219  9808-9808    A00201/BiShareOperations       pid-9808                        I  📍 [主线程] napiResult地址: 0x7f921f4050
06-15 15:33:43.219  9808-9808    A00201/BiShareOperations       pid-9808                        I  📍 [主线程] napi_resolve_deferred返回状态: 0
06-15 15:33:43.219  9808-9808    A00201/BiShareOperations       pid-9808                        I  ✅ [主线程] Promise已成功resolve
06-15 15:33:43.219  9808-9808    A00201/BiShareOperations       pid-9808                        I  🧹 [主线程] 开始清理资源...
06-15 15:33:43.219  9808-9808    A00201/BiShareOperations       pid-9808                        I  📍 [主线程] 删除async_work...
06-15 15:33:43.219  9808-9808    A00201/BiShareOperations       pid-9808                        I  ✅ [主线程] async_work已删除
06-15 15:33:43.219  9808-9808    A00201/BiShareOperations       pid-9808                        I  📍 [主线程] 删除workData...
06-15 15:33:43.219  9808-9808    A00201/BiShareOperations       pid-9808                        I  📍 [主线程] workData地址: 0x7f90688e80
06-15 15:33:43.219  9808-9808    A00201/BiShareOperations       pid-9808                        I  ✅ [主线程] workData已删除
06-15 15:33:43.219  9808-9808    A00201/BiShareOperations       pid-9808                        I  🏁 [主线程] CompleteCallback执行完成: Initialize
06-15 15:33:43.219  9808-9808    C03900/Ace                     pid-9808                        I  [jsi_declarative_engine.cpp(UpdateApplicationState)-(100000:100000:scope)] Update application state , state: ON_ACTIVE
06-15 15:33:43.219  9808-9808    C03924/AceAccessibility        pid-9808                        I  [ace_application_info.h(SetAccessibilityEnabled)-(100000:100000:scope)] AceApplicationInfo set accessibility enabled:0
06-15 15:33:43.219  9808-9808    C01d03/accessibility_acfwk     pid-9808                        I  [accessibility_config_impl.cpp(SubscribeConfigObserver:321)]id = [3]
06-15 15:33:43.219  9808-9808    C03924/AceAccessibility        pid-9808                        I  [js_accessibility_manager.cpp(OnConfigChanged)-(100000:100000:scope)] accessibility content timeout changed:0
06-15 15:33:43.219  9808-9808    C01d02/accessibility_asacfwk   pid-9808                        I  [accessibility_system_ability_client_impl.cpp(SubscribeStateObserver:358)]Observer has subscribed!
06-15 15:33:43.219  9808-9808    C03924/AceAccessibility        pid-9808                        I  [js_accessibility_manager.cpp(SubscribeStateObserver)-(100000:100000:scope)]  the result of SubscribeStateObserver:4001
06-15 15:33:43.219  9808-9808    C0391c/AceFocus                pid-9808                        I  [pipeline_context.cpp(WindowFocus)-(100000:100000:scope)] Window id: 17 get focus.
06-15 15:33:43.219  9808-9835    C057c2/IPCObjectProxy          pid-9808                        I  AddDeathRecipient 393: success, handle:23 desc:ohos.security.accesstoken.IAccessTokenManager
06-15 15:33:43.220  9808-9808    C03900/Ace                     pid-9808                        I  [page_router_manager.cpp(LoadPage)-(100000:100000:scope)] Page router manager is loading page[1]: pages/Index.
06-15 15:33:43.220  9808-9808    C03900/Ace                     pid-9808                        I  [jsi_declarative_engine.cpp(UpdateRootComponent)-(100000:100000:scope)] update rootComponent start
06-15 15:33:43.221  9808-9808    C03926/AceTheme                pid-9808                        W  [theme_style.h(GetAttr)-(100000:100000:scope)] style text_pattern not contains bg_color_selected!
06-15 15:33:43.221  9808-9808    C03926/AceTheme                pid-9808                        W  [theme_style.h(GetAttr)-(100000:100000:scope)] style text_pattern not contains linear_split_child_min_size!
06-15 15:33:43.221  9808-9808    C01406/OHOS::RS                pid-9808                        E  RSNode::GetChildIdByIndex, index out of bound
06-15 15:33:43.221  9808-9808    C03933/AceKeyboard             pid-9808                        I  [stage_manager.cpp(PageChangeCloseKeyboard)-(100000:100000:scope)] StageManager FrameNode notNeedSoftKeyboard.
06-15 15:33:43.221  9808-9808    C03933/AceKeyboard             pid-9808                        I  [stage_manager.cpp(PageChangeCloseKeyboard)-(100000:100000:scope)] Container not ScenceBoardWindow.
06-15 15:33:43.221  9808-9808    C03933/AceKeyboard             pid-9808                        I  [focus_hub.cpp(PushPageCloseKeyboard)-(100000:100000:scope)] PageChange CloseKeyboard FrameNode notNeedSoftKeyboard.
06-15 15:33:43.221  9808-9808    C01c10/ImsaKit                 pid-9808                        I  line: 364, function: Close,run in
06-15 15:33:43.222  9808-9808    C01c10/ImsaKit                 pid-9808                        I  line: 161, function: GetSystemAbilityProxy,get input method service proxy
06-15 15:33:43.222  9808-9808    C057c2/IPCObjectProxy          pid-9808                        I  AddDeathRecipient 393: success, handle:24 desc:ohos.miscservices.inputmethod.IInputMethodSystemAbility
06-15 15:33:43.222  9808-9808    C01c10/ImsaKit                 pid-9808                        I  line: 271, function: SendRequest,IMSAProxy, code = 7
06-15 15:33:43.222  9808-9808    C03933/AceKeyboard             pid-9808                        I  [focus_hub.cpp(PushPageCloseKeyboard)-(100000:100000:scope)] PageChange CloseKeyboard SoftKeyboard Closes Successfully.
06-15 15:33:43.222  9808-9808    C03900/Ace                     pid-9808                        I  [stage_manager.cpp(PushPage)-(100000:100000:scope)] waiting for window size
06-15 15:33:43.222  9808-9808    C03900/Ace                     pid-9808                        I  [page_router_manager.cpp(LoadPage)-(100000:100000:scope)] LoadPage Success
06-15 15:33:43.222  9808-9808    C03900/Ace                     pid-9808                        I  [container_modal_pattern.cpp(ShowTitle)-(100000:100000:scope)] ShowTitle isShow: 0, windowMode: 1, hasDeco: 1
06-15 15:33:43.223  9808-9808    C03900/Ace                     pid-9808                        I  [container_modal_pattern.cpp(SetContainerButtonHide)-(100000:100000:scope)] Set containerModal button status successfully, hideSplit: 0, hideMaximize: 0, hideMinimize: 0
06-15 15:33:43.223  9808-9808    C015b0/NetMgrSubsystem         pid-9808                        I  NETMANAGER_BASE [event_listener.cpp 50] ~EventListener() is called, to delete EventListener
06-15 15:33:43.223  9808-9808    C015b0/NetMgrSubsystem         pid-9808                        I  NETMANAGER_BASE [event_listener.cpp 50] ~EventListener() is called, to delete EventListener
06-15 15:33:43.223  9808-9808    C015b0/NetMgrSubsystem         pid-9808                        I  NETMANAGER_BASE [event_listener.cpp 50] ~EventListener() is called, to delete EventListener
06-15 15:33:43.223  9808-9808    C015b0/NetMgrSubsystem         pid-9808                        I  NETMANAGER_BASE [event_listener.cpp 50] ~EventListener() is called, to delete EventListener
06-15 15:33:43.223  9808-9835    C057d4/DBinderBaseInvoker      pid-9808                        I  DBinderDatabusInvoker 36: create
06-15 15:33:43.223  9808-9808    C01719/ffrt                    pid-9808                        E  78:TrivalOpenQosCtrlNode:62 task 9808 belong to user 20010032 open qos node failed
06-15 15:33:43.224  9808-9808    C03f01/NAPI                    pid-9808                        E  [(native_engine.cpp:559)(AddCleanupHook)] AddCleanupHook Failed.
06-15 15:33:43.224  9808-9808    C01120/BundleMgrService        pid-9808                        I  [bundle_manager.cpp(GetBundleInfoComplete):2844] query cache GetBundleInfoComplete
06-15 15:33:43.224  9808-9808    C01120/BundleMgrService        pid-9808                        I  [bundle_manager.cpp(GetBundleInfoComplete):2849] cache exsit GetBundleInfoComplete
06-15 15:33:43.224  9808-9808    A0001b/InfoService             pid-9808                        I  InfoCmdManager --> getNetCapabilities networkCap [12,16]
06-15 15:33:43.224  9808-9808    A0001b/InfoService             pid-9808                        I  InfoCmdManager --> getNetCapabilities bearerTypes [1]
06-15 15:33:43.224  9808-9835    C01719/ffrt                    pid-9808                        E  111:TrivalOpenQosCtrlNode:62 task 9808 belong to user 20010032 open qos node failed
06-15 15:33:43.224  9808-9808    C03900/Ace                     pid-9808                        W  [jsi_base_utils.cpp(JsLogPrint)-(-2:100000:singleton)] ace Log: AppStorage instance missing. Use AppStorage.createInstance(initObj). Creating instance without any initialization.
06-15 15:33:43.224  9808-9808    A0001b/InfoService             pid-9808                        I  InfoCmdManager --> notifyMqttNetChange isNetAvailable is true...
06-15 15:33:43.224  9808-9808    A0001b/InfoService             pid-9808                        I  EntryAbility --> networkListen isConnected: true
06-15 15:33:43.224  9808-9808    A0001b/InfoService             pid-9808                        W  EntryAbility --> Network connected but BiShare is not initialized yet. Skipping setNetworkInfo.
06-15 15:33:43.225  9808-9808    C03f01/NAPI                    pid-9808                        E  [(native_engine.cpp:559)(AddCleanupHook)] AddCleanupHook Failed.
06-15 15:33:43.225  9808-9808    C01120/BundleMgrService        pid-9808                        I  [bundle_manager.cpp(GetBundleInfoComplete):2844] query cache GetBundleInfoComplete
06-15 15:33:43.225  9808-9808    C01120/BundleMgrService        pid-9808                        I  [bundle_manager.cpp(GetBundleInfoComplete):2849] cache exsit GetBundleInfoComplete
06-15 15:33:43.225  9808-9808    A0001b/InfoService             pid-9808                        I  InfoCmdManager --> getConnectionProperties interfaceName: "wlan0"
06-15 15:33:43.225  9808-9808    A0001b/InfoService             pid-9808                        I  InfoCmdManager --> getConnectionProperties domains: ""
06-15 15:33:43.225  9808-9808    A0001b/InfoService             pid-9808                        I  InfoCmdManager --> getConnectionProperties linkAddresses: [{"address":{"address":"************37","family":1,"port":0},"prefixLength":24},{"address":{"address":"2408:8456:3223:4cca:9eb8:b4ff:fe62:66f4","family":2,"port":0},"prefixLength":0}]
06-15 15:33:43.225  9808-9808    A0001b/InfoService             pid-9808                        I  InfoCmdManager --> getConnectionProperties routes: [{"interface":"wlan0","destination":{"address":{"address":"0.0.0.0","family":1,"port":0},"prefixLength":0},"gateway":{"address":"************","prefixLength":0},"hasGateway":true,"isDefaultRoute":false},{"interface":"wlan0","destination":{"address":{"address":"::","family":2,"port":0},"prefixLength":0},"gateway":{"address":"fe80::76c1:4fff:fee0:22ae","prefixLength":0},"hasGateway":true,"isDefaultRoute":false}]
06-15 15:33:43.225  9808-9808    A0001b/InfoService             pid-9808                        I  InfoCmdManager --> getConnectionProperties dnses: [{"address":"************","family":1,"port":0},{"address":"","family":1,"port":0},{"address":"fe80::76c1:4fff:fee0:22ae","family":2,"port":0},{"address":"*","family":2,"port":0}]
06-15 15:33:43.225  9808-9808    A0001b/InfoService             pid-9808                        I  InfoCmdManager --> getConnectionProperties mtu: 0
06-15 15:33:43.226  9808-9854    C01719/ffrt                    pid-9808                        E  140:operator():32 [-1942750800] set priority failed ret[1] errno[0]
06-15 15:33:43.228  9808-9808    C03926/AceTheme                pid-9808                        W  [theme_style.h(GetAttr)-(100000:100000:scope)] style -1 not contains image_pattern!
06-15 15:33:43.229  9808-9808    C03900/Ace                     pid-9808                        I  [layout_wrapper.cpp(AvoidKeyboard)-(100000:100000:scope)] AvoidKeyboard KeyboardOffset: 0.000000, setOffset: 40.000000
06-15 15:33:43.230  9808-9808    C01406/OHOS::RS                pid-9808                        E  RSNode::GetChildIdByIndex, index out of bound
06-15 15:33:43.230  9808-9808    C01406/OHOS::RS                pid-9808                        E  RSNode::GetChildIdByIndex, index out of bound
06-15 15:33:43.230  9808-9808    C01406/OHOS::RS                pid-9808                        E  RSNode::GetChildIdByIndex, index out of bound
06-15 15:33:43.230  9808-9808    C01406/OHOS::RS                pid-9808                        E  RSNode::GetChildIdByIndex, index out of bound
06-15 15:33:43.230  9808-9808    C01406/OHOS::RS                pid-9808                        E  RSNode::GetChildIdByIndex, index out of bound
06-15 15:33:43.230  9808-9808    C01406/OHOS::RS                pid-9808                        E  RSNode::GetChildIdByIndex, index out of bound
06-15 15:33:43.230  9808-9808    C01406/OHOS::RS                pid-9808                        E  RSNode::GetChildIdByIndex, index out of bound
06-15 15:33:43.230  9808-9808    C0391c/AceFocus                pid-9808                        I  [focus_hub.cpp(OnFocusNode)-(100000:100000:scope)] Node(root/0) on focus
06-15 15:33:43.231  9808-9808    C0391c/AceFocus                pid-9808                        I  [focus_hub.cpp(OnFocusNode)-(100000:100000:scope)] Node(Column/4) on focus
06-15 15:33:43.231  9808-9808    C0391c/AceFocus                pid-9808                        I  [focus_hub.cpp(OnFocusNode)-(100000:100000:scope)] Node(Stack/3) on focus
06-15 15:33:43.231  9808-9808    C0391c/AceFocus                pid-9808                        I  [focus_hub.cpp(OnFocusNode)-(100000:100000:scope)] Node(page/30) on focus
06-15 15:33:43.231  9808-9808    C0391c/AceFocus                pid-9808                        I  [focus_hub.cpp(OnFocusNode)-(100000:100000:scope)] Node(Row/34) on focus
06-15 15:33:43.231  9808-9808    C0391c/AceFocus                pid-9808                        I  [pipeline_context.cpp(RequestDefaultFocus)-(100000:100000:scope)] Target view has no default focus. Request focus on view root: Row/34 return: 1.
06-15 15:33:43.231  9808-9808    C03933/AceKeyboard             pid-9808                        I  [pipeline_context.cpp(IsNotSCBWindowKeyboard)-(100000:100000:scope)] Normal WindowPage ready to close keyboard.
06-15 15:33:43.231  9808-9808    C03933/AceKeyboard             pid-9808                        I  [focus_hub.cpp(IsCloseKeyboard)-(100000:100000:scope)] FrameNode(Row/34) notNeedSoftKeyboard.
06-15 15:33:43.231  9808-9808    C03933/AceKeyboard             pid-9808                        I  [focus_hub.cpp(IsCloseKeyboard)-(100000:100000:scope)] SoftKeyboard Closes Successfully.
06-15 15:33:43.231  9808-9808    C01c10/ImsaKit                 pid-9808                        I  line: 364, function: Close,run in
06-15 15:33:43.231  9808-9808    C01c10/ImsaKit                 pid-9808                        I  line: 271, function: SendRequest,IMSAProxy, code = 7
06-15 15:33:43.231  9808-9853    C057d4/DBinderBaseInvoker      pid-9808                        I  DBinderDatabusInvoker 36: create
06-15 15:33:43.231  9808-9808    C03f01/NAPI                    pid-9808                        E  [(native_engine.cpp:559)(AddCleanupHook)] AddCleanupHook Failed.
06-15 15:33:43.231  9808-9808    C01120/BundleMgrService        pid-9808                        I  [bundle_manager.cpp(GetBundleInfoComplete):2844] query cache GetBundleInfoComplete
06-15 15:33:43.231  9808-9808    C01120/BundleMgrService        pid-9808                        I  [bundle_manager.cpp(GetBundleInfoComplete):2849] cache exsit GetBundleInfoComplete
06-15 15:33:43.232  9808-9808    C03f01/NAPI                    pid-9808                        E  [(native_engine.cpp:559)(AddCleanupHook)] AddCleanupHook Failed.
06-15 15:33:43.232  9808-9808    C01120/BundleMgrService        pid-9808                        I  [bundle_manager.cpp(GetBundleInfoComplete):2844] query cache GetBundleInfoComplete
06-15 15:33:43.232  9808-9808    C01120/BundleMgrService        pid-9808                        I  [bundle_manager.cpp(GetBundleInfoComplete):2849] cache exsit GetBundleInfoComplete
06-15 15:33:43.232  9808-9808    A0001b/InfoService             pid-9808                        I  EntryAbility --> checkPermissions havePermissionDeny
06-15 15:33:43.232  9808-9808    A0001b/InfoService             pid-9808                        I  EntryAbility --> reqPermissionsFromUser permissions 5
06-15 15:33:43.232  9808-9808    C05a01/AccessTokenAbilityAccessCtrl pid-9808                        I  [ParseRequestPermissionFromUser:1078]asyncContext.uiAbilityFlag is: 1.
06-15 15:33:43.232  9808-9808    C05a02/CommonNapi              pid-9808                        I  [ParseStringArray:139]array size is 5
06-15 15:33:43.232  9808-9808    C05a01/AccessTokenAbilityAccessCtrl pid-9808                        I  [ParseRequestPermissionFromUser:1088]asyncContext.permissionList size: 5.
06-15 15:33:43.233  9808-9829    C05a01/ATMProxy                pid-9808                        I  [GetSelfPermissionsState:257]result from server data = 1
06-15 15:33:43.233  9808-9829    C05a01/AccessTokenAbilityAccessCtrl pid-9808                        I  [RequestPermissionsFromUserExecute:1542]pop service extension dialog
06-15 15:33:43.233  9808-9829    C01304/AbilityManagerService   pid-9808                        I  [ability_manager_client.cpp(RequestDialogService:974)]request is:/com.ohos.permissionmanager//com.ohos.permissionmanager.GrantAbility.
06-15 15:33:43.233  9808-9829    C01300/Ability                 pid-9808                        I  [ability_manager_proxy.cpp(RequestDialogService:3702)]RequestDialogService Call
06-15 15:33:43.236  9808-9853    C01719/ffrt                    pid-9808                        I  200:operator():86 submit task delay time [10000 us] has ended.
06-15 15:33:43.236  9808-9853    C01719/ffrt                    pid-9808                        I  204:DecDepRef:47 Undependency completed, enter ready queue, task[4], name[t3]
06-15 15:33:43.240  9808-9829    C05a01/AccessTokenAbilityAccessCtrl pid-9808                        I  [StartServiceExtension:1267]RequestDialogService end. ret=0
06-15 15:33:43.251  9808-9852    C01719/ffrt                    pid-9808                        I  251:operator():86 submit task delay time [10000 us] has ended.
06-15 15:33:43.251  9808-9852    C01719/ffrt                    pid-9808                        I  255:DecDepRef:47 Undependency completed, enter ready queue, task[6], name[t5]
06-15 15:33:43.401  9808-9812    C03900/Ace                     com.aaa.bbb         I  [ui_content_impl.cpp(UnFocus)-(-1:100000:singleton)] com.aaa.bbb window unfocus
06-15 15:33:43.401  9808-9808    C01304/AbilityManagerService   com.aaa.bbb         E  [ui_ability_impl.cpp(operator():387)]applicationContext or lifecycleCallback is nullptr.
06-15 15:33:43.401  9808-9808    C03900/Ace                     com.aaa.bbb         I  [jsi_declarative_engine.cpp(UpdateApplicationState)-(100000:100000:scope)] Update application state , state: ON_INACTIVE
06-15 15:33:43.401  9808-9808    C0391c/AceFocus                com.aaa.bbb         I  [pipeline_context.cpp(WindowFocus)-(100000:100000:scope)] Window id: 17 lost focus.
06-15 15:33:43.402  9808-9808    C0391c/AceFocus                com.aaa.bbb         I  [focus_hub.cpp(OnBlurNode)-(100000:100000:scope)] Node(root/0) on blur by 1
06-15 15:33:43.402  9808-9808    C0391c/AceFocus                com.aaa.bbb         I  [focus_hub.cpp(OnBlurNode)-(100000:100000:scope)] Node(Column/4) on blur by 1
06-15 15:33:43.402  9808-9808    C0391c/AceFocus                com.aaa.bbb         I  [focus_hub.cpp(OnBlurNode)-(100000:100000:scope)] Node(Stack/3) on blur by 1
06-15 15:33:43.402  9808-9808    C0391c/AceFocus                com.aaa.bbb         I  [focus_hub.cpp(OnBlurNode)-(100000:100000:scope)] Node(page/30) on blur by 1
06-15 15:33:43.402  9808-9808    C0391c/AceFocus                com.aaa.bbb         I  [focus_hub.cpp(OnBlurNode)-(100000:100000:scope)] Node(Row/34) on blur by 1
06-15 15:33:43.421  9808-9826    C01719/ffrt                    com.aaa.bbb         I  303:operator():86 submit task delay time [10000 us] has ended.
06-15 15:33:43.421  9808-9826    C01719/ffrt                    com.aaa.bbb         I  307:DecDepRef:47 Undependency completed, enter ready queue, task[8], name[t7]
06-15 15:33:43.435  9808-9853    C01719/ffrt                    com.aaa.bbb         I  355:operator():86 submit task delay time [10000 us] has ended.
06-15 15:33:43.435  9808-9853    C01719/ffrt                    com.aaa.bbb         I  359:DecDepRef:47 Undependency completed, enter ready queue, task[10], name[t9]
06-15 15:33:44.581  9808-9817    C03f00/ArkCompiler             com.aaa.bbb         I  [gc] SmartGC: finish app cold start
06-15 15:33:48.157  9808-9837    A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 8, msg: [bishare][file:DeviceStateMachine.cpp - DeviceState()] - State name: DeviceInitState
06-15 15:33:48.157  9808-9837    A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 8, msg: [bishare][file:DeviceStateMachine.cpp - DeviceState()] - State name: DeviceConnectState
06-15 15:33:48.157  9808-9837    A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 8, msg: [bishare][file:DeviceStateMachine.cpp - DeviceState()] - State name: DeviceSenderState
06-15 15:33:48.157  9808-9837    A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 8, msg: [bishare][file:DeviceStateMachine.cpp - DeviceState()] - State name: DeviceReceiverState
06-15 15:33:48.157  9808-9837    A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 4, msg: [bishare][file:DeviceStateMachine.cpp - DeviceReceiverState()] - DeviceReceiverState created
06-15 15:33:48.157  9808-9837    A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 7, msg: [bishare][file:DeviceStateMachine.cpp - setDeviceStateMachine()] - Enter state: DeviceInitState
06-15 15:33:48.158  9808-9837    A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 8, msg: [bishare][file:RtpStreamProxy.cpp - init()] - RtpStreamProxy init.
06-15 15:33:48.158  9808-9837    A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 8, msg: [bishare][file:CommManager.cpp - CommManager()] - create
06-15 15:33:48.158  9808-9837    A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 8, msg: [bishare][file:MonitorsManager.cpp - enumMonitors()] - MonitorsManager::enumMonitors
06-15 15:33:48.158  9808-9837    A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 8, msg: [bishare][file:DeviceManager.cpp - start()] - EncodecVideo h264
06-15 15:33:48.158  9808-9837    A02b66/TAG_WANGR               com.aaa.bbb         D  {sendNotify():55} level: 8, msg: [bishare][file:DeviceManager.cpp - start()] - finished.
06-15 15:33:48.158  9808-9837    A00201/BiShareCallbacks        com.aaa.bbb         I  📞 OnEventCallback被调用 - type: 2, len: 79
06-15 15:33:48.158  9808-9837    A00201/BiShareCallbacks        com.aaa.bbb         I  📍 [事件回调] 调用线程ID: 547861559728
06-15 15:33:48.158  9808-9837    A00201/BiShareCallbacks        com.aaa.bbb         I  📍 [事件回调] 静态实例状态: 存在 (地址: 0x7f92037678)
06-15 15:33:48.158  9808-9837    A00201/BiShareCallbacks        com.aaa.bbb         I  📍 [事件回调] 参数验证通过
06-15 15:33:48.158  9808-9837    A00201/BiShareCallbacks        com.aaa.bbb         I  📍 [事件回调] value内容:
06-15 15:33:48.158  9808-9837    A00201/BiShareCallbacks        com.aaa.bbb         I  📍 [事件回调] 静态实例引用计数: 2
06-15 15:33:48.158  9808-9837    A00201/BiShareCallbacks        com.aaa.bbb         I  🔄 [事件回调] 调用EnqueueEventCallback...
06-15 15:33:48.158  9808-9837    A00201/BiShareCallbacks        com.aaa.bbb         I  事件回调已入队，类型: 2
06-15 15:33:48.158  9808-9837    A00201/BiShareCallbacks        com.aaa.bbb         I  ✅ [事件回调] EnqueueEventCallback调用成功
06-15 15:33:48.240  9808-9829    C01719/ffrt                    com.aaa.bbb         I  378:Detach:114 qos 3 thread not joinable.
06-15 15:33:48.240  9808-9829    C057c4/IPCThreadSkeleton       com.aaa.bbb         I  ~IPCThreadSkeleton 86: 9808_9829
06-15 15:33:48.421  9808-9826    C01719/ffrt                    com.aaa.bbb         I  382:Detach:114 qos 2 thread not joinable.
06-15 15:33:48.421  9808-9826    C057c4/IPCThreadSkeleton       com.aaa.bbb         I  ~IPCThreadSkeleton 86: 9808_9826
06-15 15:33:48.421  9808-9826    C057d4/DBinderBaseInvoker      com.aaa.bbb         I  ~DBinderDatabusInvoker 41: destroy
06-15 15:33:48.421  9808-9835    C01719/ffrt                    com.aaa.bbb         I  384:Detach:114 qos 2 thread not joinable.
06-15 15:33:48.421  9808-9835    C057c4/IPCThreadSkeleton       com.aaa.bbb         I  ~IPCThreadSkeleton 86: 9808_9835
06-15 15:33:48.421  9808-9835    C057d4/DBinderBaseInvoker      com.aaa.bbb         I  ~DBinderDatabusInvoker 41: destroy
06-15 15:33:48.425  9808-9852    C01719/ffrt                    com.aaa.bbb         I  387:Detach:114 qos 2 thread not joinable.
06-15 15:33:48.425  9808-9852    C057c4/IPCThreadSkeleton       com.aaa.bbb         I  ~IPCThreadSkeleton 86: 9808_9852
06-15 15:33:48.435  9808-9853    C01719/ffrt                    com.aaa.bbb         I  390:Detach:114 qos 2 thread not joinable.
06-15 15:33:48.435  9808-9853    C057c4/IPCThreadSkeleton       com.aaa.bbb         I  ~IPCThreadSkeleton 86: 9808_9853
06-15 15:33:48.435  9808-9853    C057d4/DBinderBaseInvoker      com.aaa.bbb         I  ~DBinderDatabusInvoker 41: destroy
