# RecordingManager C++类导出架构文档

## 📋 概述

本文档详细说明了RecordingManager的C++类导出架构，包括设计原则、实现细节、异步执行优化和集成指南。

## 🏗️ 架构设计原则

### 1. 一致性原则
- **与现有Manager类保持一致**: 采用与DeviceManager、BiShareManager相同的设计模式
- **单例模式**: 支持`getInstance()`静态方法获取实例
- **Promise异步**: 所有方法返回Promise，支持现代JavaScript异步编程

### 2. 性能优化原则
- **异步执行**: 使用AsyncExecutor框架，避免阻塞主线程
- **后台线程**: 耗时操作在后台线程执行，主线程只处理结果
- **资源管理**: 自动管理异步数据的生命周期

### 3. 可扩展性原则
- **模块化设计**: 每个功能独立实现，易于添加新方法
- **标准化接口**: 使用统一的参数解析和错误处理机制
- **类型安全**: 完整的TypeScript类型定义

## 🔧 核心组件

### 1. RecordingManagerNapi类
```cpp
class RecordingManagerNapi {
public:
    // 类初始化和导出
    static napi_value Init(napi_env env, napi_value exports);
    static napi_value GetInstance(napi_env env, napi_callback_info info);
    
    // 录制功能方法
    static napi_value StartScreenRecord(napi_env env, napi_callback_info info);
    static napi_value StopScreenRecord(napi_env env, napi_callback_info info);
    static napi_value Screenshot(napi_env env, napi_callback_info info);
    
private:
    // 单例管理
    static napi_ref constructor_;
    static std::shared_ptr<BiShareRecordManager> nativeManager_;
    
    // 实例管理
    std::shared_ptr<BiShareRecordManager> recordManager_;
};
```

### 2. 异步数据结构
```cpp
struct RecordingAsyncData {
    std::shared_ptr<BiShareRecordManager> recordManager;
    int32_t session, displayId, direction;
    std::string filePath;
    int32_t top, bottom, left, right;
    bstatus_t result;
};
```

### 3. AsyncExecutor集成
```cpp
// 异步执行模式
return AsyncExecutor::ExecuteAsync(env, "OperationName",
    // 后台线程执行
    [](AsyncWorkData* workData) {
        auto data = static_cast<RecordingAsyncData*>(workData->userData);
        data->result = data->recordManager->SomeOperation(...);
    },
    // 主线程完成
    [](napi_env env, AsyncWorkData* workData) -> napi_value {
        auto data = static_cast<RecordingAsyncData*>(workData->userData);
        napi_value result;
        napi_get_boolean(env, data->result == BS_OK, &result);
        delete data;
        return result;
    },
    asyncData);
```

## 🚀 性能优化特性

### 1. 异步执行架构
- **非阻塞主线程**: 所有耗时操作在后台线程执行
- **Promise支持**: 原生Promise支持，无需手动管理回调
- **错误处理**: 完整的异常捕获和Promise reject机制

### 2. 资源管理优化
- **自动清理**: 异步数据在完成后自动释放
- **智能指针**: 使用shared_ptr管理原生管理器生命周期
- **内存安全**: 避免内存泄漏和悬空指针

### 3. 线程安全
- **线程隔离**: 参数解析在主线程，业务逻辑在后台线程
- **数据拷贝**: 避免跨线程共享可变数据
- **同步机制**: 使用NAPI异步工作队列确保线程安全

## 📁 文件结构

```
bishare/src/main/cpp/
├── interfaces/napi/
│   ├── recording_manager_napi.h      # RecordingManager NAPI头文件
│   ├── recording_manager_napi.cpp    # RecordingManager NAPI实现
│   └── bishare_napi_interface.cpp    # 主NAPI接口（集成点）
├── types/libbishare_napi/
│   └── index.d.ts                    # TypeScript类型定义
└── include/
    └── async_executor.h              # 异步执行器框架
```

## 🔌 集成指南

### 1. 编译配置
在CMakeLists.txt中添加：
```cmake
set(NAPI_SOURCES
    src/main/cpp/interfaces/napi/bishare_napi_interface.cpp
    src/main/cpp/interfaces/napi/recording_manager_napi.cpp
)

include_directories(
    src/main/cpp/interfaces/napi
    src/main/cpp/include
)
```

### 2. 模块导出
在bishare_napi_interface.cpp中：
```cpp
#include "recording_manager_napi.h"

napi_value BiShareNapiInterface::Init(napi_env env, napi_value exports) {
    // ... 其他初始化代码
    
    // 初始化并导出RecordingManager类
    RecordingManagerNapi::Init(env, exports);
    
    return exports;
}
```

### 3. JavaScript使用
```typescript
import { RecordingManager } from '@ohos/libbishare_napi';

// 获取单例实例
const manager = RecordingManager.getInstance();

// 异步调用
const result = await manager.startScreenRecord({
    session: 1,
    displayId: 0,
    direction: Direction.SEND
});
```

## 🧪 测试验证

### 1. 编译验证
```bash
cd /Users/<USER>/DevEcoStudioProjects/ScreenDemo
./hvigorw --mode module -p module=bishare@default compileNative
./hvigorw assembleApp
```

### 2. 功能测试
```typescript
// 基本功能测试
const manager = RecordingManager.getInstance();
console.log('✅ getInstance() 成功');

// 异步方法测试
try {
    const result = await manager.startScreenRecord(options);
    console.log('✅ 异步执行成功:', result);
} catch (error) {
    console.error('❌ 异步执行失败:', error);
}
```

## 📈 性能对比

| 特性 | 同步版本 | 异步版本 |
|------|----------|----------|
| **主线程阻塞** | ❌ 会阻塞 | ✅ 不阻塞 |
| **用户体验** | ❌ 界面卡顿 | ✅ 流畅响应 |
| **错误处理** | ⚠️ 基础 | ✅ 完整 |
| **资源利用** | ❌ 单线程 | ✅ 多线程 |
| **扩展性** | ⚠️ 有限 | ✅ 优秀 |

## 🔮 未来扩展

### 1. 功能扩展
- 添加录制状态监听
- 支持录制参数配置
- 实现录制进度回调

### 2. 性能优化
- 实现录制缓存机制
- 添加录制质量控制
- 支持多路录制

### 3. 架构优化
- 统一Manager类架构
- 实现配置管理系统
- 添加插件化支持

---

**文档版本**: 1.0  
**最后更新**: 2025-06-15  
**维护人员**: Augment Agent
