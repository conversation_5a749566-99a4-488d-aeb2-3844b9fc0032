#include "bishare_napi_interface.h"
#include "bishare_facade.h"
#include "callback_manager.h"
#include "async_executor.h"
#include "bishare_status_codes.h"
#include "bishare_logger.h"
#include "bishare_operations.h"
#include "bishare-service.h"
#include "bishare_napi.h"
#include "bishare_callbacks.h"
#include "bishare_operation_impls.h"
#include "recording_manager_napi.h"
#include "ets_device_manager_napi.h"
#include "bishare_manager_napi.h"

using namespace OHOS::BiShare::Infrastructure;

using namespace OHOS::BiShare::Core;

namespace OHOS {
    namespace BiShare {
        namespace Interfaces {

            // 定义日志标签
            static constexpr const char *NAPI_TAG = "BiShareNapiInterface";

            // 辅助函数类型定义
            using ExecuteCallback = std::function<void(napi_env, AsyncWorkData *)>;
            using CompleteCallback = std::function<void(napi_env, napi_status, AsyncWorkData *)>;
            using ParseCallback = std::function<bool(napi_env, napi_callback_info, AsyncWorkData **)>;

            // 通用的智能路由函数 - 替代假实现的宏
            template<typename OperationType>
            napi_value SmartExecuteOperation(napi_env env, napi_callback_info info, const char* operationName) {
                BiShareLogger::Info("BiShareNapiInterface", "🎯 收到%s调用，开始智能路由检测...", operationName);

                // 动态获取参数数量
                size_t argc = 0;
                napi_get_cb_info(env, info, &argc, nullptr, nullptr, nullptr);

                BiShareLogger::Info("BiShareNapiInterface", "📋 %s参数数量: %zu", operationName, argc);

                // 根据实际参数数量分配数组（设置合理的最大值以防止栈溢出）
                const size_t MAX_ARGS = 20; // 合理的最大参数数量
                if (argc > MAX_ARGS) {
                    BiShareLogger::Error("BiShareNapiInterface", "❌ 参数数量过多: %zu (最大支持: %zu)", argc, MAX_ARGS);
                    napi_value error, message;
                    napi_create_string_utf8(env, "参数数量过多", NAPI_AUTO_LENGTH, &message);
                    napi_create_error(env, nullptr, message, &error);
                    return error;
                }

                // 使用动态数组或栈数组
                napi_value argv[MAX_ARGS];
                if (argc > 0) {
                    napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
                }

                bool hasCallback = false;
                if (argc > 0) {
                    napi_valuetype valueType;
                    napi_status status = napi_typeof(env, argv[argc - 1], &valueType);
                    hasCallback = (status == napi_ok && valueType == napi_function);
                    BiShareLogger::Info("BiShareNapiInterface", "🔍 检测最后一个参数类型: %s",
                        hasCallback ? "function (回调函数)" : "非function");
                } else {
                    BiShareLogger::Info("BiShareNapiInterface", "🔍 无参数，判定为同步模式");
                }

                auto operation = std::make_unique<OperationType>();

                if (hasCallback) {
                    // 异步模式 - 使用Operation的异步Execute方法
                    BiShareLogger::Info("BiShareNapiInterface", "🔄 [路由] 检测到回调函数，选择异步模式执行%s", operationName);
                    return operation->Execute(env, info);
                } else {
                    // 同步模式 - 使用Operation的同步ExecuteSync方法
                    BiShareLogger::Info("BiShareNapiInterface", "⚡ [路由] 未检测到回调函数，选择同步模式执行%s", operationName);
                    return operation->ExecuteSync(env, info);
                }
            }

            // 标准完成回调
            void StandardCompleteCallback(napi_env env, napi_status status, AsyncWorkData *workData) {
                if (workData->callbackRef != nullptr) {
                    napi_value callback, result, global;
                    napi_get_reference_value(env, workData->callbackRef, &callback);
                    napi_get_global(env, &global);

                    if (workData->result == BS_OK) {
                        napi_value args[2];
                        napi_get_null(env, &args[0]); // error = null
                        napi_create_int32(env, static_cast<int32_t>(workData->result), &args[1]); // result
                        napi_call_function(env, global, callback, 2, args, &result);
                    } else {
                        napi_value args[2];
                        napi_create_string_utf8(env, workData->errorMessage.c_str(), NAPI_AUTO_LENGTH, &args[0]); // error
                        napi_get_null(env, &args[1]); // result = null
                        napi_call_function(env, global, callback, 2, args, &result);
                    }
                }
                delete workData;
            }

            // 创建异步工作的通用函数
            napi_value CreateAsyncWork(napi_env env, napi_callback_info info, const char *resourceName,
                                       ExecuteCallback executeCallback, CompleteCallback completeCallback,
                                       ParseCallback parseCallback) {
                AsyncWorkData *workData = nullptr;

                // 解析参数
                if (!parseCallback(env, info, &workData)) {
                    return nullptr;
                }

                // 创建异步工作
                napi_value resourceNameValue;
                napi_create_string_utf8(env, resourceName, NAPI_AUTO_LENGTH, &resourceNameValue);

                napi_create_async_work(
                    env, nullptr, resourceNameValue,
                    // 执行函数
                    [](napi_env env, void *data) {
                        AsyncWorkData *workData = static_cast<AsyncWorkData *>(data);
                        // 这里会调用传入的executeCallback
                        // 为了简化，我们直接在具体方法中实现执行逻辑
                    },
                    // 完成函数
                    [](napi_env env, napi_status status, void *data) {
                        AsyncWorkData *workData = static_cast<AsyncWorkData *>(data);
                        StandardCompleteCallback(env, status, workData);
                    },
                    workData, &workData->work);

                // 队列异步工作
                napi_queue_async_work(env, workData->work);

                // 返回 undefined
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            }

            napi_value BiShareNapiInterface::Init(napi_env env, napi_value exports) {
                // 初始化门面
                auto& facade = GetFacade();
                if (!facade.Initialize()) {
                    napi_throw_error(env, "INIT_FAILED", "Failed to initialize BiShare facade");
                    return nullptr;
                }

                // 定义常量
                // 注意：这些常量定义必须与 types/libbishare_napi/index.d.ts 中的声明保持同步
                napi_property_descriptor constants[] = {
                    // 版本信息
                    {"VERSION", nullptr, nullptr, nullptr, nullptr,
                     CreateStringValue(env, "1.0.0"), napi_default, nullptr},

                    // 日志优先级常量
                    {"LOG_PRIORITY_DEBUG", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, LOG_DEBUG), napi_default, nullptr},
                    {"LOG_PRIORITY_INFO", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, LOG_INFO), napi_default, nullptr},
                    {"LOG_PRIORITY_WARN", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, LOG_WARN), napi_default, nullptr},
                    {"LOG_PRIORITY_ERROR", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, LOG_ERROR), napi_default, nullptr},

                    // 事件类型常量
                    {"EVENT_DEVICE_INFO", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, EVENT_DEVICE_INFO), napi_default, nullptr},
                    {"EVENT_DEVICE_STATUS", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, EVENT_DEVICE_STATUS), napi_default, nullptr},
                    {"EVENT_CONNECT_STATUS", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, EVENT_CONNECT_STATUS), napi_default, nullptr},

                    // 错误码常量（与 types/libbishare_napi/index.d.ts 保持同步）
                    {"BS_OK", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, BS_OK), napi_default, nullptr},
                    {"BS_ERROR", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, BS_ERROR), napi_default, nullptr},
                    {"BS_NOT_INIT", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, BS_NOT_INIT), napi_default, nullptr},
                    {"BS_INVALID_PARAM", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, BS_INVALID_PARAM), napi_default, nullptr},
                    {"BS_NOT_SUPPORTED", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, BS_NOT_SUPPORTED), napi_default, nullptr},
                    {"BS_PERMISSION_DENIED", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, BS_PERMISSION_DENIED), napi_default, nullptr},
                    {"BS_DEVICE_NOT_FOUND", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, BS_DEVICE_NOT_FOUND), napi_default, nullptr},
                    {"BS_CONNECTION_FAILED", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, BS_CONNECTION_FAILED), napi_default, nullptr},
                    {"BS_TIMEOUT", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, BS_TIMEOUT), napi_default, nullptr},
                    {"BS_INSUFFICIENT_MEMORY", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, BS_INSUFFICIENT_MEMORY), napi_default, nullptr},
                };

                // 定义方法 - 使用与bishare_napi.h一致的命名
                // 注意：这些方法定义必须与 types/libbishare_napi/index.d.ts 中的函数声明保持同步
                napi_property_descriptor methods[] = {
                    // API方法 - 与bishare_napi.h保持一致
                    {"initialize", nullptr, Initialize, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"release", nullptr, Release, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"discoverDevices", nullptr, DiscoverDevices, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"clearDiscoveredDevices", nullptr, ClearDiscoveredDevices, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"getDiscoveredDevices", nullptr, GetDiscoveredDevices, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"startScreenRecord", nullptr, StartScreenRecord, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"stopScreenRecord", nullptr, StopScreenRecord, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"startCapture", nullptr, StartCapture, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"setDeviceInfo", nullptr, SetDeviceInfo, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"setDeviceModel", nullptr, SetDeviceModel, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"getDeviceModel", nullptr, GetDeviceModel, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"resetDeviceModel", nullptr, ResetDeviceModel, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"getRootPath", nullptr, GetRootPath, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"getCurrentDirector", nullptr, GetCurrentDirector, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"setSize", nullptr, SetSize, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"setDefaultAudioOutputDevice", nullptr, SetDefaultAudioOutputDevice, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"screenshot", nullptr, Screenshot, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"findRemoteDevice", nullptr, FindRemoteDevice, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"setNetworkInfo", nullptr, SetNetworkInfo, nullptr, nullptr, nullptr, napi_default, nullptr},

                    // 事件管理方法
                    {"on", nullptr, On, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"off", nullptr, Off, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"once", nullptr, Once, nullptr, nullptr, nullptr, napi_default, nullptr},
                };

                // 定义常量
                napi_define_properties(env, exports, sizeof(constants) / sizeof(constants[0]), constants);

                // 定义方法
                napi_define_properties(env, exports, sizeof(methods) / sizeof(methods[0]), methods);

                // 初始化并导出Manager类
                RecordingManagerNapi::Init(env, exports);
                EtsDeviceManagerNapi::Init(env, exports);
                BiShareManagerNapi::Init(env, exports);

                return exports;
            }

            // API方法实现 - 与bishare_napi.h保持一致的命名
            napi_value BiShareNapiInterface::Initialize(napi_env env, napi_callback_info info) {
                // 统一使用SmartExecuteOperation模板函数 - 与其他方法保持一致
                return SmartExecuteOperation<InitializeOperation>(env, info, "Initialize");
            }

            napi_value BiShareNapiInterface::Release(napi_env env, napi_callback_info info) {
                // 统一使用SmartExecuteOperation模板函数 - 与其他方法保持一致
                return SmartExecuteOperation<ReleaseOperation>(env, info, "Release");
            }

            napi_value BiShareNapiInterface::DiscoverDevices(napi_env env, napi_callback_info info) {
                // 统一使用SmartExecuteOperation模板函数 - 与其他方法保持一致
                return SmartExecuteOperation<DiscoverDevicesOperation>(env, info, "DiscoverDevices");
            }

            napi_value BiShareNapiInterface::ClearDiscoveredDevices(napi_env env, napi_callback_info info) {
                // 真实的同步/异步双模式支持 - 替代假实现的宏
                return SmartExecuteOperation<ClearDiscoveredDevicesOperation>(env, info, "ClearDiscoveredDevices");
            }

            napi_value BiShareNapiInterface::GetDiscoveredDevices(napi_env env, napi_callback_info info) {
                // 统一使用SmartExecuteOperation模板函数 - 与其他方法保持一致
                return SmartExecuteOperation<GetDiscoveredDevicesOperation>(env, info, "GetDiscoveredDevices");
            }

            napi_value BiShareNapiInterface::SetDeviceInfo(napi_env env, napi_callback_info info) {
                // 真实的同步/异步双模式支持 - 配置操作通常较快，适合智能检测
                return SmartExecuteOperation<SetDeviceInfoOperation>(env, info, "SetDeviceInfo");
            }

            napi_value BiShareNapiInterface::SetDeviceModel(napi_env env, napi_callback_info info) {
                // 真实的同步/异步双模式支持
                return SmartExecuteOperation<SetDeviceModelOperation>(env, info, "SetDeviceModel");
            }

            napi_value BiShareNapiInterface::GetDeviceModel(napi_env env, napi_callback_info info) {
                // 真实的同步/异步双模式支持 - 查询操作通常较快
                return SmartExecuteOperation<GetDeviceModelOperation>(env, info, "GetDeviceModel");
            }

            napi_value BiShareNapiInterface::ResetDeviceModel(napi_env env, napi_callback_info info) {
                // 真实的同步/异步双模式支持
                return SmartExecuteOperation<ResetDeviceModelOperation>(env, info, "ResetDeviceModel");
            }

            napi_value BiShareNapiInterface::FindRemoteDevice(napi_env env, napi_callback_info info) {
                // 真实的同步/异步双模式支持 - 网络操作可能耗时，建议异步
                return SmartExecuteOperation<FindRemoteDeviceOperation>(env, info, "FindRemoteDevice");
            }

            // 录制管理方法实现
            napi_value BiShareNapiInterface::StartScreenRecord(napi_env env, napi_callback_info info) {
                // 真实的同步/异步双模式支持 - 录制操作通常需要异步处理
                return SmartExecuteOperation<StartScreenRecordOperation>(env, info, "StartScreenRecord");
            }

            napi_value BiShareNapiInterface::StopScreenRecord(napi_env env, napi_callback_info info) {
                // 真实的同步/异步双模式支持 - 录制停止操作也可能耗时
                return SmartExecuteOperation<StopScreenRecordOperation>(env, info, "StopScreenRecord");
            }

            napi_value BiShareNapiInterface::StartCapture(napi_env env, napi_callback_info info) {
                // 真实的同步/异步双模式支持 - 捕获操作可能耗时
                return SmartExecuteOperation<StartCaptureOperation>(env, info, "StartCapture");
            }

            napi_value BiShareNapiInterface::SetSize(napi_env env, napi_callback_info info) {
                // 真实的同步/异步双模式支持 - 设置尺寸是配置操作，通常较快
                return SmartExecuteOperation<SetSizeOperation>(env, info, "SetSize");
            }

            napi_value BiShareNapiInterface::SetDefaultAudioOutputDevice(napi_env env, napi_callback_info info) {
                // 真实的同步/异步双模式支持 - 音频设备设置是配置操作
                return SmartExecuteOperation<SetDefaultAudioOutputDeviceOperation>(env, info, "SetDefaultAudioOutputDevice");
            }

            napi_value BiShareNapiInterface::Screenshot(napi_env env, napi_callback_info info) {
                // 真实的同步/异步双模式支持 - 截图操作涉及I/O，可能耗时
                return SmartExecuteOperation<ScreenshotOperation>(env, info, "Screenshot");
            }

            // 网络管理方法实现
            napi_value BiShareNapiInterface::SetNetworkInfo(napi_env env, napi_callback_info info) {
                // 真实的同步/异步双模式支持 - 网络配置操作
                return SmartExecuteOperation<SetNetworkInfoOperation>(env, info, "SetNetworkInfo");
            }

            napi_value BiShareNapiInterface::GetRootPath(napi_env env, napi_callback_info info) {
                // 真实的同步/异步双模式支持 - 路径查询操作，通常较快
                return SmartExecuteOperation<GetRootPathOperation>(env, info, "GetRootPath");
            }

            napi_value BiShareNapiInterface::GetCurrentDirector(napi_env env, napi_callback_info info) {
                // 真实的同步/异步双模式支持 - 目录查询操作，通常较快
                return SmartExecuteOperation<GetCurrentDirectoryOperation>(env, info, "GetCurrentDirector");
            }

            // 事件管理方法实现
            napi_value BiShareNapiInterface::On(napi_env env, napi_callback_info info) {
                // 真实的同步/异步双模式支持 - 事件注册操作，通常较快
                return SmartExecuteOperation<OnEventOperation>(env, info, "On");
            }

            napi_value BiShareNapiInterface::Off(napi_env env, napi_callback_info info) {
                // 真实的同步/异步双模式支持 - 事件注销操作，通常较快
                return SmartExecuteOperation<OffEventOperation>(env, info, "Off");
            }

            napi_value BiShareNapiInterface::Once(napi_env env, napi_callback_info info) {
                // 真实的同步/异步双模式支持 - 一次性事件注册，通常较快
                return SmartExecuteOperation<OnceEventOperation>(env, info, "Once");
            }

            // 私有辅助方法
            OHOS::BiShare::Core::BiShareFacade& BiShareNapiInterface::GetFacade() {
                return OHOS::BiShare::Core::BiShareFacade::GetInstance();
            }

            napi_value BiShareNapiInterface::CreateError(napi_env env, const std::string& message) {
                napi_value error, errorMessage;
                napi_create_string_utf8(env, message.c_str(), NAPI_AUTO_LENGTH, &errorMessage);
                napi_create_error(env, nullptr, errorMessage, &error);
                return error;
            }

            napi_value BiShareNapiInterface::CreateSuccessResult(napi_env env, napi_value data) {
                napi_value result;
                if (data) {
                    result = data;
                } else {
                    napi_get_undefined(env, &result);
                }
                return result;
            }

            napi_value BiShareNapiInterface::CreateStringValue(napi_env env, const std::string& str) {
                napi_value result;
                napi_create_string_utf8(env, str.c_str(), NAPI_AUTO_LENGTH, &result);
                return result;
            }

            napi_value BiShareNapiInterface::CreateInt32Value(napi_env env, int32_t value) {
                napi_value result;
                napi_create_int32(env, value, &result);
                return result;
            }

        } // namespace Interfaces
    } // namespace BiShare
} // namespace OHOS
