#ifndef RECORDING_DIRECT_EXECUTOR_H
#define RECORDING_DIRECT_EXECUTOR_H

#include "bishare_operations.h"

namespace OHOS {
    namespace BiShare {

        /**
         * 录制功能直接执行器
         * 负责视频录制、音频录制、屏幕截图等功能
         */
        class RecordingDirectExecutor {
        public:
            // 注册所有录制相关的直接执行函数
            static void RegisterAll();

            // 具体的直接执行函数
            static void ExecuteStartScreenRecord(napi_env env, AsyncWorkData* workData);
            static void ExecuteStopScreenRecord(napi_env env, AsyncWorkData* workData);
            static void ExecuteStartCapture(napi_env env, AsyncWorkData* workData);
            static void ExecuteScreenshot(napi_env env, AsyncWorkData* workData);
            static void ExecuteSetSize(napi_env env, AsyncWorkData* workData);
            static void ExecuteSetDefaultAudioOutputDevice(napi_env env, AsyncWorkData* workData);

            // 未来可扩展的视频录制功能
            static void ExecuteStartVideoRecord(napi_env env, AsyncWorkData* workData);
            static void ExecuteStopVideoRecord(napi_env env, AsyncWorkData* workData);

        private:
            static const char* TAG;
        };

        // 全局注册函数（供bishare_operations.cpp调用）
        void RegisterRecordingDirectExecutors();

    } // namespace BiShare
} // namespace OHOS

#endif // RECORDING_DIRECT_EXECUTOR_H
