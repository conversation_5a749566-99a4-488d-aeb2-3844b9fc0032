#ifndef EVENT_DIRECT_EXECUTOR_H
#define EVENT_DIRECT_EXECUTOR_H

#include "bishare_operations.h"

namespace OHOS {
    namespace BiShare {

        /**
         * 事件管理直接执行器
         * 负责事件监听、取消监听等功能
         */
        class EventDirectExecutor {
        public:
            // 注册所有事件管理相关的直接执行函数
            static void RegisterAll();

            // 具体的直接执行函数
            static void ExecuteOn(napi_env env, AsyncWorkData* workData);
            static void ExecuteOff(napi_env env, AsyncWorkData* workData);
            static void ExecuteOnce(napi_env env, AsyncWorkData* workData);

        private:
            static const char* TAG;
        };

        // 全局注册函数（供bishare_operations.cpp调用）
        void RegisterEventDirectExecutors();

    } // namespace BiShare
} // namespace OHOS

#endif // EVENT_DIRECT_EXECUTOR_H
