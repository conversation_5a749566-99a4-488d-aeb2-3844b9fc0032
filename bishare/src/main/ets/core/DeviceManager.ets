import createLogger from '../utils/Logger';
import { DeviceInfo, DeviceInfoOptions, Callback, NetworkInfoOptions } from '../interfaces/BiShareTypes';
import {
  clearDiscoveredDevices,
  discoverDevices, findRemoteDevice,
  getCurrentDirector,
  getDeviceModel,
  getDiscoveredDevices,
  getRootPath,
  resetDeviceModel,
  setDeviceInfo,
  setDeviceModel,
  setNetworkInfo
} from 'libbishare_napi.so';

const logger = createLogger('BiShare.DeviceManager');

/**
 * Device Manager class - handles device operations
 */
export default class DeviceManager {
  private static instance: DeviceManager;
  private deviceList: Map<string, DeviceInfo>;

  /**
   * Private constructor to implement Singleton pattern
   */
  private constructor() {
    this.deviceList = new Map<string, DeviceInfo>();
    logger.info('DeviceManager created');
  }

  /**
   * Get DeviceManager instance (Singleton)
   * @returns DeviceManager instance
   */
  static getInstance(): DeviceManager {
    if (!DeviceManager.instance) {
      DeviceManager.instance = new DeviceManager();
    }
    return DeviceManager.instance;
  }

  /**
   * Discover devices
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  discoverDevices(callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      // 回调模式：传递回调函数给底层NAPI
      discoverDevices((error: Error | null, result: boolean) => {
        if (error) {
          logger.error(`Failed to discover devices with callback: ${error.message}`);
        } else {
          logger.info(`Device discovery started with callback: ${result}`);
        }
        callback(error, result);
      });
    } else {
      // Promise模式：不传递回调函数，让底层NAPI返回Promise
      return discoverDevices()
        .then((result: boolean) => {
          logger.info(`Device discovery started with promise: ${result}`);
          return result;
        }).catch((error: Error) => {
          logger.error(`Failed to discover devices with promise: ${error.message}`);
          throw error;
        });
    }
  }

  /**
   * Clear discovered devices
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  clearDiscoveredDevices(callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      // 回调模式：传递回调函数给底层NAPI
      clearDiscoveredDevices((error: Error | null, result: boolean) => {
        if (error) {
          logger.error(`Failed to clear discovered devices with callback: ${error.message}`);
        } else {
          // Clear local device list
          this.deviceList.clear();
          logger.info('Cleared discovered devices with callback');
        }
        callback(error, result);
      });
    } else {
      // Promise模式：不传递回调函数，让底层NAPI返回Promise
      return clearDiscoveredDevices()
        .then((result: boolean) => {
          // Clear local device list
          this.deviceList.clear();
          logger.info('Cleared discovered devices with promise');
          return result;
        }).catch((error: Error) => {
          logger.error(`Failed to clear discovered devices with promise: ${error.message}`);
          throw error;
        });
    }
  }

  /**
   * Get discovered devices
   * @param callback Optional callback function
   * @returns Promise<DeviceInfo[]> if no callback is provided
   */
  getDiscoveredDevices(callback?: Callback<DeviceInfo[]>): Promise<DeviceInfo[]> | void {
    // Function to convert the device list map to array
    const getDeviceArray = (): DeviceInfo[] => {
      return Array.from(this.deviceList.values());
    };

    if (callback) {
      // 回调模式：传递回调函数给底层NAPI
      getDiscoveredDevices((error: Error | null, result: boolean) => {
        if (error) {
          logger.error(`Failed to get discovered devices with callback: ${error.message}`);
          callback(error, []);
        } else {
          const deviceArr = getDeviceArray() as DeviceInfo[];
          logger.info(`Retrieved discovered devices with callback: ${deviceArr}`);
          callback(null, deviceArr);
        }
      });
    } else {
      // Promise模式：不传递回调函数，让底层NAPI返回Promise
      return getDiscoveredDevices()
        .then((result: boolean) => {
          const deviceArr = getDeviceArray() as DeviceInfo[];
          logger.info(`Retrieved discovered devices with promise: ${deviceArr}`);
          return deviceArr;
        }).catch((error: Error) => {
          logger.error(`Failed to get discovered devices with promise: ${error.message}`);
          throw error;
        });
    }
  }

  /**
   * Set device information
   * @param options Device information options
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  setDeviceInfo(options: DeviceInfoOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      // 回调模式：传递回调函数给底层NAPI
      setDeviceInfo(options.name, options.password, (error: Error | null, result: boolean) => {
        if (error) {
          logger.error(`Failed to set device info with callback: ${error.message}`);
        } else {
          logger.info(`Set device info: name with callback=${options.name}`);
        }
        callback(error, result);
      });
    } else {
      // Promise模式：不传递回调函数，让底层NAPI返回Promise
      return setDeviceInfo(options.name, options.password)
        .then((result: boolean) => {
          logger.info(`Set device info with promise: name=${options.name}`);
          return result;
        }).catch((error: Error) => {
          logger.error(`Failed to set device info with promise: ${error.message}`);
          throw error;
        });
    }
  }

  /**
   * Set device model
   * @param model Device model
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  setDeviceModel(model: string, callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      // 回调模式：传递回调函数给底层NAPI
      setDeviceModel(model, (error: Error | null, result: boolean) => {
        if (error) {
          logger.error(`Failed to set device model with callback: ${error.message}`);
        } else {
          logger.info(`Set device model with callback: ${model}`);
        }
        callback(error, result);
      });
    } else {
      // Promise模式：不传递回调函数，让底层NAPI返回Promise
      return setDeviceModel(model)
        .then((result: boolean) => {
          logger.info(`Set device model with promise: ${model}`);
          return result;
        }).catch((error: Error) => {
          logger.error(`Failed to set device model with promise: ${error.message}`);
          throw error;
        });
    }
  }

  /**
   * Get device model
   * @returns Device model
   */
  getDeviceModel(): string {
    try {
      const model = getDeviceModel() as string;
      logger.info(`Retrieved device model: ${model}`);
      return model;
    } catch (error) {
      logger.error(`Failed to get device model: ${error.message}`);
      return '';
    }
  }

  /**
   * Reset device model
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  resetDeviceModel(callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      // 回调模式：传递回调函数给底层NAPI
      resetDeviceModel((error: Error | null, result: boolean) => {
        if (error) {
          logger.error(`Failed to reset device model with callback: ${error.message}`);
        } else {
          logger.info('Reset device model with callback');
        }
        callback(error, result);
      });
    } else {
      // Promise模式：不传递回调函数，让底层NAPI返回Promise
      return resetDeviceModel()
        .then((result: boolean) => {
          logger.info('Reset device model with promise');
          return result;
        }).catch((error: Error) => {
          logger.error(`Failed to reset device model with promise: ${error.message}`);
          throw error;
        });
    }
  }

  /**
   * Get root path
   * @returns Root path
   */
  getRootPath(): string {
    try {
      const path = getRootPath() as string;
      logger.info(`Retrieved root path: ${path}`);
      return path;
    } catch (error) {
      logger.error(`Failed to get root path: ${error.message}`);
      return '';
    }
  }

  /**
   * Get current directory
   * @returns Current directory
   */
  getCurrentDirector(): string {
    try {
      const path = getCurrentDirector() as string;
      logger.info(`Retrieved current directory: ${path}`);
      return path;
    } catch (error) {
      logger.error(`Failed to get current directory: ${error.message}`);
      return '';
    }
  }

  /**
   * Find a remote device
   * @param pincode Device PIN code
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  findRemoteDevice(pincode: string, callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      // 回调模式：传递回调函数给底层NAPI
      findRemoteDevice(pincode, (error: Error | null, result: boolean) => {
        if (error) {
          logger.error(`Failed to find remote device with callback: ${error.message}`);
        } else {
          logger.info(`Finding remote device with pincode with callback: ${pincode}`);
        }
        callback(error, result);
      });
    } else {
      // Promise模式：不传递回调函数，让底层NAPI返回Promise
      return findRemoteDevice(pincode)
        .then((result: boolean) => {
          logger.info(`Finding remote device with pincode with promise: ${pincode}`);
          return result;
        }).catch((error: Error) => {
          logger.error(`Failed to find remote device with promise: ${error.message}`);
          throw error;
        });
    }
  }

  /**
   * 设置本机网络参数
   * @param options 网络信息
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  setNetworkInfo(options: NetworkInfoOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      // 回调模式：传递回调函数给底层NAPI
      setNetworkInfo(options.networkType || 0, options.addr, options.mac, (error: Error | null, result: boolean) => {
        if (error) {
          logger.error(`Failed to set network info with callback: ${error.message}`);
        } else {
          logger.info(`Setting Network Info with callback: ${JSON.stringify(options)}`);
        }
        callback(error, result);
      });
    } else {
      // Promise模式：不传递回调函数，让底层NAPI返回Promise
      return setNetworkInfo(options.networkType || 0, options.addr, options.mac)
        .then((result: boolean) => {
          logger.info(`Setting Network Info with promise: ${JSON.stringify(options)}`);
          return result;
        }).catch((error: Error) => {
          logger.error(`Failed to set network info with promise: ${error.message}`);
          throw error;
        });
    }
  }

  /**
   * Add a device to the local device list
   * @param device Device information
   */
  addDevice(device: DeviceInfo): void {
    this.deviceList.set(device.id, device);
    logger.info(`Added device: ${device.name} (${device.id})`);
  }

  /**
   * Remove a device from the local device list
   * @param deviceId Device ID
   */
  removeDevice(deviceId: string): void {
    if (this.deviceList.has(deviceId)) {
      this.deviceList.delete(deviceId);
      logger.info(`Removed device: ${deviceId}`);
    }
  }

  /**
   * Update a device in the local device list
   * @param device Device information
   */
  updateDevice(device: DeviceInfo): void {
    if (this.deviceList.has(device.id)) {
      this.deviceList.set(device.id, device);
      logger.info(`Updated device: ${device.name} (${device.id})`);
    }
  }
}