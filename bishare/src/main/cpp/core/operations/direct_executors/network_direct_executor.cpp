#include "direct_executors/network_direct_executor.h"
#include "bishare_napi.h"
#include "bishare_logger.h"
#include "bishare-service.h"
#include "bishare_status_codes.h"

namespace OHOS {
    namespace BiShare {

        const char* NetworkDirectExecutor::TAG = "NetworkDirectExecutor";

        void NetworkDirectExecutor::RegisterAll() {
            BiShareLogger::Info(TAG, "📝 [注册] 开始注册网络管理直接执行器...");
            
            RegisterDirectExecutor("SetNetworkInfo", ExecuteSetNetworkInfo);
            RegisterDirectExecutor("GetRootPath", ExecuteGetRootPath);
            RegisterDirectExecutor("GetCurrentDirector", ExecuteGetCurrentDirector);
            
            BiShareLogger::Info(TAG, "✅ [注册] 网络管理直接执行器注册完成");
        }

        void NetworkDirectExecutor::ExecuteSetNetworkInfo(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(TAG, "🚀 [直接执行] 开始执行SetNetworkInfo操作");

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务未初始化");
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return;
            }

            // 解析参数（需要网络类型、IP地址、MAC地址）
            network_type_t networkType = static_cast<network_type_t>(workData->data.intParam1);
            const std::string& ipAddr = workData->data.stringParam1;
            const std::string& macAddr = workData->data.stringParam2;

            BiShareLogger::Info(TAG, "🔧 [直接执行] 设置网络信息...");
            BiShareLogger::Info(TAG, "📍 [直接执行] 网络类型: %d, IP: %s, MAC: %s",
                networkType, ipAddr.c_str(), macAddr.c_str());

            // 调用原生设置网络信息函数（需要3个参数）
            workData->result = bishare_service_set_network_info(networkType, ipAddr.c_str(), macAddr.c_str());

            BiShareLogger::Info(TAG, "📊 [直接执行] 设置网络信息结果: %d (%s)",
                static_cast<int>(workData->result), err2str(workData->result));

            if (workData->result == BS_OK) {
                workData->successMessage = "网络信息设置成功";
                BiShareLogger::Info(TAG, "🎉 [直接执行] 网络信息设置成功");
            } else {
                workData->errorMessage = std::string("网络信息设置失败: ") +
                    std::string(err2str(workData->result));
                BiShareLogger::Error(TAG, "❌ [直接执行] 网络信息设置失败: %s",
                    err2str(workData->result));
            }

            BiShareLogger::Info(TAG, "🏁 [直接执行] SetNetworkInfo操作执行完成");
        }

        void NetworkDirectExecutor::ExecuteGetRootPath(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(TAG, "🚀 [直接执行] 开始执行GetRootPath操作");

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务未初始化");
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return;
            }

            BiShareLogger::Info(TAG, "🔧 [直接执行] 获取根路径...");

            // 调用原生获取根路径函数（返回const char*）
            const char* rootPath = bishare_service_get_root_path();

            if (rootPath != nullptr) {
                workData->result = BS_OK;
                workData->successMessage = std::string("根路径获取成功: ") + std::string(rootPath);
                // 将结果存储在stringParam1中，供后续处理使用
                workData->data.stringParam1 = std::string(rootPath);
                BiShareLogger::Info(TAG, "🎉 [直接执行] 根路径获取成功: %s", rootPath);
            } else {
                workData->result = BS_PARAMS_ERROR;  // 使用现有的错误码
                workData->errorMessage = "根路径获取失败：返回空指针";
                BiShareLogger::Error(TAG, "❌ [直接执行] 根路径获取失败：返回空指针");
            }

            BiShareLogger::Info(TAG, "🏁 [直接执行] GetRootPath操作执行完成");
        }

        void NetworkDirectExecutor::ExecuteGetCurrentDirector(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(TAG, "🚀 [直接执行] 开始执行GetCurrentDirector操作");

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务未初始化");
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return;
            }

            BiShareLogger::Info(TAG, "🔧 [直接执行] 获取当前目录...");

            // 调用原生获取当前目录函数（返回const char*）
            const char* currentDir = bishare_service_get_current_director();

            if (currentDir != nullptr) {
                workData->result = BS_OK;
                workData->successMessage = std::string("当前目录获取成功: ") + std::string(currentDir);
                // 将结果存储在stringParam1中，供后续处理使用
                workData->data.stringParam1 = std::string(currentDir);
                BiShareLogger::Info(TAG, "🎉 [直接执行] 当前目录获取成功: %s", currentDir);
            } else {
                workData->result = BS_PARAMS_ERROR;  // 使用现有的错误码
                workData->errorMessage = "当前目录获取失败：返回空指针";
                BiShareLogger::Error(TAG, "❌ [直接执行] 当前目录获取失败：返回空指针");
            }

            BiShareLogger::Info(TAG, "🏁 [直接执行] GetCurrentDirector操作执行完成");
        }

        // 全局注册函数
        void RegisterNetworkDirectExecutors() {
            NetworkDirectExecutor::RegisterAll();
        }

    } // namespace BiShare
} // namespace OHOS
