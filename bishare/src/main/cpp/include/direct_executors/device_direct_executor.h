#ifndef DEVICE_DIRECT_EXECUTOR_H
#define DEVICE_DIRECT_EXECUTOR_H

#include "bishare_operations.h"

namespace OHOS {
    namespace BiShare {

        /**
         * 设备管理直接执行器
         * 负责设备发现、设备列表管理等功能
         */
        class DeviceDirectExecutor {
        public:
            // 注册所有设备管理相关的直接执行函数
            static void RegisterAll();

            // 具体的直接执行函数
            static void ExecuteDiscoverDevices(napi_env env, AsyncWorkData* workData);
            static void ExecuteGetDiscoveredDevices(napi_env env, AsyncWorkData* workData);
            static void ExecuteClearDiscoveredDevices(napi_env env, AsyncWorkData* workData);
            static void ExecuteSetDeviceInfo(napi_env env, AsyncWorkData* workData);
            static void ExecuteFindRemoteDevice(napi_env env, AsyncWorkData* workData);

        private:
            static const char* TAG;
        };

        // 全局注册函数（供bishare_operations.cpp调用）
        void RegisterDeviceDirectExecutors();

    } // namespace BiShare
} // namespace OHOS

#endif // DEVICE_DIRECT_EXECUTOR_H
