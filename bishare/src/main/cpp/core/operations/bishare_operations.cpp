#include "bishare_operations.h"
#include "bishare_napi.h"
#include "bishare_logger.h"
#include "bishare_device.h"
#include "bishare_recording.h"
#include "bishare_utils.h"
#include "bishare_status_codes.h"
#include <thread>
#include <sstream>

#include <napi/native_api.h>

namespace OHOS {
    namespace BiShare {

        static constexpr const char *OPERATIONS_TAG = "BiShareOperations";

        // 辅助函数：安全地将线程ID转换为字符串
        static std::string ThreadIdToString(std::thread::id id) {
            std::ostringstream oss;
            oss << id;
            return oss.str();
        }

        // 工厂类静态成员初始化
        std::map<BiShareOperationFactory::OperationType, BiShareOperationFactory::OperationCreator> 
            BiShareOperationFactory::creators_;

        // 创建字符串辅助函数
        napi_value CreateStringUtf8(napi_env env, const char *str, size_t length = NAPI_AUTO_LENGTH) {
            napi_value result;
            napi_create_string_utf8(env, str, length, &result);
            return result;
        }

        // 创建整数辅助函数
        napi_value CreateInt32(napi_env env, int32_t value) {
            napi_value result;
            napi_create_int32(env, value, &result);
            return result;
        }

        // 创建布尔值辅助函数
        napi_value CreateBoolean(napi_env env, bool value) {
            napi_value result;
            napi_get_boolean(env, value, &result);
            return result;
        }

        // BiShareAsyncOperation 实现

        napi_value BiShareAsyncOperation::Execute(napi_env env, napi_callback_info info) {
            // 创建工作数据
            auto workData = std::make_unique<AsyncWorkData>();
            workData->env = env;
            workData->workName = operationName_;
            workData->operation = this; // 设置Operation实例指针

            // 解析参数
            if (!ParseArguments(env, info, workData.get())) {
                napi_value error, message;
                napi_create_string_utf8(env, "参数解析失败", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            // 验证参数
            if (!ValidateArguments(workData.get())) {
                napi_value error, message;
                napi_create_string_utf8(env, "参数验证失败", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            // 创建异步工作
            return CreateAsyncWork(env, workData.release(), ExecuteCallback, CompleteCallback);
        }

        napi_value BiShareAsyncOperation::ExecuteSync(napi_env env, napi_callback_info info) {
            try {
                BiShareLogger::Info(OPERATIONS_TAG, "开始同步执行操作: %s", operationName_.c_str());

                // 解析参数（同步版本）
                if (!ParseArgumentsSync(env, info)) {
                    napi_value error, message;
                    napi_create_string_utf8(env, "同步参数解析失败", NAPI_AUTO_LENGTH, &message);
                    napi_create_error(env, nullptr, message, &error);
                    return error;
                }

                // 直接执行同步操作
                napi_value result = ExecuteOperationSync(env, info);

                BiShareLogger::Info(OPERATIONS_TAG, "同步执行操作完成: %s", operationName_.c_str());
                return result;

            } catch (const std::exception& e) {
                BiShareLogger::Error(OPERATIONS_TAG, "同步执行操作异常: %s, 错误: %s", operationName_.c_str(), e.what());
                napi_value error, message;
                napi_create_string_utf8(env, e.what(), NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
        }

        napi_value BiShareAsyncOperation::CreateAsyncWork(napi_env env, AsyncWorkData* workData,
                                                        napi_async_execute_callback execute,
                                                        napi_async_complete_callback complete) {
            napi_value promise;
            napi_value resourceName;
            
            // 创建Promise
            napi_create_promise(env, &workData->deferred, &promise);
            
            // 创建资源名称
            napi_create_string_utf8(env, workData->workName.c_str(), NAPI_AUTO_LENGTH, &resourceName);
            
            // 创建异步工作
            napi_create_async_work(env, nullptr, resourceName, execute, complete, workData, &workData->work);
            
            // 排队异步工作
            napi_queue_async_work(env, workData->work);
            
            return promise;
        }

        void BiShareAsyncOperation::ExecuteCallback(napi_env env, void* data) {
            BiShareLogger::Info(OPERATIONS_TAG, "🚀 [后台线程] ExecuteCallback开始执行");
            BiShareLogger::Info(OPERATIONS_TAG, "📍 [后台线程] 线程ID: %s",
                ThreadIdToString(std::this_thread::get_id()).c_str());
            BiShareLogger::Info(OPERATIONS_TAG, "📍 [后台线程] data地址: %p", data);
            BiShareLogger::Info(OPERATIONS_TAG, "📍 [后台线程] env地址: %p", env);

            // 首先检查data指针是否有效
            if (data == nullptr) {
                BiShareLogger::Error(OPERATIONS_TAG, "❌ [后台线程] data指针为空");
                return;
            }

            AsyncWorkData* workData = static_cast<AsyncWorkData*>(data);
            BiShareLogger::Info(OPERATIONS_TAG, "📍 [后台线程] workData地址: %p", workData);

            // 检查workData是否有效（通过访问一个简单成员）
            try {
                BiShareLogger::Info(OPERATIONS_TAG, "🔍 [后台线程] 验证workData有效性...");
                // 尝试访问workData的成员来验证其有效性
                std::string workName = workData->workName;
                BiShareLogger::Info(OPERATIONS_TAG, "✅ [后台线程] workData有效，操作名称: %s", workName.c_str());
            } catch (...) {
                BiShareLogger::Error(OPERATIONS_TAG, "❌ [后台线程] workData无效或已损坏");
                return;
            }

            try {
                // 检查Operation实例指针是否有效
                BiShareLogger::Info(OPERATIONS_TAG, "🔍 [后台线程] 检查Operation实例指针...");
                BiShareLogger::Info(OPERATIONS_TAG, "📍 [后台线程] operation地址: %p", workData->operation);

                if (workData->operation != nullptr) {
                    BiShareLogger::Info(OPERATIONS_TAG, "✅ [后台线程] Operation实例指针有效，准备调用ExecuteOperation");
                    BiShareLogger::Info(OPERATIONS_TAG, "📍 [后台线程] 操作名称: %s", workData->workName.c_str());

                    // 验证operation指针的有效性
                    try {
                        BiShareLogger::Info(OPERATIONS_TAG, "🔍 [后台线程] 验证operation指针有效性...");
                        // 尝试访问operation的虚函数表来验证对象有效性
                        // 这里不直接调用虚函数，而是检查指针是否指向有效内存
                        void* vtable = *reinterpret_cast<void**>(workData->operation);
                        if (vtable == nullptr) {
                            BiShareLogger::Error(OPERATIONS_TAG, "❌ [后台线程] operation对象虚函数表无效");
                            workData->result = BS_OPS_ERROR;
                            workData->errorMessage = "Operation对象已损坏";
                            return;
                        }
                        BiShareLogger::Info(OPERATIONS_TAG, "✅ [后台线程] operation对象验证通过");
                    } catch (...) {
                        BiShareLogger::Error(OPERATIONS_TAG, "❌ [后台线程] operation对象验证失败");
                        workData->result = BS_OPS_ERROR;
                        workData->errorMessage = "Operation对象验证异常";
                        return;
                    }

                    // 调用具体操作的ExecuteOperation方法
                    BiShareLogger::Info(OPERATIONS_TAG, "🔄 [后台线程] 调用 %s::ExecuteOperation...", workData->workName.c_str());
                    BiShareLogger::Info(OPERATIONS_TAG, "📍 [后台线程] 调用前workData->result: %d", static_cast<int>(workData->result));

                    try {
                        workData->operation->ExecuteOperation(env, workData);
                        BiShareLogger::Info(OPERATIONS_TAG, "✅ [后台线程] ExecuteOperation调用成功");
                    } catch (const std::exception& e) {
                        BiShareLogger::Error(OPERATIONS_TAG, "❌ [后台线程] ExecuteOperation调用异常: %s", e.what());
                        workData->result = BS_OPS_ERROR;
                        workData->errorMessage = std::string("ExecuteOperation异常: ") + e.what();
                        return;
                    } catch (...) {
                        BiShareLogger::Error(OPERATIONS_TAG, "❌ [后台线程] ExecuteOperation调用未知异常");
                        workData->result = BS_OPS_ERROR;
                        workData->errorMessage = "ExecuteOperation未知异常";
                        return;
                    }

                    BiShareLogger::Info(OPERATIONS_TAG, "📍 [后台线程] 调用后workData->result: %d", static_cast<int>(workData->result));
                    BiShareLogger::Info(OPERATIONS_TAG, "📊 [后台线程] %s::ExecuteOperation执行完成，结果: %d (%s)",
                        workData->workName.c_str(), static_cast<int>(workData->result), err2str(workData->result));

                    if (workData->result == BS_OK) {
                        BiShareLogger::Info(OPERATIONS_TAG, "🎉 [后台线程] 操作 %s 执行成功", workData->workName.c_str());
                    } else {
                        BiShareLogger::Error(OPERATIONS_TAG, "❌ [后台线程] 操作 %s 执行失败: %s",
                            workData->workName.c_str(), workData->errorMessage.c_str());
                    }
                } else {
                    // 如果没有Operation实例，设置错误
                    workData->result = BS_ERROR;
                    workData->errorMessage = "Operation实例指针为空";
                    BiShareLogger::Error(OPERATIONS_TAG, "❌ [后台线程] Operation实例指针为空: %s", workData->workName.c_str());
                }

            } catch (const std::exception& e) {
                BiShareLogger::Error(OPERATIONS_TAG, "💥 [后台线程] 操作执行标准异常: %s", e.what());
                BiShareLogger::Error(OPERATIONS_TAG, "📍 [后台线程] 异常发生时操作: %s", workData->workName.c_str());
                BiShareLogger::Error(OPERATIONS_TAG, "📍 [后台线程] 异常发生时线程ID: %s",
                    ThreadIdToString(std::this_thread::get_id()).c_str());
                BiShareLogger::Error(OPERATIONS_TAG, "📍 [后台线程] 异常发生时result: %d", static_cast<int>(workData->result));
                workData->result = BS_ERROR;
                workData->errorMessage = std::string("操作执行异常: ") + e.what();
            } catch (...) {
                BiShareLogger::Error(OPERATIONS_TAG, "💥 [后台线程] 操作执行未知异常");
                BiShareLogger::Error(OPERATIONS_TAG, "📍 [后台线程] 未知异常发生时操作: %s", workData->workName.c_str());
                BiShareLogger::Error(OPERATIONS_TAG, "📍 [后台线程] 未知异常发生时线程ID: %s",
                    ThreadIdToString(std::this_thread::get_id()).c_str());
                BiShareLogger::Error(OPERATIONS_TAG, "📍 [后台线程] 未知异常发生时result: %d", static_cast<int>(workData->result));
                workData->result = BS_ERROR;
                workData->errorMessage = "操作执行未知异常";
            }

            BiShareLogger::Info(OPERATIONS_TAG, "🏁 [后台线程] ExecuteCallback执行完成: %s", workData->workName.c_str());
            BiShareLogger::Info(OPERATIONS_TAG, "📍 [后台线程] 最终result: %d (%s)",
                static_cast<int>(workData->result), err2str(workData->result));
            BiShareLogger::Info(OPERATIONS_TAG, "📍 [后台线程] 最终errorMessage: %s", workData->errorMessage.c_str());
        }

        void BiShareAsyncOperation::CompleteCallback(napi_env env, napi_status status, void* data) {
            BiShareLogger::Info(OPERATIONS_TAG, "🔄 [主线程] CompleteCallback开始执行");
            BiShareLogger::Info(OPERATIONS_TAG, "📍 [主线程] 线程ID: %s",
                ThreadIdToString(std::this_thread::get_id()).c_str());
            BiShareLogger::Info(OPERATIONS_TAG, "📍 [主线程] napi_status: %d", static_cast<int>(status));
            BiShareLogger::Info(OPERATIONS_TAG, "📍 [主线程] data地址: %p", data);
            BiShareLogger::Info(OPERATIONS_TAG, "📍 [主线程] env地址: %p", env);

            // 检查data指针有效性
            if (data == nullptr) {
                BiShareLogger::Error(OPERATIONS_TAG, "❌ [主线程] data指针为空");
                return;
            }

            AsyncWorkData* workData = static_cast<AsyncWorkData*>(data);
            BiShareLogger::Info(OPERATIONS_TAG, "📍 [主线程] workData地址: %p", workData);

            // 提前保存需要的信息，避免在删除workData后访问
            std::string workName;
            bstatus_t result;
            std::string errorMessage;
            napi_deferred deferred;
            napi_async_work work;

            try {
                workName = workData->workName;
                result = workData->result;
                errorMessage = workData->errorMessage;
                deferred = workData->deferred;
                work = workData->work;
                BiShareLogger::Info(OPERATIONS_TAG, "📍 [主线程] 操作: %s, 结果: %d (%s)",
                    workName.c_str(), static_cast<int>(result), err2str(result));
            } catch (...) {
                BiShareLogger::Error(OPERATIONS_TAG, "❌ [主线程] workData访问异常，可能已损坏");
                return;
            }

            napi_value napiResult;
            if (result == BS_OK) {
                BiShareLogger::Info(OPERATIONS_TAG, "✅ [主线程] 操作成功，创建成功响应");
                // 成功情况
                napi_create_object(env, &napiResult);
                napi_value successValue = CreateBoolean(env, true);
                napi_value messageValue = CreateStringUtf8(env, "操作成功");

                napi_set_named_property(env, napiResult, "success", successValue);
                napi_set_named_property(env, napiResult, "message", messageValue);

                BiShareLogger::Info(OPERATIONS_TAG, "📤 [主线程] 调用napi_resolve_deferred...");
                napi_resolve_deferred(env, deferred, napiResult);
                BiShareLogger::Info(OPERATIONS_TAG, "✅ [主线程] Promise已resolve");
            } else {
                BiShareLogger::Error(OPERATIONS_TAG, "❌ [主线程] 操作失败，创建错误响应");
                // 失败情况
                napi_value error, message;
                std::string errorMsg = errorMessage.empty() ?
                    std::string("操作失败，错误码: ") + std::to_string(result) :
                    errorMessage;

                BiShareLogger::Error(OPERATIONS_TAG, "📍 [主线程] 错误消息: %s", errorMsg.c_str());
                napi_create_string_utf8(env, errorMsg.c_str(), NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                BiShareLogger::Info(OPERATIONS_TAG, "📤 [主线程] 调用napi_reject_deferred...");
                napi_reject_deferred(env, deferred, error);
                BiShareLogger::Info(OPERATIONS_TAG, "✅ [主线程] Promise已reject");
            }

            // 清理资源
            BiShareLogger::Info(OPERATIONS_TAG, "🧹 [主线程] 开始清理资源...");
            BiShareLogger::Info(OPERATIONS_TAG, "📍 [主线程] 删除async_work...");
            napi_delete_async_work(env, work);
            BiShareLogger::Info(OPERATIONS_TAG, "✅ [主线程] async_work已删除");

            // 重要：workData是通过unique_ptr.release()释放的，需要手动删除
            BiShareLogger::Info(OPERATIONS_TAG, "📍 [主线程] 删除workData...");
            BiShareLogger::Info(OPERATIONS_TAG, "📍 [主线程] workData地址: %p", workData);
            delete workData;
            BiShareLogger::Info(OPERATIONS_TAG, "✅ [主线程] workData已删除");
            BiShareLogger::Info(OPERATIONS_TAG, "🏁 [主线程] CompleteCallback执行完成: %s", workName.c_str());
        }

        // BiShareServiceOperation 实现

        bool BiShareServiceOperation::CheckServiceInitialized(AsyncWorkData* workData) {
            if (!BiShareNapi::IsInitialized()) {
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return false;
            }
            return true;
        }

        napi_value BiShareServiceOperation::CreateStandardResult(napi_env env, AsyncWorkData* workData) {
            napi_value result;
            napi_create_object(env, &result);
            
            napi_value successValue = CreateBoolean(env, workData->result == BS_OK);
            napi_value codeValue = CreateInt32(env, static_cast<int32_t>(workData->result));
            napi_value messageValue = CreateStringUtf8(env, 
                workData->result == BS_OK ? "操作成功" : workData->errorMessage.c_str());
            
            napi_set_named_property(env, result, "success", successValue);
            napi_set_named_property(env, result, "code", codeValue);
            napi_set_named_property(env, result, "message", messageValue);
            
            return result;
        }

        // BiShareDeviceOperation 实现

        std::shared_ptr<BiShareDeviceManager> BiShareDeviceOperation::GetDeviceManager() {
            return BiShareNapi::GetInstance()->GetDeviceManager();
        }

        // BiShareRecordOperation 实现

        std::shared_ptr<BiShareRecordManager> BiShareRecordOperation::GetRecordManager() {
            return BiShareNapi::GetInstance()->GetRecordManager();
        }

        // BiShareOperationFactory 实现

        std::unique_ptr<BiShareAsyncOperation> BiShareOperationFactory::CreateOperation(OperationType type) {
            auto it = creators_.find(type);
            if (it != creators_.end()) {
                return it->second();
            }
            
            BiShareLogger::Error(OPERATIONS_TAG, "未找到操作类型: %d", static_cast<int>(type));
            return nullptr;
        }

        void BiShareOperationFactory::RegisterOperationCreator(OperationType type, OperationCreator creator) {
            creators_[type] = creator;
            BiShareLogger::Info(OPERATIONS_TAG, "注册操作创建器，类型: %d", static_cast<int>(type));
        }

    } // namespace BiShare
} // namespace OHOS
