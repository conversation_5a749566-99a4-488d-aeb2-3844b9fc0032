#include "recording_manager_napi.h"
#include "bishare_napi.h"
#include "bishare_logger.h"
#include "bishare_status_codes.h"
#include "async_executor.h"
#include <string>
#include <vector>
#include <mutex>
#include <chrono>
#include <algorithm>

namespace OHOS {
    namespace BiShare {
        namespace Interfaces {

            using namespace Infrastructure;

            // 静态成员初始化
            napi_ref RecordingManagerNapi::constructor_ = nullptr;
            std::shared_ptr<BiShareRecordManager> RecordingManagerNapi::nativeManager_ = nullptr;
            std::vector<RecordingManagerNapi::RecordingStatusListener> RecordingManagerNapi::statusListeners_;
            std::mutex RecordingManagerNapi::listenersMutex_;

            static constexpr const char* RECORDING_MANAGER_TAG = "RecordingManagerNapi";

            // 异步操作数据结构
            struct RecordingAsyncData {
                std::shared_ptr<BiShareRecordManager> recordManager;
                int32_t session;
                int32_t displayId;
                int32_t direction;
                std::string filePath;
                int32_t top, bottom, left, right;
                bstatus_t result;

                RecordingAsyncData() : session(0), displayId(0), direction(0),
                                     top(0), bottom(0), left(0), right(0), result(BS_ERROR) {}
            };

            // 构造函数
            RecordingManagerNapi::RecordingManagerNapi() {
                recordManager_ = GetNativeManager();
            }

            // 析构函数
            RecordingManagerNapi::~RecordingManagerNapi() {
                // 清理资源
            }

            // 获取原生管理器实例
            std::shared_ptr<BiShareRecordManager> RecordingManagerNapi::GetNativeManager() {
                if (!nativeManager_) {
                    auto* napiInstance = BiShareNapi::GetInstance();
                    nativeManager_ = napiInstance->GetRecordManager();
                }
                return nativeManager_;
            }

            // 初始化类到NAPI环境
            napi_value RecordingManagerNapi::Init(napi_env env, napi_value exports) {
                BiShareLogger::Info(RECORDING_MANAGER_TAG, "初始化RecordingManager类");

                // 定义类方法
                napi_property_descriptor properties[] = {
                    {"startScreenRecord", nullptr, StartScreenRecord, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"stopScreenRecord", nullptr, StopScreenRecord, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"screenshot", nullptr, Screenshot, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"addRecordingStatusListener", nullptr, AddRecordingStatusListener, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"removeRecordingStatusListener", nullptr, RemoveRecordingStatusListener, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"getRecordingStatus", nullptr, GetRecordingStatus, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"getRecordingProgress", nullptr, GetRecordingProgress, nullptr, nullptr, nullptr, napi_default, nullptr},
                };

                // 定义静态方法
                napi_property_descriptor staticProperties[] = {
                    {"getInstance", nullptr, GetInstance, nullptr, nullptr, nullptr, napi_static, nullptr},
                };

                napi_value constructor;
                napi_status status = napi_define_class(
                    env, "RecordingManager", NAPI_AUTO_LENGTH,
                    New, nullptr,
                    sizeof(properties) / sizeof(properties[0]), properties,
                    &constructor);

                if (status != napi_ok) {
                    BiShareLogger::Error(RECORDING_MANAGER_TAG, "定义RecordingManager类失败");
                    return nullptr;
                }

                // 添加静态方法
                napi_define_properties(env, constructor, 
                    sizeof(staticProperties) / sizeof(staticProperties[0]), staticProperties);

                // 创建构造函数引用
                status = napi_create_reference(env, constructor, 1, &constructor_);
                if (status != napi_ok) {
                    BiShareLogger::Error(RECORDING_MANAGER_TAG, "创建构造函数引用失败");
                    return nullptr;
                }

                // 导出类
                status = napi_set_named_property(env, exports, "RecordingManager", constructor);
                if (status != napi_ok) {
                    BiShareLogger::Error(RECORDING_MANAGER_TAG, "导出RecordingManager类失败");
                    return nullptr;
                }

                BiShareLogger::Info(RECORDING_MANAGER_TAG, "RecordingManager类初始化成功");
                return exports;
            }

            // 构造函数
            napi_value RecordingManagerNapi::New(napi_env env, napi_callback_info info) {
                napi_value thisArg;
                napi_get_cb_info(env, info, nullptr, nullptr, &thisArg, nullptr);

                // 创建C++实例
                RecordingManagerNapi* instance = new RecordingManagerNapi();

                // 包装到NAPI对象
                napi_status status = napi_wrap(env, thisArg, instance,
                    [](napi_env env, void* data, void* hint) {
                        delete static_cast<RecordingManagerNapi*>(data);
                    }, nullptr, nullptr);

                if (status != napi_ok) {
                    delete instance;
                    BiShareLogger::Error(RECORDING_MANAGER_TAG, "包装RecordingManager实例失败");
                    return nullptr;
                }

                return thisArg;
            }

            // 获取单例实例
            napi_value RecordingManagerNapi::GetInstance(napi_env env, napi_callback_info info) {
                BiShareLogger::Info(RECORDING_MANAGER_TAG, "获取RecordingManager单例实例");

                if (!constructor_) {
                    BiShareLogger::Error(RECORDING_MANAGER_TAG, "RecordingManager类未初始化");
                    return nullptr;
                }

                napi_value constructor;
                napi_status status = napi_get_reference_value(env, constructor_, &constructor);
                if (status != napi_ok) {
                    BiShareLogger::Error(RECORDING_MANAGER_TAG, "获取构造函数失败");
                    return nullptr;
                }

                napi_value instance;
                status = napi_new_instance(env, constructor, 0, nullptr, &instance);
                if (status != napi_ok) {
                    BiShareLogger::Error(RECORDING_MANAGER_TAG, "创建RecordingManager实例失败");
                    return nullptr;
                }

                return instance;
            }

            // 从this参数中获取实例
            RecordingManagerNapi* RecordingManagerNapi::GetInstanceFromThis(napi_env env, napi_value thisArg) {
                RecordingManagerNapi* instance = nullptr;
                napi_status status = napi_unwrap(env, thisArg, reinterpret_cast<void**>(&instance));
                if (status != napi_ok || !instance) {
                    BiShareLogger::Error(RECORDING_MANAGER_TAG, "获取RecordingManager实例失败");
                    return nullptr;
                }
                return instance;
            }

            // 开始屏幕录制 - 异步执行版本
            napi_value RecordingManagerNapi::StartScreenRecord(napi_env env, napi_callback_info info) {
                BiShareLogger::Info(RECORDING_MANAGER_TAG, "开始屏幕录制 - 异步执行");

                size_t argc = 1;
                napi_value args[1];
                napi_value thisArg;
                napi_get_cb_info(env, info, &argc, args, &thisArg, nullptr);

                RecordingManagerNapi* instance = GetInstanceFromThis(env, thisArg);
                if (!instance) {
                    napi_deferred deferred;
                    napi_value promise;
                    napi_create_promise(env, &deferred, &promise);
                    napi_value error;
                    napi_create_string_utf8(env, "Invalid instance", NAPI_AUTO_LENGTH, &error);
                    napi_reject_deferred(env, deferred, error);
                    return promise;
                }

                if (argc < 1) {
                    BiShareLogger::Error(RECORDING_MANAGER_TAG, "参数不足");
                    napi_deferred deferred;
                    napi_value promise;
                    napi_create_promise(env, &deferred, &promise);
                    napi_value error;
                    napi_create_string_utf8(env, "Insufficient parameters", NAPI_AUTO_LENGTH, &error);
                    napi_reject_deferred(env, deferred, error);
                    return promise;
                }

                // 创建异步数据
                auto asyncData = new RecordingAsyncData();
                asyncData->recordManager = instance->recordManager_;

                // 解析参数
                napi_value sessionValue, displayIdValue, directionValue;
                napi_get_named_property(env, args[0], "session", &sessionValue);
                napi_get_named_property(env, args[0], "displayId", &displayIdValue);
                napi_get_named_property(env, args[0], "direction", &directionValue);

                napi_get_value_int32(env, sessionValue, &asyncData->session);
                napi_get_value_int32(env, displayIdValue, &asyncData->displayId);
                napi_get_value_int32(env, directionValue, &asyncData->direction);

                // 使用AsyncExecutor执行异步操作
                return AsyncExecutor::ExecuteAsync(env, "StartScreenRecord",
                    // 执行函数 - 在后台线程执行
                    [](AsyncWorkData* workData) {
                        auto data = static_cast<RecordingAsyncData*>(workData->userData);
                        BiShareLogger::Info(RECORDING_MANAGER_TAG, "后台线程执行StartScreenRecord");
                        data->result = data->recordManager->StartScreenRecord(
                            data->session, data->displayId, data->direction);

                        // 通知状态监听器录制开始
                        if (data->result == BS_OK) {
                            RecordingProgress progress = {0, 0, 0, 0.0, "recording"};
                            NotifyStatusListeners("recording_started", progress);
                        }
                    },
                    // 完成函数 - 在主线程执行
                    [](napi_env env, AsyncWorkData* workData) -> napi_value {
                        auto data = static_cast<RecordingAsyncData*>(workData->userData);
                        napi_value result;
                        napi_get_boolean(env, data->result == BS_OK, &result);
                        delete data; // 清理异步数据
                        return result;
                    },
                    asyncData);
            }

            // 停止屏幕录制 - 异步执行版本
            napi_value RecordingManagerNapi::StopScreenRecord(napi_env env, napi_callback_info info) {
                BiShareLogger::Info(RECORDING_MANAGER_TAG, "停止屏幕录制 - 异步执行");

                size_t argc = 1;
                napi_value args[1];
                napi_value thisArg;
                napi_get_cb_info(env, info, &argc, args, &thisArg, nullptr);

                RecordingManagerNapi* instance = GetInstanceFromThis(env, thisArg);
                if (!instance) {
                    napi_deferred deferred;
                    napi_value promise;
                    napi_create_promise(env, &deferred, &promise);
                    napi_value error;
                    napi_create_string_utf8(env, "Invalid instance", NAPI_AUTO_LENGTH, &error);
                    napi_reject_deferred(env, deferred, error);
                    return promise;
                }

                if (argc < 1) {
                    BiShareLogger::Error(RECORDING_MANAGER_TAG, "参数不足");
                    napi_deferred deferred;
                    napi_value promise;
                    napi_create_promise(env, &deferred, &promise);
                    napi_value error;
                    napi_create_string_utf8(env, "Insufficient parameters", NAPI_AUTO_LENGTH, &error);
                    napi_reject_deferred(env, deferred, error);
                    return promise;
                }

                // 创建异步数据
                auto asyncData = new RecordingAsyncData();
                asyncData->recordManager = instance->recordManager_;

                // 解析参数
                napi_value sessionValue, displayIdValue, directionValue;
                napi_get_named_property(env, args[0], "session", &sessionValue);
                napi_get_named_property(env, args[0], "displayId", &displayIdValue);
                napi_get_named_property(env, args[0], "direction", &directionValue);

                napi_get_value_int32(env, sessionValue, &asyncData->session);
                napi_get_value_int32(env, displayIdValue, &asyncData->displayId);
                napi_get_value_int32(env, directionValue, &asyncData->direction);

                // 使用AsyncExecutor执行异步操作
                return AsyncExecutor::ExecuteAsync(env, "StopScreenRecord",
                    // 执行函数 - 在后台线程执行
                    [](AsyncWorkData* workData) {
                        auto data = static_cast<RecordingAsyncData*>(workData->userData);
                        BiShareLogger::Info(RECORDING_MANAGER_TAG, "后台线程执行StopScreenRecord");
                        data->result = data->recordManager->StopScreenRecord(
                            data->session, data->displayId, data->direction);

                        // 通知状态监听器录制停止
                        if (data->result == BS_OK) {
                            RecordingProgress progress = {120, 1024*1024*50, 3600, 30.0, "stopped"};
                            NotifyStatusListeners("recording_stopped", progress);
                        }
                    },
                    // 完成函数 - 在主线程执行
                    [](napi_env env, AsyncWorkData* workData) -> napi_value {
                        auto data = static_cast<RecordingAsyncData*>(workData->userData);
                        napi_value result;
                        napi_get_boolean(env, data->result == BS_OK, &result);
                        delete data; // 清理异步数据
                        return result;
                    },
                    asyncData);
            }

            // 截图 - 异步执行版本
            napi_value RecordingManagerNapi::Screenshot(napi_env env, napi_callback_info info) {
                BiShareLogger::Info(RECORDING_MANAGER_TAG, "截图 - 异步执行");

                size_t argc = 1;
                napi_value args[1];
                napi_value thisArg;
                napi_get_cb_info(env, info, &argc, args, &thisArg, nullptr);

                RecordingManagerNapi* instance = GetInstanceFromThis(env, thisArg);
                if (!instance) {
                    napi_deferred deferred;
                    napi_value promise;
                    napi_create_promise(env, &deferred, &promise);
                    napi_value error;
                    napi_create_string_utf8(env, "Invalid instance", NAPI_AUTO_LENGTH, &error);
                    napi_reject_deferred(env, deferred, error);
                    return promise;
                }

                if (argc < 1) {
                    BiShareLogger::Error(RECORDING_MANAGER_TAG, "参数不足");
                    napi_deferred deferred;
                    napi_value promise;
                    napi_create_promise(env, &deferred, &promise);
                    napi_value error;
                    napi_create_string_utf8(env, "Insufficient parameters", NAPI_AUTO_LENGTH, &error);
                    napi_reject_deferred(env, deferred, error);
                    return promise;
                }

                // 创建异步数据
                auto asyncData = new RecordingAsyncData();
                asyncData->recordManager = instance->recordManager_;

                // 解析参数
                napi_value filePathValue, topValue, bottomValue, leftValue, rightValue;
                napi_get_named_property(env, args[0], "filePath", &filePathValue);
                napi_get_named_property(env, args[0], "top", &topValue);
                napi_get_named_property(env, args[0], "bottom", &bottomValue);
                napi_get_named_property(env, args[0], "left", &leftValue);
                napi_get_named_property(env, args[0], "right", &rightValue);

                // 获取字符串参数
                size_t filePathLen;
                napi_get_value_string_utf8(env, filePathValue, nullptr, 0, &filePathLen);
                char* filePath = new char[filePathLen + 1];
                napi_get_value_string_utf8(env, filePathValue, filePath, filePathLen + 1, nullptr);
                asyncData->filePath = std::string(filePath);
                delete[] filePath;

                napi_get_value_int32(env, topValue, &asyncData->top);
                napi_get_value_int32(env, bottomValue, &asyncData->bottom);
                napi_get_value_int32(env, leftValue, &asyncData->left);
                napi_get_value_int32(env, rightValue, &asyncData->right);

                // 使用AsyncExecutor执行异步操作
                return AsyncExecutor::ExecuteAsync(env, "Screenshot",
                    // 执行函数 - 在后台线程执行
                    [](AsyncWorkData* workData) {
                        auto data = static_cast<RecordingAsyncData*>(workData->userData);
                        BiShareLogger::Info(RECORDING_MANAGER_TAG, "后台线程执行Screenshot");
                        data->result = data->recordManager->Screenshot(
                            data->filePath, data->top, data->bottom, data->left, data->right);
                    },
                    // 完成函数 - 在主线程执行
                    [](napi_env env, AsyncWorkData* workData) -> napi_value {
                        auto data = static_cast<RecordingAsyncData*>(workData->userData);
                        napi_value result;
                        napi_get_boolean(env, data->result == BS_OK, &result);
                        delete data; // 清理异步数据
                        return result;
                    },
                    asyncData);
            }

            // 添加录制状态监听器
            napi_value RecordingManagerNapi::AddRecordingStatusListener(napi_env env, napi_callback_info info) {
                BiShareLogger::Info(RECORDING_MANAGER_TAG, "添加录制状态监听器");

                size_t argc = 1;
                napi_value args[1];
                napi_value thisArg;
                napi_get_cb_info(env, info, &argc, args, &thisArg, nullptr);

                if (argc < 1) {
                    napi_value result;
                    napi_create_string_utf8(env, "Missing callback parameter", NAPI_AUTO_LENGTH, &result);
                    return result;
                }

                // 检查参数是否为函数
                napi_valuetype valueType;
                napi_typeof(env, args[0], &valueType);
                if (valueType != napi_function) {
                    napi_value result;
                    napi_create_string_utf8(env, "Callback must be a function", NAPI_AUTO_LENGTH, &result);
                    return result;
                }

                // 创建回调引用
                napi_ref callbackRef;
                napi_status status = napi_create_reference(env, args[0], 1, &callbackRef);
                if (status != napi_ok) {
                    napi_value result;
                    napi_create_string_utf8(env, "Failed to create callback reference", NAPI_AUTO_LENGTH, &result);
                    return result;
                }

                // 生成监听器ID
                std::string listenerId = "listener_" + std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(
                    std::chrono::system_clock::now().time_since_epoch()).count());

                // 添加到监听器列表
                {
                    std::lock_guard<std::mutex> lock(listenersMutex_);
                    statusListeners_.push_back({env, callbackRef, listenerId});
                }

                BiShareLogger::Info(RECORDING_MANAGER_TAG, "录制状态监听器添加成功: %s", listenerId.c_str());

                // 返回监听器ID
                napi_value result;
                napi_create_string_utf8(env, listenerId.c_str(), NAPI_AUTO_LENGTH, &result);
                return result;
            }

            // 移除录制状态监听器
            napi_value RecordingManagerNapi::RemoveRecordingStatusListener(napi_env env, napi_callback_info info) {
                BiShareLogger::Info(RECORDING_MANAGER_TAG, "移除录制状态监听器");

                size_t argc = 1;
                napi_value args[1];
                napi_get_cb_info(env, info, &argc, nullptr, nullptr, nullptr);

                if (argc < 1) {
                    napi_value result;
                    napi_get_boolean(env, false, &result);
                    return result;
                }

                // 获取监听器ID
                size_t listenerIdLen;
                napi_get_value_string_utf8(env, args[0], nullptr, 0, &listenerIdLen);
                char* listenerId = new char[listenerIdLen + 1];
                napi_get_value_string_utf8(env, args[0], listenerId, listenerIdLen + 1, nullptr);

                bool removed = false;
                {
                    std::lock_guard<std::mutex> lock(listenersMutex_);
                    auto it = std::find_if(statusListeners_.begin(), statusListeners_.end(),
                        [listenerId](const RecordingStatusListener& listener) {
                            return listener.listenerId == listenerId;
                        });

                    if (it != statusListeners_.end()) {
                        napi_delete_reference(it->env, it->callbackRef);
                        statusListeners_.erase(it);
                        removed = true;
                    }
                }

                delete[] listenerId;

                BiShareLogger::Info(RECORDING_MANAGER_TAG, "录制状态监听器移除%s", removed ? "成功" : "失败");

                napi_value result;
                napi_get_boolean(env, removed, &result);
                return result;
            }

            // 获取当前录制状态
            napi_value RecordingManagerNapi::GetRecordingStatus(napi_env env, napi_callback_info info) {
                BiShareLogger::Info(RECORDING_MANAGER_TAG, "获取当前录制状态");

                // 模拟录制状态
                const char* status = "idle"; // idle, recording, paused, stopped

                napi_value result;
                napi_create_string_utf8(env, status, NAPI_AUTO_LENGTH, &result);
                return result;
            }

            // 获取录制进度
            napi_value RecordingManagerNapi::GetRecordingProgress(napi_env env, napi_callback_info info) {
                BiShareLogger::Info(RECORDING_MANAGER_TAG, "获取录制进度");

                // 创建进度对象
                napi_value progressObj;
                napi_create_object(env, &progressObj);

                // 模拟进度数据
                napi_value duration, fileSize, frameCount, fps, status;
                napi_create_int32(env, 120, &duration);           // 120秒
                napi_create_int64(env, 1024 * 1024 * 50, &fileSize); // 50MB
                napi_create_int32(env, 3600, &frameCount);        // 3600帧
                napi_create_double(env, 30.0, &fps);              // 30fps
                napi_create_string_utf8(env, "recording", NAPI_AUTO_LENGTH, &status);

                // 设置属性
                napi_set_named_property(env, progressObj, "duration", duration);
                napi_set_named_property(env, progressObj, "fileSize", fileSize);
                napi_set_named_property(env, progressObj, "frameCount", frameCount);
                napi_set_named_property(env, progressObj, "fps", fps);
                napi_set_named_property(env, progressObj, "status", status);

                return progressObj;
            }

            // 通知状态监听器
            void RecordingManagerNapi::NotifyStatusListeners(const std::string& status, const RecordingProgress& progress) {
                std::lock_guard<std::mutex> lock(listenersMutex_);

                for (const auto& listener : statusListeners_) {
                    try {
                        // 创建状态数据对象
                        napi_value statusData;
                        napi_create_object(listener.env, &statusData);

                        napi_value statusValue, durationValue, fileSizeValue, frameCountValue, fpsValue;
                        napi_create_string_utf8(listener.env, status.c_str(), NAPI_AUTO_LENGTH, &statusValue);
                        napi_create_int32(listener.env, progress.duration, &durationValue);
                        napi_create_int64(listener.env, progress.fileSize, &fileSizeValue);
                        napi_create_int32(listener.env, progress.frameCount, &frameCountValue);
                        napi_create_double(listener.env, progress.fps, &fpsValue);

                        napi_set_named_property(listener.env, statusData, "status", statusValue);
                        napi_set_named_property(listener.env, statusData, "duration", durationValue);
                        napi_set_named_property(listener.env, statusData, "fileSize", fileSizeValue);
                        napi_set_named_property(listener.env, statusData, "frameCount", frameCountValue);
                        napi_set_named_property(listener.env, statusData, "fps", fpsValue);

                        // 调用回调函数
                        napi_value callback, global, result;
                        napi_get_reference_value(listener.env, listener.callbackRef, &callback);
                        napi_get_global(listener.env, &global);
                        napi_value args[] = {statusData};
                        napi_call_function(listener.env, global, callback, 1, args, &result);

                    } catch (const std::exception& e) {
                        BiShareLogger::Error(RECORDING_MANAGER_TAG, "通知监听器失败: %s", e.what());
                    }
                }
            }

        } // namespace Interfaces
    } // namespace BiShare
} // namespace OHOS
