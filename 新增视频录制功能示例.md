# 新增视频录制功能示例

## 🎯 **问题回答**

> 如果后续需要新增视频录制，是否需要在if (workData->workName == "Initialize") 增加else判断，增加视频录制的代码进去？

**答案：不需要！** 

我已经优化了架构，采用**函数映射表**的方案，新增功能时只需要简单的配置，不需要修改复杂的if-else链。

## 🛠️ **优化后的架构**

### **1. 函数映射表**
```cpp
// 直接执行函数映射表
static std::unordered_map<std::string, DirectExecutorFunction> directExecutorMap = {
    {"Initialize", ExecuteInitializeDirectly},
    {"Release", ExecuteReleaseDirectly},
    {"DiscoverDevices", ExecuteDiscoverDevicesDirectly},
    {"GetDiscoveredDevices", ExecuteGetDiscoveredDevicesDirectly},
    {"ClearDiscoveredDevices", ExecuteClearDiscoveredDevicesDirectly}
    // 新增函数时，只需要在这里添加一行即可
    // {"StartVideoRecord", ExecuteStartVideoRecordDirectly},
    // {"StopVideoRecord", ExecuteStopVideoRecordDirectly},
};
```

### **2. 智能路由系统**
```cpp
// 使用函数映射表进行智能路由 - 避免虚函数调用问题
auto directExecutor = GetDirectExecutor(workData->workName);
if (directExecutor) {
    // 使用直接调用
    BiShareLogger::Info(OPERATIONS_TAG, "🚀 [后台线程] 直接执行%s操作", workData->workName.c_str());
    directExecutor(env, workData);
    BiShareLogger::Info(OPERATIONS_TAG, "✅ [后台线程] %s操作执行完成", workData->workName.c_str());
} else {
    // 对于未实现直接调用的操作，仍使用虚函数调用（但添加警告）
    // ... 虚函数调用逻辑
}
```

## 📝 **新增视频录制功能步骤**

### **步骤1：实现直接执行函数**

在 `bishare_operations.cpp` 中添加：

```cpp
// 直接执行StartVideoRecord操作，避免虚函数调用
void ExecuteStartVideoRecordDirectly(napi_env env, AsyncWorkData* workData) {
    BiShareLogger::Info(OPERATIONS_TAG, "🚀 [直接执行] 开始执行StartVideoRecord操作");

    // 检查服务是否已初始化
    if (!BiShareNapi::IsInitialized()) {
        BiShareLogger::Error(OPERATIONS_TAG, "❌ [直接执行] BiShare服务未初始化");
        workData->result = BS_NOT_INIT;
        workData->errorMessage = "BiShare服务未初始化";
        return;
    }

    // 解析参数（假设需要session、displayId、direction等参数）
    int32_t session = workData->data.intParam1;
    int32_t displayId = workData->data.intParam2;
    int32_t direction = workData->data.intParam3;

    BiShareLogger::Info(OPERATIONS_TAG, "🔧 [直接执行] 开始视频录制...");
    BiShareLogger::Info(OPERATIONS_TAG, "📍 [直接执行] 参数: session=%d, displayId=%d, direction=%d", 
        session, displayId, direction);

    // 调用原生视频录制函数
    workData->result = bishare_service_start_screen_record(session, displayId, direction);

    BiShareLogger::Info(OPERATIONS_TAG, "📊 [直接执行] 视频录制启动结果: %d (%s)",
        static_cast<int>(workData->result), err2str(workData->result));

    if (workData->result == BS_OK) {
        workData->successMessage = "视频录制启动成功";
        BiShareLogger::Info(OPERATIONS_TAG, "🎉 [直接执行] 视频录制启动成功");
    } else {
        workData->errorMessage = std::string("视频录制启动失败: ") + 
            std::string(err2str(workData->result));
        BiShareLogger::Error(OPERATIONS_TAG, "❌ [直接执行] 视频录制启动失败: %s",
            err2str(workData->result));
    }

    BiShareLogger::Info(OPERATIONS_TAG, "🏁 [直接执行] StartVideoRecord操作执行完成");
}

// 直接执行StopVideoRecord操作，避免虚函数调用
void ExecuteStopVideoRecordDirectly(napi_env env, AsyncWorkData* workData) {
    BiShareLogger::Info(OPERATIONS_TAG, "🚀 [直接执行] 开始执行StopVideoRecord操作");

    // 检查服务是否已初始化
    if (!BiShareNapi::IsInitialized()) {
        BiShareLogger::Error(OPERATIONS_TAG, "❌ [直接执行] BiShare服务未初始化");
        workData->result = BS_NOT_INIT;
        workData->errorMessage = "BiShare服务未初始化";
        return;
    }

    // 解析参数
    int32_t session = workData->data.intParam1;
    int32_t displayId = workData->data.intParam2;
    int32_t direction = workData->data.intParam3;

    BiShareLogger::Info(OPERATIONS_TAG, "🔧 [直接执行] 停止视频录制...");

    // 调用原生停止录制函数
    workData->result = bishare_service_stop_screen_record(session, displayId, direction);

    BiShareLogger::Info(OPERATIONS_TAG, "📊 [直接执行] 视频录制停止结果: %d (%s)",
        static_cast<int>(workData->result), err2str(workData->result));

    if (workData->result == BS_OK) {
        workData->successMessage = "视频录制停止成功";
        BiShareLogger::Info(OPERATIONS_TAG, "🎉 [直接执行] 视频录制停止成功");
    } else {
        workData->errorMessage = std::string("视频录制停止失败: ") + 
            std::string(err2str(workData->result));
        BiShareLogger::Error(OPERATIONS_TAG, "❌ [直接执行] 视频录制停止失败: %s",
            err2str(workData->result));
    }

    BiShareLogger::Info(OPERATIONS_TAG, "🏁 [直接执行] StopVideoRecord操作执行完成");
}
```

### **步骤2：添加函数声明**

在 `bishare_operations.h` 中添加：

```cpp
// 新增视频录制直接执行函数声明
void ExecuteStartVideoRecordDirectly(napi_env env, AsyncWorkData* workData);
void ExecuteStopVideoRecordDirectly(napi_env env, AsyncWorkData* workData);
```

### **步骤3：注册到映射表**

在 `bishare_operations.cpp` 的映射表中添加：

```cpp
// 直接执行函数映射表
static std::unordered_map<std::string, DirectExecutorFunction> directExecutorMap = {
    {"Initialize", ExecuteInitializeDirectly},
    {"Release", ExecuteReleaseDirectly},
    {"DiscoverDevices", ExecuteDiscoverDevicesDirectly},
    {"GetDiscoveredDevices", ExecuteGetDiscoveredDevicesDirectly},
    {"ClearDiscoveredDevices", ExecuteClearDiscoveredDevicesDirectly},
    // ✅ 新增视频录制功能 - 只需要添加这两行！
    {"StartVideoRecord", ExecuteStartVideoRecordDirectly},
    {"StopVideoRecord", ExecuteStopVideoRecordDirectly}
};
```

### **步骤4：动态注册（可选）**

如果需要在运行时动态添加，可以使用：

```cpp
// 在初始化时动态注册
void InitializeVideoRecordingSupport() {
    RegisterDirectExecutor("StartVideoRecord", ExecuteStartVideoRecordDirectly);
    RegisterDirectExecutor("StopVideoRecord", ExecuteStopVideoRecordDirectly);
    BiShareLogger::Info(OPERATIONS_TAG, "📝 [初始化] 视频录制功能已注册");
}
```

## 🎯 **优势对比**

### **❌ 旧方案（if-else链）**
```cpp
if (workData->workName == "Initialize") {
    ExecuteInitializeDirectly(env, workData);
} else if (workData->workName == "Release") {
    ExecuteReleaseDirectly(env, workData);
} else if (workData->workName == "DiscoverDevices") {
    ExecuteDiscoverDevicesDirectly(env, workData);
} else if (workData->workName == "GetDiscoveredDevices") {
    ExecuteGetDiscoveredDevicesDirectly(env, workData);
} else if (workData->workName == "ClearDiscoveredDevices") {
    ExecuteClearDiscoveredDevicesDirectly(env, workData);
} else if (workData->workName == "StartVideoRecord") {  // ❌ 需要修改这里
    ExecuteStartVideoRecordDirectly(env, workData);
} else if (workData->workName == "StopVideoRecord") {   // ❌ 需要修改这里
    ExecuteStopVideoRecordDirectly(env, workData);
} else {
    // 虚函数调用
}
```

**问题**：
- 每次新增功能都需要修改核心路由逻辑
- 代码越来越长，难以维护
- 容易出错，忘记添加判断

### **✅ 新方案（函数映射表）**
```cpp
// 只需要在映射表中添加一行
{"StartVideoRecord", ExecuteStartVideoRecordDirectly},
{"StopVideoRecord", ExecuteStopVideoRecordDirectly}
```

**优势**：
- **简单**：只需要添加一行配置
- **清晰**：所有直接执行函数一目了然
- **安全**：不会影响核心路由逻辑
- **可扩展**：支持动态注册
- **高性能**：O(1)查找时间复杂度

## 🔮 **未来扩展示例**

当需要添加更多功能时：

```cpp
// 直接执行函数映射表
static std::unordered_map<std::string, DirectExecutorFunction> directExecutorMap = {
    // 基础功能
    {"Initialize", ExecuteInitializeDirectly},
    {"Release", ExecuteReleaseDirectly},
    
    // 设备管理
    {"DiscoverDevices", ExecuteDiscoverDevicesDirectly},
    {"GetDiscoveredDevices", ExecuteGetDiscoveredDevicesDirectly},
    {"ClearDiscoveredDevices", ExecuteClearDiscoveredDevicesDirectly},
    
    // 视频录制
    {"StartVideoRecord", ExecuteStartVideoRecordDirectly},
    {"StopVideoRecord", ExecuteStopVideoRecordDirectly},
    
    // 音频录制
    {"StartAudioRecord", ExecuteStartAudioRecordDirectly},
    {"StopAudioRecord", ExecuteStopAudioRecordDirectly},
    
    // 屏幕截图
    {"TakeScreenshot", ExecuteTakeScreenshotDirectly},
    
    // 文件传输
    {"StartFileTransfer", ExecuteStartFileTransferDirectly},
    {"StopFileTransfer", ExecuteStopFileTransferDirectly},
    
    // 网络配置
    {"SetNetworkConfig", ExecuteSetNetworkConfigDirectly},
    {"GetNetworkStatus", ExecuteGetNetworkStatusDirectly}
};
```

## 🎉 **总结**

**回答您的问题**：
- ❌ **不需要**修改if-else链
- ✅ **只需要**在映射表中添加一行配置
- ✅ **更简单**、**更安全**、**更可维护**

这个新的架构让添加新功能变得非常简单，同时保持了代码的清晰性和可维护性！
