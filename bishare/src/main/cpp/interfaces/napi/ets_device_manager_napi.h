#ifndef ETS_DEVICE_MANAGER_NAPI_H
#define ETS_DEVICE_MANAGER_NAPI_H

#include <napi/native_api.h>
#include <memory>
#include "bishare_device.h"

namespace OHOS {
    namespace BiShare {
        namespace Interfaces {

            /**
             * EtsDeviceManager NAPI包装类
             * 
             * 职责：
             * 1. 提供JavaScript可调用的EtsDeviceManager类
             * 2. 实现单例模式
             * 3. 封装设备管理相关的NAPI方法
             */
            class EtsDeviceManagerNapi {
            public:
                /**
                 * 初始化EtsDeviceManager类到NAPI环境
                 */
                static napi_value Init(napi_env env, napi_value exports);

                /**
                 * 创建EtsDeviceManager实例
                 */
                static napi_value New(napi_env env, napi_callback_info info);

                /**
                 * 获取EtsDeviceManager单例实例
                 */
                static napi_value GetInstance(napi_env env, napi_callback_info info);

                /**
                 * 发现设备
                 */
                static napi_value DiscoverDevices(napi_env env, napi_callback_info info);

                /**
                 * 清除已发现的设备
                 */
                static napi_value ClearDiscoveredDevices(napi_env env, napi_callback_info info);

                /**
                 * 获取已发现的设备列表
                 */
                static napi_value GetDiscoveredDevices(napi_env env, napi_callback_info info);

                /**
                 * 设置设备信息
                 */
                static napi_value SetDeviceInfo(napi_env env, napi_callback_info info);

                /**
                 * 设置设备模型
                 */
                static napi_value SetDeviceModel(napi_env env, napi_callback_info info);

                /**
                 * 获取设备模型
                 */
                static napi_value GetDeviceModel(napi_env env, napi_callback_info info);

                /**
                 * 重置设备模型
                 */
                static napi_value ResetDeviceModel(napi_env env, napi_callback_info info);

                /**
                 * 查找远程设备
                 */
                static napi_value FindRemoteDevice(napi_env env, napi_callback_info info);

                /**
                 * 获取根路径
                 */
                static napi_value GetRootPath(napi_env env, napi_callback_info info);

                /**
                 * 获取当前目录
                 */
                static napi_value GetCurrentDirector(napi_env env, napi_callback_info info);

                /**
                 * 设置网络信息
                 */
                static napi_value SetNetworkInfo(napi_env env, napi_callback_info info);

            private:
                /**
                 * 获取原生DeviceManager实例
                 */
                static std::shared_ptr<BiShareDeviceManager> GetNativeManager();

                /**
                 * 从NAPI值中提取EtsDeviceManagerNapi实例
                 */
                static EtsDeviceManagerNapi* GetInstanceFromThis(napi_env env, napi_value thisArg);

                /**
                 * 单例实例
                 */
                static napi_ref constructor_;
                static std::shared_ptr<BiShareDeviceManager> nativeManager_;

                /**
                 * 私有构造函数
                 */
                EtsDeviceManagerNapi();
                ~EtsDeviceManagerNapi();

                // 实例数据
                std::shared_ptr<BiShareDeviceManager> deviceManager_;
            };

        } // namespace Interfaces
    } // namespace BiShare
} // namespace OHOS

#endif // ETS_DEVICE_MANAGER_NAPI_H
