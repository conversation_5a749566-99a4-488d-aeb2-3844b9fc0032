# BiShare模块

## 🎯 概述

BiShare模块是一个基于OpenHarmony NAPI的跨设备文件共享和通信框架，提供统一的设备发现、连接、文件传输、消息通信和录制回放功能。

## 📁 模块结构

```
bishare/
├── src/main/
│   ├── ets/                    # TypeScript接口层
│   │   ├── core/              # 核心管理器
│   │   ├── interfaces/        # 接口定义
│   │   ├── managers/          # 功能管理器
│   │   ├── operations/        # 操作类
│   │   └── index.ets          # 模块导出
│   └── cpp/                   # C++实现层
│       ├── napi_interface/    # NAPI桥接
│       ├── operations/        # 操作实现
│       └── core/              # 核心逻辑
├── DESIGN_PRINCIPLES.md       # 设计原理文档
├── INTEGRATION_GUIDE.md       # 接入指南文档
└── README.md                  # 本文档
```

## 🏗️ 架构设计

### 分层架构

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Entry Module)                      │
├─────────────────────────────────────────────────────────────┤
│                  TypeScript接口层                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ DeviceManager│ │NetworkManager│ │RecordingMgr │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    NAPI桥接层                               │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              BiShareNapiInterface                       │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    C++核心层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   设备管理   │ │   网络通信   │ │   文件传输   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

1. **BiShareManager**: 统一的入口管理器，负责整体生命周期
2. **DeviceManager**: 设备发现、连接、状态管理
3. **NetworkManager**: 网络配置和状态监控
4. **RecordingManager**: 录制和回放功能
5. **FileManager**: 文件传输和管理
6. **EventManager**: 事件监听和分发

## 🚀 快速开始

### 1. 基本初始化

```typescript
import { BiShareManager, InitOptions, BlogPriority } from '@ohos/libbishare_napi';

// 获取管理器实例
const biShareManager = BiShareManager.getInstance();

// 配置初始化选项
const initOptions: InitOptions = {
  isConsole: true,
  isFile: true,
  filePath: '/data/storage/el2/base/haps/entry/files/bishare.log',
  priority: BlogPriority.INFO
};

// 初始化BiShare
const success = await biShareManager.initialize(initOptions);
if (success) {
  console.log('BiShare初始化成功');
} else {
  console.error('BiShare初始化失败');
}
```

### 2. 设备发现和连接

```typescript
// 获取设备管理器
const deviceManager = biShareManager.getDeviceManager();

// 开始设备发现
const discoveryResult = await deviceManager.startDiscovery();
if (discoveryResult.success) {
  console.log('设备发现已启动');
}

// 获取发现的设备
const devices = deviceManager.getDiscoveredDevices();
console.log('发现的设备:', devices);

// 连接设备
if (devices.length > 0) {
  const connectResult = await deviceManager.connectDevice(devices[0].id);
  if (connectResult.success) {
    console.log('设备连接成功');
  }
}
```

### 3. 网络配置

```typescript
import { NetworkInfoOptions, NetworkType } from '@ohos/libbishare_napi';

// 获取网络管理器
const networkManager = biShareManager.getNetworkManager();

// 配置网络信息
const networkInfo: NetworkInfoOptions = {
  networkType: NetworkType.Wlan,
  addr: '*************',
  mac: '00:11:22:33:44:55'
};

// 设置网络信息
const result = await networkManager.setNetworkInfo(networkInfo);
if (result.success) {
  console.log('网络配置成功');
}
```

### 4. 事件监听

```typescript
import { DeviceEventListener, ExtendedDeviceInfo } from '@ohos/libbishare_napi';

// 创建设备事件监听器
class MyDeviceListener implements DeviceEventListener {
  onDeviceDiscovered(device: ExtendedDeviceInfo): void {
    console.log('发现新设备:', device.id, device.name);
  }
  
  onDeviceConnected(device: ExtendedDeviceInfo): void {
    console.log('设备已连接:', device.id);
  }
  
  onDeviceDisconnected(device: ExtendedDeviceInfo): void {
    console.log('设备已断开:', device.id);
  }
}

// 注册事件监听器
const eventManager = biShareManager.getEventManager();
eventManager.addDeviceEventListener(new MyDeviceListener());
```

## 🔧 核心功能

### 设备管理

- **设备发现**: 自动发现局域网内的BiShare设备
- **设备连接**: 建立与目标设备的连接
- **状态监控**: 实时监控设备连接状态
- **设备信息**: 获取设备详细信息和能力

### 网络管理

- **网络配置**: 设置网络参数和连接信息
- **状态监控**: 监控网络连接状态
- **自动重连**: 网络异常时自动重连
- **多网络支持**: 支持WiFi、以太网等多种网络

### 文件传输

- **文件发送**: 向目标设备发送文件
- **文件接收**: 接收来自其他设备的文件
- **传输进度**: 实时显示传输进度
- **断点续传**: 支持传输中断后的续传

### 录制回放

- **屏幕录制**: 录制设备屏幕内容
- **操作录制**: 录制用户操作序列
- **回放功能**: 回放录制的内容
- **格式支持**: 支持多种录制格式

## 📊 API参考

### 核心接口

```typescript
// 主管理器
interface BiShareManager {
  initialize(options: InitOptions): Promise<boolean>;
  release(): Promise<boolean>;
  getDeviceManager(): DeviceManager;
  getNetworkManager(): NetworkManager;
  getRecordingManager(): RecordingManager;
}

// 设备管理
interface DeviceManager {
  startDiscovery(): Promise<BiShareResult<boolean>>;
  stopDiscovery(): Promise<BiShareResult<boolean>>;
  connectDevice(deviceId: string): Promise<BiShareResult<boolean>>;
  disconnectDevice(deviceId: string): Promise<BiShareResult<boolean>>;
  getDiscoveredDevices(): ExtendedDeviceInfo[];
  getConnectedDevices(): ExtendedDeviceInfo[];
}

// 网络管理
interface NetworkManager {
  setNetworkInfo(info: NetworkInfoOptions): Promise<BiShareResult<boolean>>;
  getNetworkInfo(): Promise<BiShareResult<NetworkInfoOptions>>;
  isNetworkConnected(): boolean;
}
```

### 数据类型

```typescript
// 设备信息
interface ExtendedDeviceInfo {
  id: string;
  name: string;
  type: DeviceType;
  status: DeviceConnectionStatus;
  capabilities: string[];
}

// 网络信息
interface NetworkInfoOptions {
  networkType: NetworkType;
  addr: string;
  mac: string;
}

// 操作结果
interface BiShareResult<T> {
  success: boolean;
  data?: T;
  error?: BiShareError;
}
```

## 🛡️ 错误处理

BiShare模块提供完善的错误处理机制：

```typescript
// 错误信息结构
interface BiShareError {
  code: string;
  message: string;
  details?: any;
}

// 常见错误代码
const ErrorCodes = {
  INITIALIZATION_FAILED: 'INITIALIZATION_FAILED',
  DEVICE_NOT_FOUND: 'DEVICE_NOT_FOUND',
  CONNECTION_FAILED: 'CONNECTION_FAILED',
  NETWORK_ERROR: 'NETWORK_ERROR',
  PERMISSION_DENIED: 'PERMISSION_DENIED'
};
```

## 🔒 权限要求

使用BiShare模块需要以下权限：

```json
{
  "requestPermissions": [
    {
      "name": "ohos.permission.GET_NETWORK_INFO",
      "reason": "获取网络信息"
    },
    {
      "name": "ohos.permission.GET_WIFI_INFO", 
      "reason": "获取WiFi信息"
    },
    {
      "name": "ohos.permission.INTERNET",
      "reason": "网络通信"
    }
  ]
}
```

## 📚 详细文档

- **[设计原理文档](./DESIGN_PRINCIPLES.md)** - 详细的架构设计原理、设计模式应用和核心组件设计
- **[接入指南文档](./INTEGRATION_GUIDE.md)** - 新功能和管理器的详细接入方式、开发步骤和最佳实践

## 🔧 开发指南

### 编译要求

- OpenHarmony SDK 4.0+
- CMake 3.16+
- C++17支持
- Node.js 16+

### 构建步骤

```bash
# 清理构建
hvigor clean

# 编译模块
hvigor assembleHap --mode module -p module=bishare

# 运行测试
hvigor test
```

## 🎯 最佳实践

1. **初始化顺序**: 先初始化BiShare，再使用具体功能
2. **资源管理**: 及时释放不需要的资源
3. **错误处理**: 始终检查操作结果
4. **事件监听**: 合理使用事件监听，避免内存泄漏
5. **线程安全**: 注意多线程环境下的安全性
6. **性能优化**: 避免频繁的设备发现和连接操作

## 📈 版本历史

- **v1.0.0**: 初始版本，基础功能实现
- **v1.1.0**: 添加录制回放功能
- **v1.2.0**: 优化网络管理和错误处理
- **v1.3.0**: 增强事件系统和状态管理

---

BiShare模块为OpenHarmony应用提供了强大的跨设备通信能力，通过简洁的API和完善的功能，让开发者能够轻松实现设备间的协作和数据共享。
