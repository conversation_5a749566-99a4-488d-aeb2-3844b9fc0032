#include "direct_executors/service_direct_executor.h"
#include "bishare_napi.h"
#include "bishare_logger.h"
#include "bishare_callbacks.h"
#include "bishare-service.h"

namespace OHOS {
    namespace BiShare {

        const char* ServiceDirectExecutor::TAG = "ServiceDirectExecutor";

        void ServiceDirectExecutor::RegisterAll() {
            BiShareLogger::Info(TAG, "📝 [注册] 开始注册服务管理直接执行器...");
            
            RegisterDirectExecutor("Initialize", ExecuteInitialize);
            RegisterDirectExecutor("Release", ExecuteRelease);
            
            BiShareLogger::Info(TAG, "✅ [注册] 服务管理直接执行器注册完成");
        }

        void ServiceDirectExecutor::ExecuteInitialize(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(TAG, "🚀 [直接执行] 开始执行Initialize操作");

            // 检查是否已初始化
            BiShareLogger::Info(TAG, "🔍 [直接执行] 检查服务初始化状态...");
            bool isInitialized = BiShareNapi::IsInitialized();
            BiShareLogger::Info(TAG, "📍 [直接执行] 当前初始化状态: %s", isInitialized ? "已初始化" : "未初始化");

            if (isInitialized) {
                BiShareLogger::Info(TAG, "✅ [直接执行] BiShare服务已经初始化过了，跳过初始化");
                workData->result = BS_OK;
                workData->successMessage = "服务已经初始化";
                return;
            }

            // 验证和转换参数
            BiShareLogger::Info(TAG, "🔧 [直接执行] 验证和转换参数...");
            BiShareLogger::Info(TAG, "📍 [直接执行] 原始参数:");
            BiShareLogger::Info(TAG, "  - boolParam1: %s", workData->data.boolParam1 ? "true" : "false");
            BiShareLogger::Info(TAG, "  - boolParam2: %s", workData->data.boolParam2 ? "true" : "false");
            BiShareLogger::Info(TAG, "  - stringParam1: %s", workData->data.stringParam1.c_str());
            BiShareLogger::Info(TAG, "  - priority: %d", workData->data.priority);

            bool_type_t isConsole = workData->data.boolParam1 ? BOOL_TRUE : BOOL_FALSE;
            bool_type_t isFile = workData->data.boolParam2 ? BOOL_TRUE : BOOL_FALSE;

            BiShareLogger::Info(TAG, "📍 [直接执行] 转换后参数:");
            BiShareLogger::Info(TAG, "  - isConsole: %d", isConsole);
            BiShareLogger::Info(TAG, "  - isFile: %d", isFile);

            // 验证参数有效性
            if (workData->data.stringParam1.empty()) {
                BiShareLogger::Error(TAG, "❌ [直接执行] 日志路径为空");
                workData->result = BS_PARAMS_ERROR;
                workData->errorMessage = "日志路径参数无效";
                return;
            }

            // 调用原生初始化函数
            BiShareLogger::Info(TAG, "🔄 [直接执行] 准备调用bishare_service_init...");
            const char* pathPtr = workData->data.stringParam1.c_str();

            BiShareLogger::Info(TAG, "🎯 [直接执行] 开始原生库调用...");
            bstatus_t initResult = bishare_service_init(isConsole, isFile, pathPtr, workData->data.priority);
            BiShareLogger::Info(TAG, "🎯 [直接执行] 原生库调用返回");

            workData->result = initResult;
            BiShareLogger::Info(TAG, "📊 [直接执行] 返回结果: %d (%s)",
                static_cast<int>(workData->result), err2str(workData->result));

            // 如果初始化成功，注册回调并设置状态
            if (workData->result == BS_OK) {
                BiShareLogger::Info(TAG, "✅ [直接执行] 原生服务初始化成功，设置初始化状态...");
                BiShareNapi::SetInitialized(true);

                // 获取BiShareNapi实例并初始化回调系统
                BiShareLogger::Info(TAG, "🔗 [直接执行] 开始初始化回调系统...");
                auto* napiInstance = BiShareNapi::GetInstance();
                BiShareLogger::Info(TAG, "📍 [直接执行] BiShareNapi实例地址: %p", napiInstance);

                if (napiInstance) {
                    auto callbacks = napiInstance->GetCallbacks();
                    BiShareLogger::Info(TAG, "📍 [直接执行] 回调系统实例地址: %p", callbacks.get());

                    if (callbacks) {
                        // 设置静态实例
                        BiShareLogger::Info(TAG, "🔧 [直接执行] 设置静态回调实例...");
                        BiShareCallbacks::SetStaticInstance(callbacks);

                        // 注册回调
                        BiShareLogger::Info(TAG, "🔗 [直接执行] 开始注册事件回调...");
                        bstatus_t eventResult = bishare_service_register_event_callback(BiShareCallbacks::OnEventCallback);
                        BiShareLogger::Info(TAG, "📊 [直接执行] 事件回调注册结果: %d (%s)",
                            static_cast<int>(eventResult), err2str(eventResult));

                        BiShareLogger::Info(TAG, "🔗 [直接执行] 开始注册数据包回调...");
                        bstatus_t packetResult = bishare_service_register_packet_callback(BiShareCallbacks::OnPacketCallback);
                        BiShareLogger::Info(TAG, "📊 [直接执行] 数据包回调注册结果: %d (%s)",
                            static_cast<int>(packetResult), err2str(packetResult));

                        if (eventResult == BS_OK && packetResult == BS_OK) {
                            BiShareLogger::Info(TAG, "✅ [直接执行] 所有回调注册成功");
                        } else {
                            BiShareLogger::Warn(TAG, "⚠️ [直接执行] 部分回调注册失败");
                        }
                    } else {
                        BiShareLogger::Error(TAG, "❌ [直接执行] 回调系统实例为空");
                    }
                } else {
                    BiShareLogger::Error(TAG, "❌ [直接执行] BiShareNapi实例为空");
                }

                workData->successMessage = "BiShare服务初始化成功";
                BiShareLogger::Info(TAG, "🎉 [直接执行] BiShare服务初始化完全成功，回调已注册");
            } else {
                workData->errorMessage = std::string("BiShare服务初始化失败: ") +
                    std::string(err2str(workData->result));
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务初始化失败: %s",
                    err2str(workData->result));
            }

            BiShareLogger::Info(TAG, "🏁 [直接执行] Initialize操作执行完成");
        }

        void ServiceDirectExecutor::ExecuteRelease(napi_env env, AsyncWorkData* workData) {
            BiShareLogger::Info(TAG, "🚀 [直接执行] 开始执行Release操作");

            // 检查是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务未初始化，无法释放");
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return;
            }

            BiShareLogger::Info(TAG, "🔧 [直接执行] 开始释放BiShare服务...");

            // 调用原生释放函数
            workData->result = bishare_service_release();

            BiShareLogger::Info(TAG, "📊 [直接执行] 原生服务释放结果: %d (%s)",
                static_cast<int>(workData->result), err2str(workData->result));

            if (workData->result == BS_OK) {
                BiShareLogger::Info(TAG, "✅ [直接执行] 原生服务释放成功，设置初始化状态为false...");
                BiShareNapi::SetInitialized(false);
                workData->successMessage = "BiShare服务释放成功";
                BiShareLogger::Info(TAG, "🎉 [直接执行] BiShare服务释放完全成功");
            } else {
                workData->errorMessage = std::string("BiShare服务释放失败: ") +
                    std::string(err2str(workData->result));
                BiShareLogger::Error(TAG, "❌ [直接执行] BiShare服务释放失败: %s",
                    err2str(workData->result));
            }

            BiShareLogger::Info(TAG, "🏁 [直接执行] Release操作执行完成");
        }

        // 全局注册函数
        void RegisterServiceDirectExecutors() {
            ServiceDirectExecutor::RegisterAll();
        }

    } // namespace BiShare
} // namespace OHOS
