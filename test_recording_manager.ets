import { RecordingManager } from './entry/src/main/ets/comm/managers/RecordingManager';
import { RecordingOptions, Direction } from '@ohos/libbishare_napi';

// 测试RecordingManager是否能正常创建和使用
function testRecordingManager() {
  try {
    // 创建RecordingManager实例
    const recordingManager = new RecordingManager();
    console.log('✅ RecordingManager创建成功');
    
    // 测试基本方法
    const status = recordingManager.getRecordingStatus();
    console.log('✅ getRecordingStatus方法调用成功:', status);
    
    const isRecording = recordingManager.isRecording();
    console.log('✅ isRecording方法调用成功:', isRecording);
    
    const currentRecording = recordingManager.getCurrentRecording();
    console.log('✅ getCurrentRecording方法调用成功:', currentRecording);
    
    console.log('🎉 所有基本测试通过！RecordingManager修复成功！');
    
  } catch (error) {
    console.error('❌ RecordingManager测试失败:', error);
  }
}

// 运行测试
testRecordingManager();
