#include "recording_manager_napi.h"
#include "bishare_napi.h"
#include "bishare_logger.h"
#include "bishare_status_codes.h"
#include <string>

namespace OHOS {
    namespace BiShare {
        namespace Interfaces {

            // 静态成员初始化
            napi_ref RecordingManagerNapi::constructor_ = nullptr;
            std::shared_ptr<BiShareRecordManager> RecordingManagerNapi::nativeManager_ = nullptr;

            static constexpr const char* RECORDING_MANAGER_TAG = "RecordingManagerNapi";

            // 构造函数
            RecordingManagerNapi::RecordingManagerNapi() {
                recordManager_ = GetNativeManager();
            }

            // 析构函数
            RecordingManagerNapi::~RecordingManagerNapi() {
                // 清理资源
            }

            // 获取原生管理器实例
            std::shared_ptr<BiShareRecordManager> RecordingManagerNapi::GetNativeManager() {
                if (!nativeManager_) {
                    auto* napiInstance = BiShareNapi::GetInstance();
                    nativeManager_ = napiInstance->GetRecordManager();
                }
                return nativeManager_;
            }

            // 初始化类到NAPI环境
            napi_value RecordingManagerNapi::Init(napi_env env, napi_value exports) {
                BiShareLogger::Info(RECORDING_MANAGER_TAG, "初始化RecordingManager类");

                // 定义类方法
                napi_property_descriptor properties[] = {
                    {"startScreenRecord", nullptr, StartScreenRecord, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"stopScreenRecord", nullptr, StopScreenRecord, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"screenshot", nullptr, Screenshot, nullptr, nullptr, nullptr, napi_default, nullptr},
                };

                // 定义静态方法
                napi_property_descriptor staticProperties[] = {
                    {"getInstance", nullptr, GetInstance, nullptr, nullptr, nullptr, napi_static, nullptr},
                };

                napi_value constructor;
                napi_status status = napi_define_class(
                    env, "RecordingManager", NAPI_AUTO_LENGTH,
                    New, nullptr,
                    sizeof(properties) / sizeof(properties[0]), properties,
                    &constructor);

                if (status != napi_ok) {
                    BiShareLogger::Error(RECORDING_MANAGER_TAG, "定义RecordingManager类失败");
                    return nullptr;
                }

                // 添加静态方法
                napi_define_properties(env, constructor, 
                    sizeof(staticProperties) / sizeof(staticProperties[0]), staticProperties);

                // 创建构造函数引用
                status = napi_create_reference(env, constructor, 1, &constructor_);
                if (status != napi_ok) {
                    BiShareLogger::Error(RECORDING_MANAGER_TAG, "创建构造函数引用失败");
                    return nullptr;
                }

                // 导出类
                status = napi_set_named_property(env, exports, "RecordingManager", constructor);
                if (status != napi_ok) {
                    BiShareLogger::Error(RECORDING_MANAGER_TAG, "导出RecordingManager类失败");
                    return nullptr;
                }

                BiShareLogger::Info(RECORDING_MANAGER_TAG, "RecordingManager类初始化成功");
                return exports;
            }

            // 构造函数
            napi_value RecordingManagerNapi::New(napi_env env, napi_callback_info info) {
                napi_value thisArg;
                napi_get_cb_info(env, info, nullptr, nullptr, &thisArg, nullptr);

                // 创建C++实例
                RecordingManagerNapi* instance = new RecordingManagerNapi();

                // 包装到NAPI对象
                napi_status status = napi_wrap(env, thisArg, instance,
                    [](napi_env env, void* data, void* hint) {
                        delete static_cast<RecordingManagerNapi*>(data);
                    }, nullptr, nullptr);

                if (status != napi_ok) {
                    delete instance;
                    BiShareLogger::Error(RECORDING_MANAGER_TAG, "包装RecordingManager实例失败");
                    return nullptr;
                }

                return thisArg;
            }

            // 获取单例实例
            napi_value RecordingManagerNapi::GetInstance(napi_env env, napi_callback_info info) {
                BiShareLogger::Info(RECORDING_MANAGER_TAG, "获取RecordingManager单例实例");

                if (!constructor_) {
                    BiShareLogger::Error(RECORDING_MANAGER_TAG, "RecordingManager类未初始化");
                    return nullptr;
                }

                napi_value constructor;
                napi_status status = napi_get_reference_value(env, constructor_, &constructor);
                if (status != napi_ok) {
                    BiShareLogger::Error(RECORDING_MANAGER_TAG, "获取构造函数失败");
                    return nullptr;
                }

                napi_value instance;
                status = napi_new_instance(env, constructor, 0, nullptr, &instance);
                if (status != napi_ok) {
                    BiShareLogger::Error(RECORDING_MANAGER_TAG, "创建RecordingManager实例失败");
                    return nullptr;
                }

                return instance;
            }

            // 从this参数中获取实例
            RecordingManagerNapi* RecordingManagerNapi::GetInstanceFromThis(napi_env env, napi_value thisArg) {
                RecordingManagerNapi* instance = nullptr;
                napi_status status = napi_unwrap(env, thisArg, reinterpret_cast<void**>(&instance));
                if (status != napi_ok || !instance) {
                    BiShareLogger::Error(RECORDING_MANAGER_TAG, "获取RecordingManager实例失败");
                    return nullptr;
                }
                return instance;
            }

            // 开始屏幕录制
            napi_value RecordingManagerNapi::StartScreenRecord(napi_env env, napi_callback_info info) {
                BiShareLogger::Info(RECORDING_MANAGER_TAG, "开始屏幕录制");

                size_t argc = 1;
                napi_value args[1];
                napi_value thisArg;
                napi_get_cb_info(env, info, &argc, args, &thisArg, nullptr);

                RecordingManagerNapi* instance = GetInstanceFromThis(env, thisArg);
                if (!instance) {
                    // 返回rejected promise
                    napi_deferred deferred;
                    napi_value promise;
                    napi_create_promise(env, &deferred, &promise);
                    napi_value error;
                    napi_create_string_utf8(env, "Invalid instance", NAPI_AUTO_LENGTH, &error);
                    napi_reject_deferred(env, deferred, error);
                    return promise;
                }

                // 解析参数对象
                if (argc < 1) {
                    BiShareLogger::Error(RECORDING_MANAGER_TAG, "参数不足");
                    napi_deferred deferred;
                    napi_value promise;
                    napi_create_promise(env, &deferred, &promise);
                    napi_value error;
                    napi_create_string_utf8(env, "Insufficient parameters", NAPI_AUTO_LENGTH, &error);
                    napi_reject_deferred(env, deferred, error);
                    return promise;
                }

                // 从options对象中提取参数
                napi_value sessionValue, displayIdValue, directionValue;
                napi_get_named_property(env, args[0], "session", &sessionValue);
                napi_get_named_property(env, args[0], "displayId", &displayIdValue);
                napi_get_named_property(env, args[0], "direction", &directionValue);

                int32_t session, displayId, direction;
                napi_get_value_int32(env, sessionValue, &session);
                napi_get_value_int32(env, displayIdValue, &displayId);
                napi_get_value_int32(env, directionValue, &direction);

                // 调用原生方法
                bstatus_t result = instance->recordManager_->StartScreenRecord(session, displayId, direction);

                // 创建并返回Promise
                napi_deferred deferred;
                napi_value promise;
                napi_create_promise(env, &deferred, &promise);

                napi_value returnValue;
                napi_get_boolean(env, result == BS_OK, &returnValue);

                if (result == BS_OK) {
                    napi_resolve_deferred(env, deferred, returnValue);
                } else {
                    napi_value error;
                    napi_create_string_utf8(env, "Start screen record failed", NAPI_AUTO_LENGTH, &error);
                    napi_reject_deferred(env, deferred, error);
                }

                return promise;
            }

            // 停止屏幕录制
            napi_value RecordingManagerNapi::StopScreenRecord(napi_env env, napi_callback_info info) {
                BiShareLogger::Info(RECORDING_MANAGER_TAG, "停止屏幕录制");

                size_t argc = 1;
                napi_value args[1];
                napi_value thisArg;
                napi_get_cb_info(env, info, &argc, args, &thisArg, nullptr);

                RecordingManagerNapi* instance = GetInstanceFromThis(env, thisArg);
                if (!instance) {
                    napi_deferred deferred;
                    napi_value promise;
                    napi_create_promise(env, &deferred, &promise);
                    napi_value error;
                    napi_create_string_utf8(env, "Invalid instance", NAPI_AUTO_LENGTH, &error);
                    napi_reject_deferred(env, deferred, error);
                    return promise;
                }

                // 解析参数对象
                if (argc < 1) {
                    BiShareLogger::Error(RECORDING_MANAGER_TAG, "参数不足");
                    napi_deferred deferred;
                    napi_value promise;
                    napi_create_promise(env, &deferred, &promise);
                    napi_value error;
                    napi_create_string_utf8(env, "Insufficient parameters", NAPI_AUTO_LENGTH, &error);
                    napi_reject_deferred(env, deferred, error);
                    return promise;
                }

                // 从options对象中提取参数
                napi_value sessionValue, displayIdValue, directionValue;
                napi_get_named_property(env, args[0], "session", &sessionValue);
                napi_get_named_property(env, args[0], "displayId", &displayIdValue);
                napi_get_named_property(env, args[0], "direction", &directionValue);

                int32_t session, displayId, direction;
                napi_get_value_int32(env, sessionValue, &session);
                napi_get_value_int32(env, displayIdValue, &displayId);
                napi_get_value_int32(env, directionValue, &direction);

                // 调用原生方法
                bstatus_t result = instance->recordManager_->StopScreenRecord(session, displayId, direction);

                // 创建并返回Promise
                napi_deferred deferred;
                napi_value promise;
                napi_create_promise(env, &deferred, &promise);

                napi_value returnValue;
                napi_get_boolean(env, result == BS_OK, &returnValue);

                if (result == BS_OK) {
                    napi_resolve_deferred(env, deferred, returnValue);
                } else {
                    napi_value error;
                    napi_create_string_utf8(env, "Stop screen record failed", NAPI_AUTO_LENGTH, &error);
                    napi_reject_deferred(env, deferred, error);
                }

                return promise;
            }

            // 截图
            napi_value RecordingManagerNapi::Screenshot(napi_env env, napi_callback_info info) {
                BiShareLogger::Info(RECORDING_MANAGER_TAG, "截图");

                size_t argc = 1;
                napi_value args[1];
                napi_value thisArg;
                napi_get_cb_info(env, info, &argc, args, &thisArg, nullptr);

                RecordingManagerNapi* instance = GetInstanceFromThis(env, thisArg);
                if (!instance) {
                    napi_deferred deferred;
                    napi_value promise;
                    napi_create_promise(env, &deferred, &promise);
                    napi_value error;
                    napi_create_string_utf8(env, "Invalid instance", NAPI_AUTO_LENGTH, &error);
                    napi_reject_deferred(env, deferred, error);
                    return promise;
                }

                // 解析参数对象
                if (argc < 1) {
                    BiShareLogger::Error(RECORDING_MANAGER_TAG, "参数不足");
                    napi_deferred deferred;
                    napi_value promise;
                    napi_create_promise(env, &deferred, &promise);
                    napi_value error;
                    napi_create_string_utf8(env, "Insufficient parameters", NAPI_AUTO_LENGTH, &error);
                    napi_reject_deferred(env, deferred, error);
                    return promise;
                }

                // 从options对象中提取参数
                napi_value filePathValue, topValue, bottomValue, leftValue, rightValue;
                napi_get_named_property(env, args[0], "filePath", &filePathValue);
                napi_get_named_property(env, args[0], "top", &topValue);
                napi_get_named_property(env, args[0], "bottom", &bottomValue);
                napi_get_named_property(env, args[0], "left", &leftValue);
                napi_get_named_property(env, args[0], "right", &rightValue);

                // 获取字符串参数
                size_t filePathLen;
                napi_get_value_string_utf8(env, filePathValue, nullptr, 0, &filePathLen);
                char* filePath = new char[filePathLen + 1];
                napi_get_value_string_utf8(env, filePathValue, filePath, filePathLen + 1, nullptr);

                int32_t top, bottom, left, right;
                napi_get_value_int32(env, topValue, &top);
                napi_get_value_int32(env, bottomValue, &bottom);
                napi_get_value_int32(env, leftValue, &left);
                napi_get_value_int32(env, rightValue, &right);

                // 调用原生方法
                bstatus_t result = instance->recordManager_->Screenshot(
                    std::string(filePath), top, bottom, left, right);

                delete[] filePath;

                // 创建并返回Promise
                napi_deferred deferred;
                napi_value promise;
                napi_create_promise(env, &deferred, &promise);

                napi_value returnValue;
                napi_get_boolean(env, result == BS_OK, &returnValue);

                if (result == BS_OK) {
                    napi_resolve_deferred(env, deferred, returnValue);
                } else {
                    napi_value error;
                    napi_create_string_utf8(env, "Screenshot failed", NAPI_AUTO_LENGTH, &error);
                    napi_reject_deferred(env, deferred, error);
                }

                return promise;
            }

        } // namespace Interfaces
    } // namespace BiShare
} // namespace OHOS
