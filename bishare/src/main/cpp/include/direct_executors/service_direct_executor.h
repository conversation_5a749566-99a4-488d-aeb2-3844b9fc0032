#ifndef SERVICE_DIRECT_EXECUTOR_H
#define SERVICE_DIRECT_EXECUTOR_H

#include "bishare_operations.h"

namespace OHOS {
    namespace BiShare {

        /**
         * 服务管理直接执行器
         * 负责Initialize和Release等核心服务管理功能
         */
        class ServiceDirectExecutor {
        public:
            // 注册所有服务管理相关的直接执行函数
            static void RegisterAll();

            // 具体的直接执行函数
            static void ExecuteInitialize(napi_env env, AsyncWorkData* workData);
            static void ExecuteRelease(napi_env env, AsyncWorkData* workData);

        private:
            static const char* TAG;
        };

        // 全局注册函数（供bishare_operations.cpp调用）
        void RegisterServiceDirectExecutors();

    } // namespace BiShare
} // namespace OHOS

#endif // SERVICE_DIRECT_EXECUTOR_H
