# RecordingManager 集成指南

## 🎯 目标读者

本指南面向需要集成或维护RecordingManager功能的开发人员，包括：
- OpenHarmony应用开发者
- NAPI模块维护者
- 系统架构师
- 新团队成员

## 🚀 快速开始

### 1. 环境准备

#### 必需工具
- **DevEco Studio**: 4.0+
- **OpenHarmony SDK**: 10+
- **CMake**: 3.16+
- **Ninja**: 1.10+

#### 依赖检查
```bash
# 检查SDK版本
ls ~/Library/OpenHarmony/Sdk/

# 检查构建工具
cmake --version
ninja --version
```

### 2. 项目集成

#### 步骤1: 添加源文件
确保以下文件存在于项目中：
```
bishare/src/main/cpp/interfaces/napi/
├── recording_manager_napi.h
├── recording_manager_napi.cpp
└── bishare_napi_interface.cpp (已修改)
```

#### 步骤2: 更新构建配置
在`bishare/CMakeLists.txt`中添加：
```cmake
# 添加RecordingManager源文件
target_sources(bishare_napi PRIVATE
    src/main/cpp/interfaces/napi/recording_manager_napi.cpp
)

# 确保包含路径正确
target_include_directories(bishare_napi PRIVATE
    src/main/cpp/interfaces/napi
    src/main/cpp/include
)
```

#### 步骤3: 验证编译
```bash
cd /path/to/your/project
./hvigorw --mode module -p module=bishare@default compileNative
```

## 📝 API使用指南

### 1. 基础用法

#### 导入模块
```typescript
import { RecordingManager } from '@ohos/libbishare_napi';
import { RecordingOptions, Direction } from '@ohos/libbishare_napi';
```

#### 获取实例
```typescript
// 单例模式 - 全局唯一实例
const recordingManager = RecordingManager.getInstance();
```

#### 开始录制
```typescript
const options: RecordingOptions = {
    session: 1,
    displayId: 0,
    direction: Direction.SEND
};

try {
    const success = await recordingManager.startScreenRecord(options);
    if (success) {
        console.log('录制开始成功');
    }
} catch (error) {
    console.error('录制开始失败:', error);
}
```

#### 停止录制
```typescript
try {
    const success = await recordingManager.stopScreenRecord(options);
    if (success) {
        console.log('录制停止成功');
    }
} catch (error) {
    console.error('录制停止失败:', error);
}
```

#### 截图功能
```typescript
const screenshotOptions = {
    filePath: '/data/storage/el2/base/haps/screenshot.png',
    top: 0,
    bottom: 1920,
    left: 0,
    right: 1080
};

try {
    const success = await recordingManager.screenshot(screenshotOptions);
    if (success) {
        console.log('截图成功');
    }
} catch (error) {
    console.error('截图失败:', error);
}
```

### 2. 高级用法

#### 错误处理最佳实践
```typescript
class RecordingService {
    private recordingManager: RecordingManager;
    
    constructor() {
        this.recordingManager = RecordingManager.getInstance();
    }
    
    async startRecording(options: RecordingOptions): Promise<boolean> {
        try {
            // 参数验证
            if (!this.validateOptions(options)) {
                throw new Error('Invalid recording options');
            }
            
            // 执行录制
            const result = await this.recordingManager.startScreenRecord(options);
            
            // 结果处理
            if (result) {
                this.onRecordingStarted();
                return true;
            } else {
                this.onRecordingFailed('Start recording returned false');
                return false;
            }
            
        } catch (error) {
            this.onRecordingFailed(`Start recording error: ${error.message}`);
            return false;
        }
    }
    
    private validateOptions(options: RecordingOptions): boolean {
        return options.session >= 0 && 
               options.displayId >= 0 && 
               options.direction !== undefined;
    }
    
    private onRecordingStarted(): void {
        console.log('Recording started successfully');
        // 更新UI状态
        // 启动录制监控
    }
    
    private onRecordingFailed(reason: string): void {
        console.error('Recording failed:', reason);
        // 显示错误提示
        // 重置UI状态
    }
}
```

#### 批量操作
```typescript
class BatchRecordingManager {
    private recordingManager: RecordingManager;
    
    constructor() {
        this.recordingManager = RecordingManager.getInstance();
    }
    
    async batchScreenshot(paths: string[]): Promise<boolean[]> {
        const results: boolean[] = [];
        
        for (const path of paths) {
            try {
                const result = await this.recordingManager.screenshot({
                    filePath: path,
                    top: 0, bottom: 1920, left: 0, right: 1080
                });
                results.push(result);
            } catch (error) {
                console.error(`Screenshot failed for ${path}:`, error);
                results.push(false);
            }
        }
        
        return results;
    }
}
```

## 🔧 故障排除

### 1. 常见编译错误

#### 错误: "RecordingManager not found"
**原因**: 类未正确导出
**解决**: 检查bishare_napi_interface.cpp中是否调用了RecordingManagerNapi::Init

#### 错误: "Promise API参数错误"
**原因**: napi_create_promise参数顺序错误
**解决**: 确保使用正确的参数顺序：`napi_create_promise(env, &deferred, &promise)`

#### 错误: "AsyncExecutor未定义"
**原因**: 缺少async_executor.h包含
**解决**: 在recording_manager_napi.cpp中添加`#include "async_executor.h"`

### 2. 运行时错误

#### 错误: "getInstance返回undefined"
**原因**: 构造函数引用未正确创建
**解决**: 检查napi_create_reference调用是否成功

#### 错误: "异步操作超时"
**原因**: 后台线程阻塞或死锁
**解决**: 检查BiShareRecordManager的实现，确保无阻塞操作

#### 错误: "内存泄漏"
**原因**: 异步数据未正确释放
**解决**: 确保在CompleteCallback中delete异步数据

### 3. 性能问题

#### 问题: 录制延迟高
**原因**: 主线程阻塞
**解决**: 确保使用异步执行，检查是否有同步调用

#### 问题: 内存使用过高
**原因**: 异步数据累积
**解决**: 检查异步数据清理逻辑，避免数据泄漏

## 📊 性能监控

### 1. 关键指标

#### 响应时间监控
```typescript
class PerformanceMonitor {
    async measureRecordingStart(options: RecordingOptions): Promise<number> {
        const startTime = Date.now();
        
        try {
            await RecordingManager.getInstance().startScreenRecord(options);
            const endTime = Date.now();
            const duration = endTime - startTime;
            
            console.log(`Recording start took ${duration}ms`);
            return duration;
        } catch (error) {
            console.error('Recording start failed:', error);
            return -1;
        }
    }
}
```

#### 内存使用监控
```typescript
class MemoryMonitor {
    private initialMemory: number = 0;
    
    startMonitoring(): void {
        // 注意：实际项目中需要使用适当的内存监控API
        this.initialMemory = this.getCurrentMemoryUsage();
        console.log(`Initial memory: ${this.initialMemory}MB`);
    }
    
    checkMemoryLeak(): void {
        const currentMemory = this.getCurrentMemoryUsage();
        const increase = currentMemory - this.initialMemory;
        
        if (increase > 50) { // 50MB阈值
            console.warn(`Potential memory leak detected: +${increase}MB`);
        }
    }
    
    private getCurrentMemoryUsage(): number {
        // 实现内存使用获取逻辑
        return 0;
    }
}
```

## 🧪 测试策略

### 1. 单元测试
```typescript
describe('RecordingManager', () => {
    let recordingManager: RecordingManager;
    
    beforeEach(() => {
        recordingManager = RecordingManager.getInstance();
    });
    
    it('should create singleton instance', () => {
        const instance1 = RecordingManager.getInstance();
        const instance2 = RecordingManager.getInstance();
        expect(instance1).toBe(instance2);
    });
    
    it('should start recording successfully', async () => {
        const options = {
            session: 1,
            displayId: 0,
            direction: Direction.SEND
        };
        
        const result = await recordingManager.startScreenRecord(options);
        expect(result).toBe(true);
    });
});
```

### 2. 集成测试
```typescript
describe('RecordingManager Integration', () => {
    it('should handle complete recording workflow', async () => {
        const manager = RecordingManager.getInstance();
        const options = { session: 1, displayId: 0, direction: Direction.SEND };
        
        // 开始录制
        const startResult = await manager.startScreenRecord(options);
        expect(startResult).toBe(true);
        
        // 等待录制
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 停止录制
        const stopResult = await manager.stopScreenRecord(options);
        expect(stopResult).toBe(true);
    });
});
```

## 📚 参考资料

### 1. 相关文档
- [OpenHarmony NAPI开发指南](https://docs.openharmony.cn/pages/v4.0/zh-cn/application-dev/napi/napi-guidelines.md)
- [AsyncExecutor架构文档](./ASYNC_EXECUTION_FIX.md)
- [BiShare模块架构](./BISHARE_ARCHITECTURE.md)

### 2. 示例代码
- [完整示例项目](../examples/recording_demo)
- [性能测试用例](../tests/performance)
- [集成测试套件](../tests/integration)

---

**指南版本**: 1.0  
**最后更新**: 2025-06-15  
**技术支持**: Augment Agent
