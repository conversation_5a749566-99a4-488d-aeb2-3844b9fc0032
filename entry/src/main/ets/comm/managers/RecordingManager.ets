import {
  startScreenRecord,
  stopScreenRecord,
  screenshot,
  BiShareResult,
  RecordingOptions,
  RecordingInfo,
  RecordingStatus,
  RecordingEventListener,
  ScreenshotOptions,
  Direction
} from '@ohos/libbishare_napi';
import { Log } from '@ohos/lib_info_sender';

const TAG = 'RecordingManager';

/**
 * 录制管理器
 * 负责：
 * 1. 屏幕录制控制
 * 2. 录制状态管理
 * 3. 录制事件处理
 */
export class RecordingManager {
  private currentRecording: RecordingInfo | null = null;
  private eventListeners: Set<RecordingEventListener> = new Set();

  constructor() {
    // 不再需要获取原生管理器实例，直接使用导入的函数
  }

  /**
   * 初始化录制管理器
   */
  async initialize(): Promise<void> {
    Log.showInfo(TAG, '录制管理器初始化');
    this.currentRecording = {
      status: RecordingStatus.IDLE
    };
  }

  /**
   * 释放录制管理器
   */
  async release(): Promise<void> {
    Log.showInfo(TAG, '录制管理器释放');

    // 如果正在录制，先停止
    if (this.currentRecording?.status === RecordingStatus.RECORDING) {
      await this.stopRecording();
    }

    this.currentRecording = null;
    this.eventListeners.clear();
  }

  /**
   * 开始屏幕录制
   */
  async startScreenRecording(options: RecordingOptions): Promise<BiShareResult<boolean>> {
    try {
      if (this.currentRecording?.status === RecordingStatus.RECORDING) {
        Log.showWarn(TAG, '录制已在进行中');
        return { success: true, data: true };
      }

      Log.showInfo(TAG, `开始屏幕录制: session=${options.session}, displayId=${options.displayId}`);

      const result = await startScreenRecord(options.session, options.displayId, options.direction);

      if (result) {
        this.currentRecording = {
          status: RecordingStatus.RECORDING,
          filePath: `recording_${options.session}_${Date.now()}.mp4`,
          startTime: Date.now(),
          duration: 0
        };

        // 通知监听器
        this.notifyRecordingStarted(this.currentRecording);

        Log.showInfo(TAG, '屏幕录制启动成功');
        return { success: true, data: true };
      } else {
        throw new Error('屏幕录制启动失败');
      }
    } catch (error) {
      Log.showError(TAG, '屏幕录制启动失败:', error);
      return {
        success: false,
        error: {
          code: 'START_RECORDING_FAILED',
          message: `屏幕录制启动失败: ${error.message}`
        }
      };
    }
  }

  /**
   * 停止屏幕录制
   */
  async stopRecording(): Promise<BiShareResult<boolean>> {
    try {
      if (this.currentRecording?.status !== RecordingStatus.RECORDING) {
        Log.showWarn(TAG, '当前没有进行录制');
        return { success: true, data: true };
      }

      Log.showInfo(TAG, '停止屏幕录制');

      const stopOptions: RecordingOptions = {
        session: 0,
        displayId: 0,
        direction: Direction.NULL
      };
      const result = await stopScreenRecord(stopOptions.session, stopOptions.displayId, stopOptions.direction);
      
      if (result) {
        // 计算录制时长
        const duration = this.currentRecording.startTime ?
          Date.now() - this.currentRecording.startTime : 0;

        const recordingInfo: RecordingInfo = {
          status: RecordingStatus.STOPPED,
          filePath: this.currentRecording.filePath,
          duration: duration,
          startTime: this.currentRecording.startTime
        };

        this.currentRecording = {
          status: RecordingStatus.IDLE
        };

        // 通知监听器
        this.notifyRecordingStopped(recordingInfo);

        Log.showInfo(TAG, '屏幕录制停止成功');
        return { success: true, data: true };
      } else {
        throw new Error('屏幕录制停止失败');
      }
    } catch (error) {
      Log.showError(TAG, '屏幕录制停止失败:', error);
      return {
        success: false,
        error: {
          code: 'STOP_RECORDING_FAILED',
          message: `屏幕录制停止失败: ${error.message}`
        }
      };
    }
  }

  /**
   * 暂停录制
   */
  async pauseRecording(): Promise<BiShareResult<boolean>> {
    try {
      if (this.currentRecording?.status !== RecordingStatus.RECORDING) {
        Log.showWarn(TAG, '当前没有进行录制');
        return { success: false, error: { code: 'NOT_RECORDING', message: '当前没有进行录制' } };
      }

      // 这里应该调用原生的暂停方法
      // const result = await this.nativeRecordingManager.pauseRecording();

      this.currentRecording.status = RecordingStatus.PAUSED;
      this.notifyRecordingPaused(this.currentRecording);

      Log.showInfo(TAG, '录制暂停成功');
      return { success: true, data: true };
    } catch (error) {
      Log.showError(TAG, '录制暂停失败:', error);
      return {
        success: false,
        error: {
          code: 'PAUSE_RECORDING_FAILED',
          message: `录制暂停失败: ${error.message}`
        }
      };
    }
  }

  /**
   * 恢复录制
   */
  async resumeRecording(): Promise<BiShareResult<boolean>> {
    try {
      if (this.currentRecording?.status !== RecordingStatus.PAUSED) {
        Log.showWarn(TAG, '录制未暂停');
        return { success: false, error: { code: 'NOT_PAUSED', message: '录制未暂停' } };
      }

      // 这里应该调用原生的恢复方法
      // const result = await this.nativeRecordingManager.resumeRecording();

      this.currentRecording.status = RecordingStatus.RECORDING;
      this.notifyRecordingResumed(this.currentRecording);

      Log.showInfo(TAG, '录制恢复成功');
      return { success: true, data: true };
    } catch (error) {
      Log.showError(TAG, '录制恢复失败:', error);
      return {
        success: false,
        error: {
          code: 'RESUME_RECORDING_FAILED',
          message: `录制恢复失败: ${error.message}`
        }
      };
    }
  }

  /**
   * 截图
   */
  async takeScreenshot(filePath: string): Promise<BiShareResult<string>> {
    try {
      Log.showInfo(TAG, `开始截图: ${filePath}`);

      // 直接调用原生函数，使用正确的参数格式
      const result = await screenshot(filePath, 0, 1920, 0, 1080);
      
      if (result) {
        Log.showInfo(TAG, '截图成功');
        return { success: true, data: filePath };
      } else {
        throw new Error('截图失败');
      }
    } catch (error) {
      Log.showError(TAG, '截图失败:', error);
      return {
        success: false,
        error: {
          code: 'SCREENSHOT_FAILED',
          message: `截图失败: ${error.message}`
        }
      };
    }
  }

  /**
   * 获取当前录制信息
   */
  getCurrentRecording(): RecordingInfo | null {
    return this.currentRecording;
  }

  /**
   * 获取录制状态
   */
  getRecordingStatus(): RecordingStatus {
    return this.currentRecording?.status || RecordingStatus.IDLE;
  }

  /**
   * 是否正在录制
   */
  isRecording(): boolean {
    return this.currentRecording?.status === RecordingStatus.RECORDING;
  }

  /**
   * 添加录制事件监听器
   */
  addRecordingEventListener(listener: RecordingEventListener): void {
    this.eventListeners.add(listener);
  }

  /**
   * 移除录制事件监听器
   */
  removeRecordingEventListener(listener: RecordingEventListener): void {
    this.eventListeners.delete(listener);
  }

  /**
   * 通知录制开始
   */
  private notifyRecordingStarted(info: RecordingInfo): void {
    this.eventListeners.forEach(listener => {
      try {
        listener.onRecordingStarted?.(info);
      } catch (error) {
        Log.showError(TAG, '录制开始事件通知失败:', error);
      }
    });
  }

  /**
   * 通知录制停止
   */
  private notifyRecordingStopped(info: RecordingInfo): void {
    this.eventListeners.forEach(listener => {
      try {
        listener.onRecordingStopped?.(info);
      } catch (error) {
        Log.showError(TAG, '录制停止事件通知失败:', error);
      }
    });
  }

  /**
   * 通知录制暂停
   */
  private notifyRecordingPaused(info: RecordingInfo): void {
    this.eventListeners.forEach(listener => {
      try {
        listener.onRecordingPaused?.(info);
      } catch (error) {
        Log.showError(TAG, '录制暂停事件通知失败:', error);
      }
    });
  }

  /**
   * 通知录制恢复
   */
  private notifyRecordingResumed(info: RecordingInfo): void {
    this.eventListeners.forEach(listener => {
      try {
        listener.onRecordingResumed?.(info);
      } catch (error) {
        Log.showError(TAG, '录制恢复事件通知失败:', error);
      }
    });
  }
}
