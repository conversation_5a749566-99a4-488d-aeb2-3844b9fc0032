#include "bishare_manager_napi.h"
#include "bishare_napi.h"
#include "bishare_logger.h"
#include "bishare_status_codes.h"
#include "async_executor.h"
#include <string>

namespace OHOS {
    namespace BiShare {
        namespace Interfaces {

            using namespace Infrastructure;

            // 静态成员初始化
            napi_ref BiShareManagerNapi::constructor_ = nullptr;
            BiShareNapi* BiShareManagerNapi::nativeManager_ = nullptr;

            static constexpr const char* BISHARE_MANAGER_TAG = "BiShareManagerNapi";

            // 异步操作数据结构
            struct BiShareAsyncData {
                BiShareNapi* biShareManager;
                bool isConsole;
                bool isFile;
                std::string filePath;
                int32_t priority;
                std::string name;
                std::string password;
                int32_t eventType;
                bstatus_t result;
                
                BiShareAsyncData() : isConsole(false), isFile(false), priority(0), eventType(0), result(BS_ERROR) {}
            };

            // 构造函数
            BiShareManagerNapi::BiShareManagerNapi() {
                biShareManager_ = GetNativeManager();
            }

            // 析构函数
            BiShareManagerNapi::~BiShareManagerNapi() {
                // 清理资源
            }

            // 获取原生管理器实例
            BiShareNapi* BiShareManagerNapi::GetNativeManager() {
                if (!nativeManager_) {
                    nativeManager_ = BiShareNapi::GetInstance();
                }
                return nativeManager_;
            }

            // 初始化类到NAPI环境
            napi_value BiShareManagerNapi::Init(napi_env env, napi_value exports) {
                BiShareLogger::Info(BISHARE_MANAGER_TAG, "初始化BiShareManager类");

                // 定义类方法
                napi_property_descriptor properties[] = {
                    {"initialize", nullptr, Initialize, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"release", nullptr, Release, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"on", nullptr, On, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"off", nullptr, Off, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"once", nullptr, Once, nullptr, nullptr, nullptr, napi_default, nullptr},
                };

                // 定义静态方法
                napi_property_descriptor staticProperties[] = {
                    {"getInstance", nullptr, GetInstance, nullptr, nullptr, nullptr, napi_static, nullptr},
                };

                napi_value constructor;
                napi_status status = napi_define_class(
                    env, "BiShareManager", NAPI_AUTO_LENGTH,
                    New, nullptr,
                    sizeof(properties) / sizeof(properties[0]), properties,
                    &constructor);

                if (status != napi_ok) {
                    BiShareLogger::Error(BISHARE_MANAGER_TAG, "定义BiShareManager类失败");
                    return nullptr;
                }

                // 添加静态方法
                napi_define_properties(env, constructor, 
                    sizeof(staticProperties) / sizeof(staticProperties[0]), staticProperties);

                // 创建构造函数引用
                status = napi_create_reference(env, constructor, 1, &constructor_);
                if (status != napi_ok) {
                    BiShareLogger::Error(BISHARE_MANAGER_TAG, "创建构造函数引用失败");
                    return nullptr;
                }

                // 导出类
                status = napi_set_named_property(env, exports, "BiShareManager", constructor);
                if (status != napi_ok) {
                    BiShareLogger::Error(BISHARE_MANAGER_TAG, "导出BiShareManager类失败");
                    return nullptr;
                }

                BiShareLogger::Info(BISHARE_MANAGER_TAG, "BiShareManager类初始化成功");
                return exports;
            }

            // 构造函数
            napi_value BiShareManagerNapi::New(napi_env env, napi_callback_info info) {
                napi_value thisArg;
                napi_get_cb_info(env, info, nullptr, nullptr, &thisArg, nullptr);

                // 创建C++实例
                BiShareManagerNapi* instance = new BiShareManagerNapi();

                // 包装到NAPI对象
                napi_status status = napi_wrap(env, thisArg, instance,
                    [](napi_env env, void* data, void* hint) {
                        delete static_cast<BiShareManagerNapi*>(data);
                    }, nullptr, nullptr);

                if (status != napi_ok) {
                    delete instance;
                    BiShareLogger::Error(BISHARE_MANAGER_TAG, "包装BiShareManager实例失败");
                    return nullptr;
                }

                return thisArg;
            }

            // 获取单例实例
            napi_value BiShareManagerNapi::GetInstance(napi_env env, napi_callback_info info) {
                BiShareLogger::Info(BISHARE_MANAGER_TAG, "获取BiShareManager单例实例");

                if (!constructor_) {
                    BiShareLogger::Error(BISHARE_MANAGER_TAG, "BiShareManager类未初始化");
                    return nullptr;
                }

                napi_value constructor;
                napi_status status = napi_get_reference_value(env, constructor_, &constructor);
                if (status != napi_ok) {
                    BiShareLogger::Error(BISHARE_MANAGER_TAG, "获取构造函数失败");
                    return nullptr;
                }

                napi_value instance;
                status = napi_new_instance(env, constructor, 0, nullptr, &instance);
                if (status != napi_ok) {
                    BiShareLogger::Error(BISHARE_MANAGER_TAG, "创建BiShareManager实例失败");
                    return nullptr;
                }

                return instance;
            }

            // 从this参数中获取实例
            BiShareManagerNapi* BiShareManagerNapi::GetInstanceFromThis(napi_env env, napi_value thisArg) {
                BiShareManagerNapi* instance = nullptr;
                napi_status status = napi_unwrap(env, thisArg, reinterpret_cast<void**>(&instance));
                if (status != napi_ok || !instance) {
                    BiShareLogger::Error(BISHARE_MANAGER_TAG, "获取BiShareManager实例失败");
                    return nullptr;
                }
                return instance;
            }

            // 初始化BiShare服务 - 异步执行版本
            napi_value BiShareManagerNapi::Initialize(napi_env env, napi_callback_info info) {
                BiShareLogger::Info(BISHARE_MANAGER_TAG, "初始化BiShare服务 - 异步执行");

                size_t argc = 1;
                napi_value args[1];
                napi_value thisArg;
                napi_get_cb_info(env, info, &argc, args, &thisArg, nullptr);

                BiShareManagerNapi* instance = GetInstanceFromThis(env, thisArg);
                if (!instance || argc < 1) {
                    napi_deferred deferred;
                    napi_value promise;
                    napi_create_promise(env, &deferred, &promise);
                    napi_value error;
                    napi_create_string_utf8(env, "Invalid parameters", NAPI_AUTO_LENGTH, &error);
                    napi_reject_deferred(env, deferred, error);
                    return promise;
                }

                // 创建异步数据
                auto asyncData = new BiShareAsyncData();
                asyncData->biShareManager = instance->biShareManager_;

                // 解析初始化参数对象
                napi_value isConsoleValue, isFileValue, filePathValue, priorityValue;
                napi_get_named_property(env, args[0], "isConsole", &isConsoleValue);
                napi_get_named_property(env, args[0], "isFile", &isFileValue);
                napi_get_named_property(env, args[0], "filePath", &filePathValue);
                napi_get_named_property(env, args[0], "priority", &priorityValue);

                napi_get_value_bool(env, isConsoleValue, &asyncData->isConsole);
                napi_get_value_bool(env, isFileValue, &asyncData->isFile);
                napi_get_value_int32(env, priorityValue, &asyncData->priority);

                size_t filePathLen;
                napi_get_value_string_utf8(env, filePathValue, nullptr, 0, &filePathLen);
                char* filePath = new char[filePathLen + 1];
                napi_get_value_string_utf8(env, filePathValue, filePath, filePathLen + 1, nullptr);
                asyncData->filePath = std::string(filePath);
                delete[] filePath;

                // 使用AsyncExecutor执行异步操作
                return AsyncExecutor::ExecuteAsync(env, "Initialize",
                    // 执行函数 - 在后台线程执行
                    [](AsyncWorkData* workData) {
                        auto data = static_cast<BiShareAsyncData*>(workData->userData);
                        BiShareLogger::Info(BISHARE_MANAGER_TAG, "后台线程执行Initialize");
                        // 调用底层初始化逻辑
                        data->result = BS_OK; // 简化实现
                        BiShareNapi::SetInitialized(true);
                    },
                    // 完成函数 - 在主线程执行
                    [](napi_env env, AsyncWorkData* workData) -> napi_value {
                        auto data = static_cast<BiShareAsyncData*>(workData->userData);
                        napi_value result;
                        napi_get_boolean(env, data->result == BS_OK, &result);
                        delete data; // 清理异步数据
                        return result;
                    },
                    asyncData);
            }

            // 释放BiShare服务 - 异步执行版本
            napi_value BiShareManagerNapi::Release(napi_env env, napi_callback_info info) {
                BiShareLogger::Info(BISHARE_MANAGER_TAG, "释放BiShare服务 - 异步执行");

                napi_value thisArg;
                napi_get_cb_info(env, info, nullptr, nullptr, &thisArg, nullptr);

                BiShareManagerNapi* instance = GetInstanceFromThis(env, thisArg);
                if (!instance) {
                    napi_deferred deferred;
                    napi_value promise;
                    napi_create_promise(env, &deferred, &promise);
                    napi_value error;
                    napi_create_string_utf8(env, "Invalid instance", NAPI_AUTO_LENGTH, &error);
                    napi_reject_deferred(env, deferred, error);
                    return promise;
                }

                // 创建异步数据
                auto asyncData = new BiShareAsyncData();
                asyncData->biShareManager = instance->biShareManager_;

                // 使用AsyncExecutor执行异步操作
                return AsyncExecutor::ExecuteAsync(env, "Release",
                    // 执行函数 - 在后台线程执行
                    [](AsyncWorkData* workData) {
                        auto data = static_cast<BiShareAsyncData*>(workData->userData);
                        BiShareLogger::Info(BISHARE_MANAGER_TAG, "后台线程执行Release");
                        // 调用底层释放逻辑
                        data->result = BS_OK; // 简化实现
                        BiShareNapi::SetInitialized(false);
                    },
                    // 完成函数 - 在主线程执行
                    [](napi_env env, AsyncWorkData* workData) -> napi_value {
                        auto data = static_cast<BiShareAsyncData*>(workData->userData);
                        napi_value result;
                        napi_get_boolean(env, data->result == BS_OK, &result);
                        delete data; // 清理异步数据
                        return result;
                    },
                    asyncData);
            }

            // 其他方法的简化实现
            napi_value BiShareManagerNapi::On(napi_env env, napi_callback_info info) {
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            }

            napi_value BiShareManagerNapi::Off(napi_env env, napi_callback_info info) {
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            }

            napi_value BiShareManagerNapi::Once(napi_env env, napi_callback_info info) {
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            }

        } // namespace Interfaces
    } // namespace BiShare
} // namespace OHOS
